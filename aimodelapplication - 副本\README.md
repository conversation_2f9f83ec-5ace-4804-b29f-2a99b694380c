
## 项目介绍
    基于fastapi项目结构，使用langchain进行llm上下文管理以及推理流程编排。

## 项目结构

```bash
.
├── main.py                  # 应用入口
├── config.py                # 全局配置管理
├── logger.py                # 日志配置
├── api/                     # 接口路由
│   ├── v1/                  # 接口路由》API版本目录
├── db/                      # 数据库连接
├── logs/                    # 日志存储目录
├── models/                  # ORM模型
├── services/                # 业务逻辑层
│   ├── dify/                # 业务逻辑层》dify逻辑层
├── template/                # 模板文件层
├── tests/                   # 测试用例
├── utils/                   # 工具类
└──  ├── requester/           # 工具类》请求工具