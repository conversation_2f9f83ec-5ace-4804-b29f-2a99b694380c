2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | INFO | 成功查询知识库 67e1f68d-d0ec-4cf3-9b90-e58e0622a2de 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0(2).json']
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-07-11 13:46:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
