import os
import aiomysql
from typing import Dict, List, Any, Optional, Union, Tuple
from contextlib import asynccontextmanager
from dotenv import load_dotenv
from logger import logging
import asyncio

# 加载环境变量
load_dotenv()

class MySQLPool:
    """MySQL连接池管理类（单例模式）"""
    _instance = None
    _pools = {}  # 存储多个数据库连接池
    _logger = None
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(MySQLPool, cls).__new__(cls)
            # 初始化日志
            cls._instance._logger = logging()
        return cls._instance
    
    async def initialize(self, 
                        db_name: str = None,
                        host: str = None, 
                        port: int = None, 
                        user: str = None, 
                        password: str = None, 
                        pool_size: int = 10,
                        **kwargs) -> None:
        """初始化连接池
        
        Args:
            db_name: 数据库名称，默认使用环境变量中的MYSQL_DB_AI
            host: 数据库主机，默认使用环境变量中的MYSQL_HOST
            port: 数据库端口，默认使用环境变量中的MYSQL_PORT
            user: 数据库用户名，默认使用环境变量中的MYSQL_USER
            password: 数据库密码，默认使用环境变量中的MYSQL_PASSWORD
            pool_size: 连接池大小，默认为10
        """
        # 使用环境变量或默认值
        db = db_name or os.getenv('MYSQL_DB_AI', '')
        host = host or os.getenv('MYSQL_HOST', 'localhost')
        port = port or int(os.getenv('MYSQL_PORT', '3306'))
        user = user or os.getenv('MYSQL_USER', 'root')
        password = password or os.getenv('MYSQL_PASSWORD', '')
        
        # 检查必要的配置
        if not host or not user or not db:
            missing = []
            if not host: missing.append('host')
            if not user: missing.append('user')
            if not db: missing.append('db')
            self._logger.error(f"缺少MySQL配置: {', '.join(missing)}")
            raise ValueError(f"缺少MySQL配置: {', '.join(missing)}")
        
        # 创建连接池唯一标识
        pool_key = f"{host}:{port}/{db}"
        
        # 如果连接池已存在且已初始化，直接返回
        if pool_key in self._pools and self._pools[pool_key]["initialized"]:
            return
        
        try:
            # 创建新连接池
            pool = await aiomysql.create_pool(
                host=host,
                port=port,
                user=user,
                password=password,
                db=db,
                maxsize=pool_size,
                autocommit=True,
                **kwargs
            )
            
            # 保存连接池信息
            self._pools[pool_key] = {
                "pool": pool,
                "initialized": True,
                "config": {
                    "host": host,
                    "port": port,
                    "user": user,
                    "db": db,
                    "pool_size": pool_size
                }
            }
        except Exception as e:
            self._logger.error(f"MySQL连接池初始化失败: {str(e)}")
            # 标记初始化失败
            if pool_key in self._pools:
                self._pools[pool_key]["initialized"] = False
            raise
    
    async def close(self, db_name: str = None) -> None:
        """关闭连接池
        
        Args:
            db_name: 指定要关闭的数据库连接池，None表示关闭所有连接池
        """
        if db_name:
            # 关闭指定数据库的连接池
            for key, pool_info in list(self._pools.items()):
                if key.endswith(f"/{db_name}") and pool_info["initialized"]:
                    pool_info["pool"].close()
                    await pool_info["pool"].wait_closed()
                    pool_info["initialized"] = False
        else:
            # 关闭所有连接池
            for key, pool_info in list(self._pools.items()):
                if pool_info["initialized"]:
                    pool_info["pool"].close()
                    await pool_info["pool"].wait_closed()
                    pool_info["initialized"] = False
    
    async def _get_pool(self, db_name: str = None) -> aiomysql.Pool:
        """获取指定数据库的连接池，如果不存在则初始化
        
        Args:
            db_name: 数据库名称，None表示使用默认数据库
            
        Returns:
            连接池对象
        """
        db = db_name or os.getenv('MYSQL_DB_AI', '')
        
        # 查找匹配的连接池
        for key, pool_info in self._pools.items():
            if key.endswith(f"/{db}") and pool_info["initialized"]:
                # 检查连接池是否有效
                try:
                    async with pool_info["pool"].acquire() as conn:
                        await conn.ping()
                    return pool_info["pool"]
                except Exception as e:
                    self._logger.warning(f"连接池 {key} 无效，尝试重新初始化: {str(e)}")
                    # 标记为未初始化，等待重新初始化
                    pool_info["initialized"] = False
                    break
        
        # 没有找到有效的连接池，初始化一个新的
        await self.initialize(db_name=db)
        
        # 再次查找
        for key, pool_info in self._pools.items():
            if key.endswith(f"/{db}") and pool_info["initialized"]:
                return pool_info["pool"]
        
        # 如果仍然找不到，抛出异常
        raise ValueError(f"无法创建数据库 '{db}' 的连接池")
    
    @asynccontextmanager
    async def connection(self, db_name: str = None) -> aiomysql.Connection:
        """获取数据库连接
        
        Args:
            db_name: 数据库名称，None表示使用默认数据库
            
        Yields:
            数据库连接对象
        """
        pool = await self._get_pool(db_name)
        conn = await pool.acquire()
        try:
            yield conn
        finally:
            pool.release(conn)
    
    @asynccontextmanager
    async def cursor(self, db_name: str = None) -> aiomysql.Cursor:
        """获取游标
        
        Args:
            db_name: 数据库名称，None表示使用默认数据库
            
        Yields:
            数据库游标对象
        """
        async with self.connection(db_name) as conn:
            cursor = await conn.cursor(aiomysql.DictCursor)
            try:
                yield cursor
            finally:
                await cursor.close()
    
    async def execute(self, sql: str, params: Union[tuple, None] = None, db_name: str = None) -> int:
        """执行SQL语句并返回影响行数
        
        Args:
            sql: SQL语句
            params: SQL参数
            db_name: 数据库名称，None表示使用默认数据库
            
        Returns:
            影响的行数
        """
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                async with self.cursor(db_name) as cursor:
                    await cursor.execute(sql, params)
                    return cursor.rowcount
            except Exception as e:
                retry_count += 1
                if retry_count == max_retries:
                    self._logger.error(f"SQL执行错误: {sql}, 参数: {params}, 错误: {str(e)}")
                    raise
                else:
                    self._logger.warning(f"SQL执行失败，尝试重试 ({retry_count}/{max_retries}): {str(e)}")
                    # 等待一段时间后重试
                    await asyncio.sleep(1)
    
    async def fetch_one(self, sql: str, params: Union[tuple, None] = None, db_name: str = None) -> Optional[Dict[str, Any]]:
        """执行查询并返回一条记录
        
        Args:
            sql: SQL语句
            params: SQL参数
            db_name: 数据库名称，None表示使用默认数据库
            
        Returns:
            查询结果的一条记录，未找到时返回None
        """
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                async with self.cursor(db_name) as cursor:
                    await cursor.execute(sql, params)
                    return await cursor.fetchone()
            except Exception as e:
                retry_count += 1
                if retry_count == max_retries:
                    self._logger.error(f"SQL查询错误: {sql}, 参数: {params}, 错误: {str(e)}")
                    raise
                else:
                    self._logger.warning(f"SQL查询失败，尝试重试 ({retry_count}/{max_retries}): {str(e)}")
                    # 等待一段时间后重试
                    await asyncio.sleep(1)
    
    async def fetch_all(self, sql: str, params: Union[tuple, None] = None, db_name: str = None) -> List[Dict[str, Any]]:
        """执行查询并返回所有记录
        
        Args:
            sql: SQL语句
            params: SQL参数
            db_name: 数据库名称，None表示使用默认数据库
            
        Returns:
            查询结果的所有记录
        """
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                async with self.cursor(db_name) as cursor:
                    await cursor.execute(sql, params)
                    return await cursor.fetchall()
            except Exception as e:
                retry_count += 1
                if retry_count == max_retries:
                    self._logger.error(f"SQL查询错误: {sql}, 参数: {params}, 错误: {str(e)}")
                    raise
                else:
                    self._logger.warning(f"SQL查询失败，尝试重试 ({retry_count}/{max_retries}): {str(e)}")
                    # 等待一段时间后重试
                    await asyncio.sleep(1)
    
    async def execute_many(self, sql: str, params_list: List[Tuple], db_name: str = None) -> int:
        """批量执行SQL语句
        
        Args:
            sql: SQL语句
            params_list: SQL参数列表
            db_name: 数据库名称，None表示使用默认数据库
            
        Returns:
            影响的行数
        """
        async with self.cursor(db_name) as cursor:
            try:
                await cursor.executemany(sql, params_list)
                return cursor.rowcount
            except Exception as e:
                self._logger.error(f"批量SQL执行错误: {sql}, 参数数量: {len(params_list)}, 错误: {str(e)}")
                raise
    
    async def transaction(self, db_name: str = None) -> aiomysql.Connection:
        """启动事务
        
        Args:
            db_name: 数据库名称，None表示使用默认数据库
            
        Returns:
            数据库连接对象
        """
        pool = await self._get_pool(db_name)
        conn = await pool.acquire()
        try:
            await conn.begin()
            return conn
        except Exception as e:
            pool.release(conn)
            self._logger.error(f"启动事务失败: {str(e)}")
            raise
    
    def get_pool_info(self) -> Dict[str, Dict]:
        """获取所有连接池信息
        
        Returns:
            连接池信息字典
        """
        result = {}
        for key, pool_info in self._pools.items():
            result[key] = {
                "initialized": pool_info["initialized"],
                "config": pool_info["config"].copy()
            }
        return result
