
from fastapi import APIRouter
from fastapi.responses import JSONResponse

from template.response_temp import BaseResponse

router = APIRouter()

@router.get("/", response_model=BaseResponse)
async def test():
    return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message=f"测试连接成功",
                data=[]
            ).dict()
        )