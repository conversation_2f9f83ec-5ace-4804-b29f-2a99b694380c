#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试提示词管理API接口
"""

import requests
import json

# API基础URL
BASE_URL = "http://10.113.7.86:8081/ai/v1/prompt_management"

def test_load_api():
    """测试加载接口"""
    print("测试加载接口...")
    url = f"{BASE_URL}/load"
    params = {"knowledge_base_id": "default"}
    
    try:
        response = requests.get(url, params=params)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print("加载成功!")
            print(f"数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print("加载失败!")
            
    except Exception as e:
        print(f"请求失败: {str(e)}")

def test_save_api():
    """测试保存接口"""
    print("\n测试保存接口...")
    url = f"{BASE_URL}/save"
    params = {"knowledge_base_id": "test_api_001"}
    
    # 测试数据
    data = {
        "businessIntroduction": "当前系统为堡垒机系统",
        "termDefinitions": [
            {"name": "资产", "definition": "本地添加的资产，包括主机数据库等"},
            {"name": "账号", "definition": "创建资产时同步创建或者绑定资产创建"},
            {"name": "标签", "definition": "分为资产标签和账号标签"}
        ],
        "businessPrompts": [
            {"content": "授权是对用户进行（资产，资产账号等）授权"},
            {"content": "账号是关联在资产下，创建资产可以同时创建账号，也可以创建资产后再创建账号"}
        ],
        "extraEvidence": [
            {"name": "接口类型", "description": "接口类型为创建类型接口"}
        ]
    }
    
    try:
        response = requests.post(url, params=params, json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("保存成功!")
            print(f"结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print("保存失败!")
            
    except Exception as e:
        print(f"请求失败: {str(e)}")

def test_load_saved_data():
    """测试加载刚保存的数据"""
    print("\n测试加载刚保存的数据...")
    url = f"{BASE_URL}/load"
    params = {"knowledge_base_id": "test_api_001"}
    
    try:
        response = requests.get(url, params=params)
        print(f"状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print("加载成功!")
            print(f"数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print("加载失败!")
            
    except Exception as e:
        print(f"请求失败: {str(e)}")

if __name__ == "__main__":
    print("开始测试提示词管理API...")
    
    # 测试加载默认配置
    test_load_api()
    
    # 测试保存配置
    test_save_api()
    
    # 测试加载刚保存的配置
    test_load_saved_data()
    
    print("\n测试完成")
