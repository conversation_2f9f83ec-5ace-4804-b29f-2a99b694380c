# utf-8
import json

class ExtractSwaggerEndpoints:

    def __init__(self, filepath):
        self.filepath = filepath

    def extract_swagger_endpoints(self):
        with open(self.filepath, 'r', encoding='utf-8') as f:
            swagger_data = json.load(f)

        _endpoint = []
        for path, methods in swagger_data.get('paths', {}).items():
            for method, details in methods.items():
                # 提取 summary 和 parameters
                summary = details.get('summary', '')
                # 提取 parameters 中的 name 字段
                parameters = details.get('parameters', [])
                parameter_names = [param.get('name') for param in parameters]

                # 提取 response 下 200 响应中的 schema.properties.data.required
                response_200 = details.get('responses', {}).get('200', {})
                schema = response_200.get('schema', {})
                properties = schema.get('properties', {})
                required_fields = properties.get('data', {}).get('required', [])

                _endpoint.append({
                    "url": path,
                    "method": method.upper(),
                    "summary": summary,
                    "parameters": parameter_names,
                    "response": required_fields
                })

        return _endpoint