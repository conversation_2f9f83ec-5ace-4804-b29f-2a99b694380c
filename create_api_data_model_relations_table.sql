-- 创建API数据模型关系表
CREATE TABLE `api_data_model_relations` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `knowledge_base_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '知识库ID',
  `upstream_api` json DEFAULT NULL COMMENT '上游API信息（JSON格式）',
  `data_model` json DEFAULT NULL COMMENT '数据模型信息（JSON格式）',
  `downstream_api` json DEFAULT NULL COMMENT '下游API信息（JSON格式）',
  `relation_type` varchar(10) DEFAULT NULL COMMENT '关系类型：QUTO或BY',
  `relation_description` text DEFAULT NULL COMMENT '关系描述，说明关系产生的原因',
  `status` int NOT NULL DEFAULT '0' COMMENT '关系状态：0待确认，1已嵌入，2已忽略',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_knowledge_base_id` (`knowledge_base_id`),
  KEY `idx_status` (`status`),
  KEY `idx_relation_type` (`relation_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='API数据模型关系表';

-- 数据模型字段说明：
-- upstream_api: 上游API信息，包含API的详细信息
-- data_model: 数据模型信息，格式如下：
-- {
--   "name": "自定义名称（按规范生成）",
--   "type": "data_model",
--   "relation": {
--     "first_api.某个字段": "second_api.某个字段"
--   },
--   "description": "描述信息"
-- }
-- downstream_api: 下游API信息，包含API的详细信息

-- API与数据模型的关系类型：
-- QUTO: API的response产出数据模型
-- BY: 数据模型作为API入参
