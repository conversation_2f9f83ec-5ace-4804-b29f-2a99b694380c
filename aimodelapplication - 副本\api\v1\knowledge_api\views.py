from fastapi import APIRouter, Body, Query
from dotenv import load_dotenv
from fastapi.responses import JSONResponse
import uuid
from datetime import datetime
from logger import logging

from db.mysql.models.knowledge_dify_mapping import KnowledgeDifyMappingModel
from service.dify.dify import Dify, DifyAdmin
from template.response_temp import BaseResponse
from db.mysql.models.knowledge_base import KnowledgeBaseModel
from typing import Dict, Any
from db.mysql.models.map_info import MapInfoModel
from db.neo.models.neo4j_map import Neo4jModel
from db.mysql.models.map_info_history import MapInfoHistoryModel
from db.mysql.models.recall_test_history import RecallTestHistoryModel

# 加载环境变量
load_dotenv()

router = APIRouter()
logger = logging()
dify_mapping = KnowledgeDifyMappingModel()

@router.post("/create_knowledge", response_model=BaseResponse)
async def create_knowledge_base(data: Dict[str, Any] = Body(...)):
    """
    创建知识库接口
    
    Args:
        name: 知识库名称
        description: 知识库描述
        permission: 权限
        category: 知识库类别
        neo4j_host: 主机ip
        neo4j_port: 端口
        neo4j_user: 用户
        neo4j_password: 密码
        neo4j_db: 数据库名
        neo4j_tags: 标签
    Returns:
        BaseResponse: 响应结果
        :param data:
    """
    # 获取请求参数
    username = data.get("username")
    user_id = data.get("user_id")
    name = data.get("name")
    description = data.get("description", "")
    permission = data.get("permission", "only_me")
    category = data.get("category")
    neo4j_host = data.get("neo4j_host", "")
    neo4j_port = data.get("neo4j_port", 7687)
    neo4j_user = data.get("neo4j_user", "neo4j")
    neo4j_password = data.get("neo4j_password", "")
    neo4j_db = data.get("neo4j_db", "neo4j")
    neo4j_tags = data.get("neo4j_tags", "")

    # 创建知识库模型实例
    kb_model = KnowledgeBaseModel()
    
    try:
        # 判断知识库类别
        if category == "C":
            # 构建知识库数据
            data = {
                "id": str(uuid.uuid4()),  # 生成唯一ID
                "name": name,
                "description": description,
                "permission": permission,
                "created_by": username,  # 使用传入的用户名
                "created_at": int(datetime.now().timestamp()),  # 使用时间戳格式
                "document_count": 0,
                "word_count": 0,
                "host": neo4j_host,
                "port": neo4j_port,
                "user": neo4j_user,
                "password": neo4j_password,
                "db": neo4j_db,
                "tags": neo4j_tags
            }
            
            # 插入知识库记录
            result = await kb_model.insert(data)
            
            if result:
                try:
                    # 创建MapInfoModel实例
                    map_model = MapInfoModel()
                    await map_model.initialize()
                    
                    try:
                        # 更新map_info表
                        map_result = await map_model.create_map_info(
                            uid=data["id"],  # 使用知识库ID作为uid
                            kb_name=name,
                            version="V1.0",
                            version_desc=description,
                            kb_type="API接口图谱",
                            entity_count=0,
                            relation_count=0
                        )
                        
                        if map_result:
                            return JSONResponse(
                                status_code=200,
                                content=BaseResponse(
                                    code=200,
                                    message="图谱型知识库创建成功",
                                    data={"id": data["id"]}
                                ).dict()
                            )
                        else:
                            # 如果map_info创建失败，回滚知识库创建
                            await kb_model.delete(data["id"])
                            return JSONResponse(
                                status_code=500,
                                content=BaseResponse(
                                    code=500,
                                    message="图谱型知识库元信息创建失败",
                                    data={}
                                ).dict()
                            )
                            
                    finally:
                        await map_model.close()
                        
                except Exception as e:
                    # 如果map_info创建过程中发生异常，回滚知识库创建
                    await kb_model.delete(data["id"])
                    logger.error(f"创建图谱型知识库元信息失败: {str(e)}")
                    return JSONResponse(
                        status_code=500,
                        content=BaseResponse(
                            code=500,
                            message=f"创建图谱型知识库元信息失败: {str(e)}",
                            data={}
                        ).dict()
                    )
            else:
                return JSONResponse(
                    status_code=500,
                    content=BaseResponse(
                        code=500,
                        message="图谱型知识库创建失败",
                        data={}
                    ).dict()
                )
        else:
            records = await dify_mapping.find_by_userid_and_username(user_id, username)
            # 重新构建Dify实例
            dify = Dify(email=records[0].get("dify_email"), password=records[0].get("dify_password"))

            if category == 'A':
                response = await dify.create_knowledge_base(name)
            else:
                name = f'【CODE】{name}'
                response = await dify.create_knowledge_base(name)
            if isinstance(response, BaseResponse):
                response = response.dict()

            if response.get("code") == 200:
                return JSONResponse(
                    status_code=200,
                    content=response
                )
            else:
                return JSONResponse(
                    status_code=500,
                    content=response
                )
            
    except Exception as e:
        # 记录错误并返回错误响应
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"创建失败: {str(e)}",
                data={}
            ).dict()
        )
        
    finally:
        # 确保关闭数据库连接
        if 'kb_model' in locals():
            await kb_model.close()

@router.put("/update_knowledge", response_model=BaseResponse)
async def update_knowledge_base(data: Dict[str, Any] = Body(...)):
    """
    更新知识库接口
    Args:
        data:{}字典格式数据，必传参数：uid（知识库的id），user_id（用户id），username（用户名）和category（知识库类型）

    Returns:
        BaseResponse: 响应结果
        :param data:
    """
    try:
        # 获取必要参数
        category = data.get("category")
        username = data.get("username")
        user_id = data.get("user_id")
        uid = data.get("uid")
        
        if not uid:
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="缺少必要参数",
                    data={}
                ).dict()
            )
            
        # 准备需要更新的数据
        edit_data = {
            "name": '【CODE】' + data.get("name") if category == 'B' else data.get("name"),
            "description": data.get("description", ""),
            "permission": data.get("permission", "only_me"),
        }
        
        # 根据不同类型处理
        if category == "C":
            # 对于C类型，使用本地KnowledgeBaseModel更新
            kb_model = KnowledgeBaseModel()
            try:
                
                update_result = await kb_model.update(uid, edit_data)
                
                if update_result:
                    logger.info(f"用户{username} 执行更新知识库操作： 【{uid}】.")
                    return JSONResponse(
                        status_code=200,
                        content=BaseResponse(
                            code=200,
                            message="知识库更新成功",
                            data={}
                        ).dict()
                    )
                else:
                    return JSONResponse(
                        status_code=404,
                        content=BaseResponse(
                            code=404,
                            message="知识库不存在或更新失败",
                            data={}
                        ).dict()
                    )
            except Exception as e:
                logger.error(f"C类型知识库更新失败: {str(e)}")
                return JSONResponse(
                    status_code=500,
                    content=BaseResponse(
                        code=500,
                        message=f"知识库更新失败: {str(e)}",
                        data={}
                    ).dict()
                )
            finally:
                # 确保关闭数据库连接
                await kb_model.close()
        else:
            # 对于非C类型，调用Dify的编辑知识库函数
            try:
                records = await dify_mapping.find_by_userid_and_username(user_id, username)
                if not records:
                    return JSONResponse(
                        status_code=404,
                        content=BaseResponse(
                            code=404,
                            message="未找到用户Dify账号信息",
                            data={}
                        ).dict()
                    )
                
                # 构建Dify实例
                dify = Dify(email=records[0].get("dify_email"), password=records[0].get("dify_password"))
                edit_data["uid"] = uid
                edit_response = await dify.edit_knowledge_base(edit_data)
                
                # 确保edit_response是可JSON序列化的
                if isinstance(edit_response, BaseResponse):
                    logger.info(f"用户{username} 执行更新Dify知识库操作： 【{uid}】.")
                    edit_response = edit_response.dict()
                
                return JSONResponse(
                    status_code=200,
                    content=edit_response
                )
            except Exception as e:
                logger.error(f"编辑知识库失败: {str(e)}")
                return JSONResponse(
                    status_code=500,
                    content=BaseResponse(
                        code=500,
                        message=f"编辑知识库失败: {str(e)}",
                        data={}
                    ).dict()
                )
    except Exception as e:
        logger.error(f"处理知识库更新请求失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"处理知识库更新请求失败: {str(e)}",
                data={}
            ).dict()
        )

@router.delete("/delete_knowledge", response_model=BaseResponse)
async def delete_knowledge_base(data: Dict[str, Any] = Body(...)):
    """
    删除知识库接口
    Args:
        data:{}字典格式数据，必传参数：uid（知识库的id），user_id（用户id），username（用户名）和category（知识库类型）
    Returns:
        BaseResponse: 响应结果
        :param data:
    """
    uid = data.get("uid")
    username = data.get("username")
    category = data.get("category")
    user_id = data.get("user_id")

    if category == "C":
        try:
            # 1. 先删除 Neo4j 中的相关数据
            neo4j_model = Neo4jModel([uid])
            await neo4j_model.initialize()
            deleted_count = await neo4j_model.delete_all_by_label()
            await neo4j_model.close()
            
            logger.info(f"已删除 Neo4j 中的 {deleted_count} 个节点及其关系")
            
            # 2. 删除 MySQL 中的记录
            kb_model = KnowledgeBaseModel()
            await kb_model.initialize()
            
            # 初始化其他模型
            map_info_model = MapInfoModel()
            map_info_history_model = MapInfoHistoryModel()
            recall_test_history_model = RecallTestHistoryModel()
            
            try:
                # 初始化连接
                await map_info_model.initialize()
                await map_info_history_model.initialize()
                await recall_test_history_model.initialize()
                
                # 使用kb_model的db_pool执行所有删除操作
                # 删除map_info表中的数据
                await kb_model.db_pool.execute(
                    "DELETE FROM map_info WHERE uid = %s",
                    (uid,)
                )
                
                # 删除map_info_history表中的数据
                await kb_model.db_pool.execute(
                    "DELETE FROM map_info_history WHERE uid = %s",
                    (uid,)
                )
                
                # 删除recall_test_history表中的数据
                await kb_model.db_pool.execute(
                    "DELETE FROM recall_test_history WHERE uid = %s",
                    (uid,)
                )
                
                # 删除knowledge_base表中的数据
                await kb_model.db_pool.execute(
                    "DELETE FROM knowledge_base WHERE id = %s",
                    (uid,)
                )
                
                # 删除map_doc表中的数据
                await kb_model.db_pool.execute(
                    "DELETE FROM map_doc WHERE knowledge_base_uid = %s",
                    (uid,)
                )
                
                logger.info(f"用户{username} 执行删除知识库操作： 【{uid}】.")
                return JSONResponse(
                    status_code=200,
                    content=BaseResponse(
                        code=200,
                        message="知识库删除成功",
                        data={"deleted_nodes": deleted_count}
                    ).dict()
                )
                
            except Exception as e:
                logger.error(f"删除知识库时发生错误: {str(e)}")
                return JSONResponse(
                    status_code=500,
                    content=BaseResponse(
                        code=500,
                        message=f"删除知识库时发生错误: {str(e)}",
                        data={}
                    ).dict()
                )
                
            finally:
                # 关闭连接
                await kb_model.close()
                await map_info_model.close()
                await map_info_history_model.close()
                await recall_test_history_model.close()
                
        except Exception as e:
            logger.error(f"删除知识库时发生错误: {str(e)}")
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message=f"删除知识库时发生错误: {str(e)}",
                    data={}
                ).dict()
            )
    else:
        # 使用 Dify 的 delete_knowledge_base 方法删除知识库
        try:
            # 初始化Dify映射模型
            dify_mapping = KnowledgeDifyMappingModel()
            await dify_mapping.initialize()
            
            try:
                # 查询Dify账号信息
                records = await dify_mapping.find_by_userid_and_username(user_id, username)
                if not records:
                    return JSONResponse(
                        status_code=404,
                        content=BaseResponse(
                            code=404,
                            message="未找到用户Dify账号信息",
                            data={}
                        ).dict()
                    )
                    
                # 构建Dify实例并删除知识库
                dify = Dify(email=records[0].get("dify_email"), password=records[0].get("dify_password"))
                response = await dify.delete_knowledge_base(uid)
                
                if response.code == 200:
                    # 删除成功
                    return JSONResponse(
                        status_code=200,
                        content=BaseResponse(
                            code=200,
                            message=response.message,
                            data=response.data
                        ).dict()
                    )
                else:
                    # 删除失败
                    return JSONResponse(
                        status_code=500,
                        content=BaseResponse(
                            code=500,
                            message=response.message,
                            data=response.data
                        ).dict()
                    )
                    
            finally:
                # 确保关闭连接
                await dify_mapping.close()
                
        except Exception as e:
            logger.error(f"删除Dify知识库时发生错误: {str(e)}")
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message=f"删除Dify知识库时发生错误: {str(e)}",
                    data={}
                ).dict()
            )

@router.delete("/delete_knowledge_doc", response_model=BaseResponse)
async def delete_knowledge_doc(
    uid: str = Query(..., description="知识库ID"),
    username: str = Query(..., description="用户名"),
    user_id: str = Query(..., description="用户ID")
):
    """
    删除知识库文档分片
    
    Args:
        uid: 知识库ID
        username: 用户名
        user_id: 用户ID
        
    Returns:
        BaseResponse: 响应对象
    """
    try:
        # 查询数据库获取dify_email和dify_password
        knowledge_dify_mapping = KnowledgeDifyMappingModel()
        try:
            await knowledge_dify_mapping.initialize()
            record = await knowledge_dify_mapping.find_by_userid_and_username(user_id, username)

            if not record:
                return JSONResponse(
                    status_code=404,
                    content=BaseResponse(
                        code=404,
                        message="未找到用户对应的Dify账号信息",
                        data={}
                    ).dict()
                )

            dify_email = record[0].get("dify_email")
            dify_password = record[0].get("dify_password")

        except Exception as e:
            logger.error(f"查询Dify账号信息失败: {str(e)}")
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message="查询Dify账号信息失败",
                    data={}
                ).dict()
            )
        finally:
            await knowledge_dify_mapping.close()

        # 创建Dify实例并删除文档分片
        dify = Dify(email=dify_email, password=dify_password)
        status_code, response_data = await dify.delete_knowledge_docs(uid)

        if status_code == 200:
            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message="删除知识库文档成功",
                    data=response_data if response_data else {}
                ).dict()
            )
        else:
            error_message = "删除知识库文档失败2"
            if isinstance(response_data, dict) and "error" in response_data:
                error_message = f"删除知识库文档失败2: {response_data['error']}"
            elif isinstance(response_data, str):
                error_message = f"删除知识库文档失败3: {response_data}"
            
            return JSONResponse(
                status_code=status_code,
                content=BaseResponse(
                    code=status_code,
                    message=error_message,
                    data=response_data if response_data else {}
                ).dict()
            )

    except Exception as e:
        logger.error(f"删除知识库文档失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"删除知识库文档失败: {str(e)}",
                data={}
            ).dict()
        )
