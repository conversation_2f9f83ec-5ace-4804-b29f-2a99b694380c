from typing import Dict, List, Any, Optional

from db.mysql.mysql_pool import MySQLPool
from logger import logging
import json


class ApiDataModelRelationsModel:
    """API数据模型关系模型类 - 处理 api_data_model_relations 表"""

    def __init__(self):
        self.table = "api_data_model_relations"
        self.primary_key = "id"
        self.logger = logging()
        self.db_pool = MySQLPool()

    async def initialize(self):
        """初始化连接池"""
        await self.db_pool.initialize()

    async def close(self):
        """关闭连接池，释放资源"""
        await self.db_pool.close()

    async def create_relation(
            self,
            knowledge_base_id: str,
            upstream_api: Optional[str] = None,
            data_model: Optional[Dict] = None,
            downstream_api: Optional[str] = None,
            relation_type: Optional[str] = None,
            relation_description: Optional[str] = None,
            status: int = 0
    ):
        """
        创建API数据模型关系记录
        
        Args:
            knowledge_base_id: 知识库ID
            upstream_api: 上游API（JSON格式字符串）
            data_model: 数据模型（Dict类型，会转换为JSON）
            downstream_api: 下游API（JSON格式字符串）
            relation_type: 关系类型（QUTO/BY）
            relation_description: 关系描述，说明关系产生的原因
            status: 关系状态，默认0（待确认）
        """
        try:
            sql = f"""
                INSERT INTO {self.table} (
                    knowledge_base_id, upstream_api, data_model, downstream_api, relation_type, relation_description, status
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                knowledge_base_id,
                json.dumps(upstream_api) if upstream_api else None,
                json.dumps(data_model) if data_model else None,
                json.dumps(downstream_api) if downstream_api else None,
                relation_type,
                relation_description,
                status
            )

            return await self.db_pool.execute(sql, params)

        except Exception as e:
            self.logger.error(f"创建API数据模型关系记录失败: {str(e)}")
            raise

    async def get_relations_by_knowledge_base(self, knowledge_base_id: str) -> List[Dict[str, Any]]:
        """
        根据知识库ID查询所有关系记录
        
        Args:
            knowledge_base_id: 知识库ID
            
        Returns:
            List[Dict]: 查询结果列表（包含解析后的JSON字段）
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE knowledge_base_id = %s
                ORDER BY id DESC
            """
            results = await self.db_pool.fetch_all(sql, (knowledge_base_id,))

            # 解析JSON字段
            for result in results:
                if result.get("upstream_api") and isinstance(result["upstream_api"], str):
                    try:
                        result["upstream_api"] = json.loads(result["upstream_api"])
                    except json.JSONDecodeError:
                        result["upstream_api"] = None

                if result.get("data_model") and isinstance(result["data_model"], str):
                    try:
                        result["data_model"] = json.loads(result["data_model"])
                    except json.JSONDecodeError:
                        result["data_model"] = None

                if result.get("downstream_api") and isinstance(result["downstream_api"], str):
                    try:
                        result["downstream_api"] = json.loads(result["downstream_api"])
                    except json.JSONDecodeError:
                        result["downstream_api"] = None

            return results

        except Exception as e:
            self.logger.error(f"查询API数据模型关系记录失败: {str(e)}")
            raise

    async def update_status(self, relation_id: int, status: int) -> bool:
        """
        更新关系状态
        
        Args:
            relation_id: 关系记录ID
            status: 新的状态值
            
        Returns:
            bool: 是否更新成功
        """
        try:
            sql = f"""
                UPDATE {self.table}
                SET status = %s
                WHERE id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (status, relation_id))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"更新关系状态失败: {str(e)}")
            raise

    async def delete_relation(self, relation_id: int) -> bool:
        """
        删除关系记录
        
        Args:
            relation_id: 关系记录ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            sql = f"""
                DELETE FROM {self.table}
                WHERE id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (relation_id,))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"删除关系记录失败: {str(e)}")
            raise

    async def get_relation_by_id(self, relation_id: int) -> Optional[Dict[str, Any]]:
        """
        根据ID查询单个关系记录
        
        Args:
            relation_id: 关系记录ID
            
        Returns:
            Optional[Dict]: 查询结果，如果没有找到返回None
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE id = %s
            """
            result = await self.db_pool.fetch_one(sql, (relation_id,))

            if result:
                # 解析JSON字段
                if result.get("upstream_api") and isinstance(result["upstream_api"], str):
                    try:
                        result["upstream_api"] = json.loads(result["upstream_api"])
                    except json.JSONDecodeError:
                        result["upstream_api"] = None

                if result.get("data_model") and isinstance(result["data_model"], str):
                    try:
                        result["data_model"] = json.loads(result["data_model"])
                    except json.JSONDecodeError:
                        result["data_model"] = None

                if result.get("downstream_api") and isinstance(result["downstream_api"], str):
                    try:
                        result["downstream_api"] = json.loads(result["downstream_api"])
                    except json.JSONDecodeError:
                        result["downstream_api"] = None

            return result

        except Exception as e:
            self.logger.error(f"查询关系记录失败: {str(e)}")
            raise

    async def count_relations_by_status(self, knowledge_base_id: str, status: int) -> int:
        """
        统计指定状态的关系数量
        
        Args:
            knowledge_base_id: 知识库ID
            status: 状态值
            
        Returns:
            int: 关系数量
        """
        try:
            sql = f"""
                SELECT COUNT(*) AS count 
                FROM {self.table}
                WHERE knowledge_base_id = %s AND status = %s
            """
            result = await self.db_pool.fetch_one(sql, (knowledge_base_id, status))
            return result.get("count", 0)

        except Exception as e:
            self.logger.error(f"统计关系数量失败: {str(e)}")
            raise
