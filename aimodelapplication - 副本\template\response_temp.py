from typing import Optional, Union, Dict, List, Any
from pydantic import BaseModel

class BaseResponse(BaseModel):
    """
    基础响应模型
    """
    code: int
    message: str
    data: Optional[Union[Dict[str, Any], List[Any]]] = None

    def dict(self, **kwargs):
        """
        重写dict方法，确保data字段始终有值
        """
        data = super().dict(**kwargs)
        if data["data"] is None:
            data["data"] = {
                "count": 0,
                "records": []
            }
        return data

    def json(self, **kwargs):
        """
        重写json方法，确保可以正确序列化
        """
        return super().json(**kwargs)