from typing import Dict, List, Any, Optional
from db.mysql.mysql_pool import MySQLPool
from logger import logging

class KnowledgeBaseModel:
    """知识库模型类"""
    
    def __init__(self):
        self.table = "knowledge_base"
        self.primary_key = "id"
        self.logger = logging()
        # 创建数据库连接池实例
        self.db_pool = MySQLPool()
        
    async def initialize(self):
        """初始化连接池"""
        await self.db_pool.initialize()
    
    async def close(self):
        """关闭连接池，释放资源"""
        await self.db_pool.close()
    
    async def find_all(self) -> List[Dict[str, Any]]:
        """查询所有知识库记录
        
        Returns:
            知识库列表
        """
        try:
            sql = f"SELECT * FROM {self.table}"
            return await self.db_pool.fetch_all(sql)
        except Exception as e:
            self.logger.error(f"查询所有知识库记录失败: {str(e)}")
            return []
    
    async def find_by_username(self, username: str) -> Optional[Dict[str, Any]]:
        """根据username查询知识库记录
        
        Args:
            username: username
            
        Returns:
            知识库记录，如果不存在返回None
        """
        try:
            sql = f"SELECT * FROM {self.table} WHERE username = %s"
            result = await self.db_pool.fetch_one(sql, (username,))
            return result
        except Exception as e:
            self.logger.error(f"根据username查询知识库记录失败: {str(e)}")
            return None

    async def find_by_id(self, kb_id: str) -> Optional[Dict[str, Any]]:
        """根据username查询知识库记录

        Args:
            kb_id: 知识库ID

        Returns:
            知识库记录，如果不存在返回None
        """
        try:
            sql = f"SELECT * FROM {self.table} WHERE id = %s"
            result = await self.db_pool.fetch_one(sql, (kb_id,))
            return result
        except Exception as e:
            self.logger.error(f"根据username查询知识库记录失败: {str(e)}")
            return None
    
    async def insert(self, data: Dict[str, Any]) -> int:
        """插入知识库记录
        
        Args:
            data: 要插入的数据，字段名与值的字典
            
        Returns:
            插入成功返回1，失败返回0
        """
        try:
            # 提取字段名和对应的值
            fields = list(data.keys())
            placeholders = ', '.join(['%s'] * len(fields))
            values = tuple(data.values())
            
            # 构建SQL语句
            sql = f"INSERT INTO {self.table} ({', '.join(fields)}) VALUES ({placeholders})"
            
            # 执行插入操作
            result = await self.db_pool.execute(sql, values)
            return result
        except Exception as e:
            self.logger.error(f"插入知识库记录失败: {str(e)}")
            return 0
    
    async def update(self, kb_id: str, data: Dict[str, Any]) -> int:
        """更新知识库记录
        
        Args:
            kb_id: 知识库ID
            data: 要更新的数据，字段名与值的字典
            
        Returns:
            更新成功返回1，失败返回0
        """
        try:
            # 构建更新字段
            update_fields = [f"{k} = %s" for k in data.keys()]
            values = list(data.values())
            values.append(kb_id)  # 添加WHERE条件的值
            
            # 构建SQL语句
            sql = f"""
                UPDATE {self.table} 
                SET {', '.join(update_fields)}
                WHERE {self.primary_key} = %s
            """
            
            # 执行更新操作
            result = await self.db_pool.execute(sql, tuple(values))
            return result
        except Exception as e:
            self.logger.error(f"更新知识库记录失败: {str(e)}")
            return 0
    
    async def delete(self, kb_id: str) -> int:
        """删除知识库记录
        
        Args:
            kb_id: 知识库ID
            
        Returns:
            删除成功返回1，失败返回0
        """
        try:
            sql = f"DELETE FROM {self.table} WHERE id = %s"
            result = await self.db_pool.execute(sql, (kb_id,))
            return result
        except Exception as e:
            self.logger.error(f"删除知识库记录失败: {str(e)}")
            return 0
