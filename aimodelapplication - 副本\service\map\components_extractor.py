# extract_components.py
import json
import uuid
import os


class ExtractSwaggerComponents:

    def __init__(self, filepath):
        self.filepath = filepath
        self.document_name = os.path.basename(filepath)

    def extract_swagger_components(self):
        """
        提取OpenAPI文档中的组件(components)信息
        返回包含组件详情的字典列表
        """
        try:
            with open(self.filepath, 'r', encoding='utf-8') as f:
                swagger_data = json.load(f)

            schemas = swagger_data.get('components', {}).get('schemas', {})
            components = []

            for schema_name, details in schemas.items():
                # 准备组件数据
                component = {
                    "schema_name": schema_name,
                    "type": details.get('type', ''),
                    "title": details.get('title', ''),
                    "default_value": details.get('default'),
                    "enum_values": details.get('enum'),
                    "properties": details.get('properties', {}),
                    "required_properties": details.get('required', [])
                }

                components.append(component)

            print(f"🟢 从 {self.filepath} 中提取了 {len(components)} 个组件")
            return components

        except Exception as e:
            print(f"🔴 提取组件失败: {str(e)}")
            return []