from fastapi import APIRouter, Body, Query
from typing import Dict, Any
from template.response_temp import BaseResponse
from db.mysql.models.knowledge_dify_mapping import KnowledgeDifyMappingModel
from service.dify.dify import Dify, DifyAdmin
import os
from dotenv import load_dotenv
from fastapi.responses import JSONResponse

# 加载环境变量
load_dotenv()

router = APIRouter()

@router.post("/", response_model=BaseResponse)
async def dify_mapping(data: Dict[str, Any] = Body(...)):
    """
    关联Dify账号接口
    
    Args:
        data: 请求体数据
            - username: 用户名
            - email: Dify平台用户邮箱
            - password: Dify平台用户密码
            - user_id: 用户ID
    
    Returns:
        BaseResponse: 响应结果
    """
    # 获取请求参数
    username = data.get("username")
    email = data.get("email")
    password = data.get("password")
    user_id = data.get("user_id")
    
    # 参数验证
    if not username or not email or not password:
        return BaseResponse(
            code=400,
            message="邮箱或密码不能为空",
            data={}
        )
    
    # 创建知识库模型实例
    kb_model = KnowledgeDifyMappingModel()
    
    try:
        # 初始化数据库连接
        await kb_model.initialize()
        
        # 验证Dify账号
        dify_client = Dify(email=email, password=password)
        if not dify_client.account_active():
            return BaseResponse(
                code=401,
                message="Dify账号不存在或密码错误",
                data={}
            )
        
        # 查询是否已存在关联记录
        existing_records = await kb_model.find_by_userid_and_username(user_id, username)
        
        # 删除已存在的记录
        delete_count = 0
        if existing_records:
            for record in existing_records:
                if await kb_model.delete_by_id(record["id"]):
                    delete_count += 1

        try:
            # 获取API密钥
            dify_client = Dify(email=email, password=password)
            api_keys_response = await dify_client.get_api_keys()

            # 从响应中提取API密钥
            api_key = None
            if api_keys_response.code == 200 and api_keys_response.data.get("data"):
                api_keys = api_keys_response.data["data"]
                if api_keys:
                    api_key = api_keys[0].get("token")
        except Exception as e:
            return BaseResponse(
                code=500,
                message=f"获取Dify账号的API密钥失败，请联系管理员进行dify权限排查: {str(e)}",
                data={}
            )
        # 插入新记录
        new_record = {
            "username": username,
            "dify_email": email,
            "dify_password": password,
            "user_id": user_id,
            "dify_api_key": api_key
        }
        
        insert_result = await kb_model.insert(new_record)
        
        if insert_result > 0:
            return BaseResponse(
                code=200,
                message="Dify账号关联成功",
                data={
                    "deleted_count": delete_count,
                    "new_record": {k: v for k, v in new_record.items() if k != 'dify_password'}  # 返回时不包含密码字段
                }
            )
        else:
            return BaseResponse(
                code=500,
                message="Dify账号关联失败",
                data={}
            )
    
    except Exception as e:
        # 记录错误并返回错误响应
        return BaseResponse(
            code=500,
            message=f"操作失败: {str(e)}",
            data={}
        )
    
    finally:
        # 确保关闭数据库连接
        if 'kb_model' in locals():
            await kb_model.close()

@router.get("/", response_model=BaseResponse)
async def get_dify_mapping(
    user_id: str = Query(..., description="用户ID"),
    username: str = Query(..., description="用户名")
):
    """
    根据用户ID和用户名获取Dify账号关联信息
    
    Args:
        user_id: 用户ID
        username: 用户名
    
    Returns:
        BaseResponse: 响应结果
    """
    # 参数验证
    if not user_id or not username:
        return BaseResponse(
            code=400,
            message="用户ID和用户名不能为空",
            data={
                "count": 0,
                "records": []
            }
        )
    
    # 创建知识库模型实例
    kb_model = KnowledgeDifyMappingModel()
    
    try:
        # 初始化数据库连接
        await kb_model.initialize()
        
        # 查询关联记录
        records = await kb_model.find_by_userid_and_username(user_id, username)
        
        if records:
            # 返回记录列表，不包含密码字段
            return BaseResponse(
                code=200,
                message="查询成功",
                data={
                    "records": [
                        {k: v for k, v in record.items() if k != 'dify_password'}
                        for record in records
                    ][0]
                }
            )
        else:
            return BaseResponse(
                code=404,
                message="未找到关联记录",
                data={
                    "records": []
                }
            )
    
    except Exception as e:
        # 记录错误并返回错误响应
        return BaseResponse(
            code=500,
            message=f"查询失败: {str(e)}",
            data={
                "records": []
            }
        )
    
    finally:
        # 确保关闭数据库连接
        if 'kb_model' in locals():
            await kb_model.close()

@router.post("/create_dify_account", response_model=BaseResponse)
async def create_dify_account(data: Dict[str, Any] = Body(...)):
    """
    创建Dify账号接口
    
    Args:
        data: 请求体数据
            - email: 用户邮箱
    
    Returns:
        BaseResponse: 响应结果
    """
    # 获取请求参数
    email = data.get("email")
    
    # 参数验证
    if not email:
        return JSONResponse(
            status_code=400,
            content=BaseResponse(
                code=400,
                message="邮箱不能为空",
                data={
                    "count": 0,
                    "records": []
                }
            ).dict()
        )

    # 创建Dify账号
    dify_admin = DifyAdmin()
    response = await dify_admin.create_dify_account(email)
    
    # 如果创建成功，拼接完整的激活URL
    if response.code == 200:
        invitation_results = response.data.get("invitation_results", [])
        if invitation_results:
            invitation = invitation_results[0]
            if invitation.get("status") == "success":
                # 获取DIFY_HOST环境变量
                dify_host = os.getenv("DIFY_HOST")
                # 拼接完整的激活URL
                activation_url = f"{dify_host}{invitation.get('url')}"
                # 更新响应数据
                response.data["activation_url"] = activation_url
    
    return JSONResponse(
        status_code=response.code,
        content=response.dict()
    )
   
