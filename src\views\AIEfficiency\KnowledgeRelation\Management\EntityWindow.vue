<template>
  <a-modal
    :visible="visible"
    @update:visible="(val) => emit('update:visible', val)"
    :title="title"
    width="90vw"
    :footer="null"
    @cancel="handleClose"
    :body-style="{ maxHeight: '70vh', overflowY: 'auto', padding: '24px' }"
  >
    <div style="margin-bottom: 16px; display: flex; gap: 12px; align-items: center; justify-content: space-between;">
      <div style="display: flex; gap: 12px; align-items: center;">
        <a-select
          v-model:value="statusFilter"
          mode="multiple"
          allow-clear
          placeholder="请选择状态"
          style="width: 180px"
          @change="onStatusFilterChange"
        >
          <a-select-option v-for="item in statusOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
        <a-select
          v-model:value="tagFilter"
          mode="multiple"
          allow-clear
          placeholder="请选择标签"
          style="width: 180px"
          @change="onTagFilterChange"
        >
          <a-select-option v-for="tag in tagOptions" :key="tag" :value="tag">
            {{ tag }}
          </a-select-option>
        </a-select>
        <a-input-search
          v-model:value="searchText"
          placeholder="实体名称/接口路径"
          style="width: 300px"
          @search="onSearch"
          @input="(e) => debouncedSearch(e.target.value)"
        />
      </div>
      <div style="display: flex; gap: 8px;">
        <a-button 
          type="primary"
          :disabled="!selectedRowKeys.length || aiExtractDisabled"
          @click="handleEmbedSelected('increment')"
          class="embed-btn increment-embed-btn"
        >
          <template #icon><PlusOutlined /></template>
          <template v-if="aiExtractDisabled">
            <a-tooltip :title="aiExtractTooltip">
              <span>AI关系提取</span>
            </a-tooltip>
          </template>
          <template v-else>
            AI关系提取
          </template>
        </a-button>
        <a-button type="primary" @click="showColumnSettings" class="column-settings-btn">
          <template #icon><SettingOutlined /></template>
          自定义列
        </a-button>
      </div>
    </div>
    <a-table
      :columns="visibleColumns"
      :data-source="pagedList"
      :pagination="{
        current: page,
        pageSize: pageSize,
        pageSizeOptions: ['10', '20', '50'],
        showSizeChanger: true,
        showQuickJumper: true,
        onShowSizeChange: onPageSizeChange,
        total: total,
        onChange: onPageChange
      }"
      rowKey="id"
      bordered
      size="middle"
      :scroll="{ x: 'max-content', y: 'calc(70vh - 200px)' }"
      :row-selection="rowSelection"
      :loading="entityLoading"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-else-if="column.key === 'method'">
          <span :style="getMethodTagStyle(record.method)">{{ record.method }}</span>
        </template>
        <template v-else-if="column.key === 'tags'">
          <div class="tags-container">
            <a-tag v-for="tag in record.tags" :key="tag" :color="getTagColor(tag)">{{ tag }}</a-tag>
          </div>
        </template>
        <template v-else-if="column.key === 'parameters_list'">
          <div class="content-preview">
            <span class="preview-text">{{ getPreviewText(record.parameters_list) }}</span>
            <a-button 
              type="link" 
              class="view-btn"
              @click="showDetailModal(record, 'parameters')"
            >
              <EyeOutlined />
            </a-button>
          </div>
        </template>
        <template v-else-if="column.key === 'request_body_list'">
          <div class="content-preview">
            <span class="preview-text">{{ getPreviewText(record.request_body_list) }}</span>
            <a-button 
              type="link" 
              class="view-btn"
              @click="showDetailModal(record, 'requestBody')"
            >
              <EyeOutlined />
            </a-button>
          </div>
        </template>
        <template v-else-if="column.key === 'responses_list'">
          <div class="content-preview">
            <span class="preview-text">{{ getPreviewText(record.responses_list) }}</span>
            <a-button 
              type="link" 
              class="view-btn"
              @click="showDetailModal(record, 'responses')"
            >
              <EyeOutlined />
            </a-button>
          </div>
        </template>
        <template v-else-if="column.key === 'properties'">
          <div class="content-preview">
            <span class="preview-text">{{ getPreviewText(record.properties) }}</span>
            <a-button 
              type="link" 
              class="view-btn"
              @click="showDetailModal(record, 'properties')"
            >
              <EyeOutlined />
            </a-button>
          </div>
        </template>
      </template>
    </a-table>

    <!-- 列设置弹窗 -->
    <a-modal
      v-model:visible="columnSettingsVisible"
      title="自定义列"
      width="400px"
      :footer="null"
      @cancel="columnSettingsVisible = false"
      class="column-settings-modal"
    >
      <div class="column-settings-content">
        <div class="column-settings-header">
          <a-checkbox
            :indeterminate="isIndeterminate"
            :checked="isAllSelected"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
        </div>
        <div class="column-settings-body">
          <a-checkbox-group v-model:value="selectedColumns" style="width: 100%">
            <a-row>
              <a-col :span="8" v-for="col in allColumns" :key="col.key">
                <a-checkbox :value="col.key">{{ col.title }}</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </div>
      </div>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      :title="detailModalTitle"
      width="800px"
      :footer="null"
      @cancel="detailModalVisible = false"
    >
      <div class="detail-content">
        <template v-if="detailModalTitle === '参数详情'">
          <a-table
            :columns="parameterColumns"
            :data-source="detailContent"
            :pagination="false"
            size="small"
            bordered
          />
        </template>
        <template v-else-if="detailModalTitle === '响应详情'">
          <div v-for="(response, index) in detailContent" :key="index" class="response-item">
            <a-tag :color="getStatusTagColor(response.status)" class="response-status-tag">{{ response.status }}</a-tag>
            <span class="response-description">{{ response.description }}</span>
            <pre class="json-content" v-if="response.schema">{{ formatJson(response.schema) }}</pre>
          </div>
        </template>
        <template v-else>
          <pre class="json-content">{{ formatJson(detailContent) }}</pre>
        </template>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue';
import { ApiOutlined, SettingOutlined, EyeOutlined, PlusOutlined, ReloadOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import http from '@/utils/ai/http'; // 引入http工具
import { Tooltip } from 'ant-design-vue';

const entityList = ref([]); // 将entityList的定义移动到此处

// Method 标签颜色映射 (从ApiWindow复制)
const methodColorMap = {
  'GET': '#52c41a',     // 绿色
  'POST': '#1890ff',    // 蓝色
  'PUT': '#faad14',     // 黄色
  'DELETE': '#ff4d4f',  // 红色
  'PATCH': '#722ed1',   // 紫色
  'HEAD': '#13c2c2',    // 青色
  'OPTIONS': '#eb2f96'  // 粉色
};

// 标签颜色映射 (从ApiWindow复制)
const tagColors = [
  '#1890ff', // 蓝色
  '#faad14', // 黄色
  '#13c2c2', // 青色
  '#eb2f96', // 粉色
  '#a0d911', // 浅绿
  '#2f54eb', // 极客蓝
  '#faad14', // 金色
  '#13c2c2', // 青色
  '#fa8c16', // 橙色
  '#a0d911', // 浅绿
  '#2f54eb', // 极客蓝
  '#1890ff', // 蓝色
];

// 标签颜色缓存 (从ApiWindow复制)
const tagColorCache = new Map();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '实体列表'
  },
  taskProgress: {
    type: [Number, null],
    default: null
  }
});

const emit = defineEmits(['update:visible', 'close', 'embed', 'start-polling']);

// 状态筛选
const statusFilter = ref([]);
const entityLoading = ref(false);
const statusOptions = computed(() => {
  const statusSet = new Set(entityList.value.map(item => item.status || 'pending')); // 将空状态视为待提取
  const statusLabelMap = {
    'active': '激活',
    'inactive': '非激活',
    'deprecated': '已废弃',
    'pending': '待提取' // 更新待审核的显示文本为待提取
  };
  return Array.from(statusSet).map(s => ({
    value: s || 'pending', // 将空状态视为待提取
    label: statusLabelMap[s || 'pending'] || s
  }));
});

// 标签筛选 (新增)
const tagFilter = ref([]);
const tagOptions = computed(() => {
  const tagsSet = new Set();
  entityList.value.forEach(item => {
    if (Array.isArray(item.tags)) {
      item.tags.forEach(tag => tagsSet.add(tag));
    }
  });
  return Array.from(tagsSet);
});

// 获取实体列表 (新增方法)
const fetchEntityList = async () => {
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }
  entityLoading.value = true;
  try {
    // 调用 /knowledge_api/{relation_id}/get_graph_api_data 接口
    const res = await http.get(`/knowledge_api/${relationId}/get_graph_api_data`);
    if (res.data.code === 200) {
      // 假设接口返回的数据结构可以直接用于entityList
      const processedData = (res.data.data || []).map(item => {
        let tagsArray = [];
        const rawTags = item.tags; // 保存原始标签值

        if (typeof rawTags === 'string') {
          try {
            const parsedTags = JSON.parse(rawTags);
            if (Array.isArray(parsedTags)) {
              tagsArray = parsedTags;
            } else if (parsedTags !== null && parsedTags !== undefined) {
              tagsArray = [String(parsedTags)];
            }
          } catch (e) {
            console.warn('Failed to parse tags string as JSON, treating as plain string:', rawTags, e);
            tagsArray = [rawTags]; // 解析失败，将原始字符串作为单个标签
          }
        } else if (Array.isArray(rawTags)) {
          tagsArray = rawTags;
        } else if (rawTags !== null && rawTags !== undefined) {
          tagsArray = [String(rawTags)]; // 如果不是字符串也不是数组，但有值，将其作为单个标签
        }

        // 确保所有元素都是字符串，并过滤掉空字符串
        tagsArray = tagsArray.map(tag => String(tag || '')).filter(tag => tag !== '');

        console.log('Original tags for item:', item.id, rawTags, '; Processed tags:', tagsArray); // 添加日志
        return { ...item, tags: tagsArray };
      });
      entityList.value = processedData;
    } else {
      entityList.value = [];
      message.error(res.data.message || '获取实体列表失败');
    }
  } catch (error) {
    entityList.value = [];
    message.error('获取实体列表失败');
    console.error('获取实体列表失败:', error);
  } finally {
    entityLoading.value = false;
  }
};

// AI关系提取按钮可用状态和提示
const aiExtractDisabled = ref(false);
const aiExtractTooltip = ref('');

// 监听 taskProgress 变化，动态设置按钮状态
watch(() => props.taskProgress, (newVal) => {
  if (newVal !== null && newVal !== undefined) {
    if (newVal !== 100) {
      aiExtractDisabled.value = true;
      aiExtractTooltip.value = '请任务完成后再提取关系~';
    } else {
      aiExtractDisabled.value = false;
      aiExtractTooltip.value = '';
    }
  } else {
    aiExtractDisabled.value = false;
    aiExtractTooltip.value = '';
  }
});

// 监听 visible 属性的变化，当它变为 true 时调用 fetchEntityList
watch(() => props.visible, async (newVal) => {
  if (newVal) {
    fetchEntityList();
    selectedRowKeys.value = []; // 每次打开弹窗时清空选中项
  }
});

// 搜索
const searchText = ref('');

// 分页
const page = ref(1);
const pageSize = ref(10);

// 行选择
const selectedRowKeys = ref([]);
const rowSelection = {
  selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys;
  }
};

// 列设置
const columnSettingsVisible = ref(false);
const selectedColumns = ref(['id', 'summary', 'path', 'method', 'tags', 'status']);
const allColumns = [
  { title: 'ID', key: 'id' },
  { title: '接口名称', key: 'summary', width: 150, ellipsis: true },
  { title: '状态', key: 'status' },
  { title: '参数', key: 'parameters_list' },
  { title: '请求体', key: 'request_body_list' },
  { title: '响应', key: 'responses_list' },
  { title: '请求方法', key: 'method' },
  { title: '标签', key: 'tags' },
  { title: '更新时间', key: 'create_time' },
];

// 表格列配置
const columns = [
  { title: 'ID', dataIndex: 'id', key: 'id', width: 60, ellipsis: true },
  { title: '标签', dataIndex: 'tags', key: 'tags', width: 80, ellipsis: true },
  { title: '接口名称', dataIndex: 'summary', key: 'summary', width: 150, ellipsis: true },
  { title: '请求方法', dataIndex: 'method', key: 'method', width: 100, ellipsis: true },
  { title: '接口路径', dataIndex: 'path', key: 'path', width: 200, ellipsis: true },
  { title: '状态', dataIndex: 'status', key: 'status', width: 80, ellipsis: true },
  { 
    title: '参数', 
    dataIndex: 'parameters_list', 
    key: 'parameters_list', 
    width: 150, 
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text) return '-';
      const content = typeof text === 'string' ? text : JSON.stringify(text);
      return content.length > 50 ? content.substring(0, 50) + '...' : content;
    }
  },
  { 
    title: '请求体', 
    dataIndex: 'request_body_list', 
    key: 'request_body_list', 
    width: 150, 
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text) return '-';
      const content = typeof text === 'string' ? text : JSON.stringify(text);
      return content.length > 50 ? content.substring(0, 50) + '...' : content;
    }
  },
  { 
    title: '响应', 
    dataIndex: 'responses_list', 
    key: 'responses_list', 
    width: 150, 
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text) return '-';
      const content = typeof text === 'string' ? text : JSON.stringify(text);
      return content.length > 50 ? content.substring(0, 50) + '...' : content;
    }
  },
  { 
    title: '更新时间', 
    dataIndex: 'create_time', 
    key: 'create_time', 
    width: 150, 
    ellipsis: true,
    customRender: ({ text }) => {
      if (!text) return '-';
      const date = new Date(text);
      return date.toLocaleString(); // 格式化日期时间
    }
  }
];

// 计算可见列
const visibleColumns = computed(() => {
  return columns.filter(col => selectedColumns.value.includes(col.key));
});

// 实体筛选后的列表
const filteredEntityList = computed(() => {
  let list = entityList.value;
  
  if (statusFilter.value.length > 0) {
    list = list.filter(item => statusFilter.value.includes(item.status));
  }

  if (tagFilter.value.length > 0) {
    list = list.filter(item => {
      return item.tags && tagFilter.value.every(filterTag => item.tags.includes(filterTag));
    });
  }
  
  if (searchText.value) {
    const searchTextLower = searchText.value.toLowerCase().trim();
    list = list.filter(item => {
      const summary = (item.summary || '').toLowerCase();
      const path = (item.path || '').toLowerCase();
      const tags = (item.tags || []).map(tag => tag.toLowerCase()).join(' ');
      
      return summary.includes(searchTextLower) || 
             path.includes(searchTextLower) ||
             tags.includes(searchTextLower);
    });
  }
  
  return list;
});

// 分页后的列表
const pagedList = computed(() => {
  console.log('pagedList: filteredEntityList.value type:', typeof filteredEntityList.value, 'value:', filteredEntityList.value);
  const start = (page.value - 1) * pageSize.value;
  // 再次确保filteredEntityList.value是一个数组，以防万一
  const listToSlice = Array.isArray(filteredEntityList.value) ? filteredEntityList.value : [];
  return listToSlice.slice(start, start + pageSize.value);
});

// 总数
const total = computed(() => filteredEntityList.value.length);

// 方法
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

const onStatusFilterChange = () => {
  page.value = 1;
};

const onTagFilterChange = () => { // 新增标签筛选变更方法
  page.value = 1;
};

const onSearch = () => {
  page.value = 1;
};

const onPageChange = (p, size) => {
  page.value = p;
  pageSize.value = size;
};

const onPageSizeChange = (current, size) => {
  page.value = 1;
  pageSize.value = size;
};

const showColumnSettings = () => {
  columnSettingsVisible.value = true;
};

const handleColumnSettingsConfirm = () => {
  columnSettingsVisible.value = false;
};

const isIndeterminate = computed(() => {
  return selectedColumns.value.length > 0 && selectedColumns.value.length < allColumns.length;
});

const isAllSelected = computed(() => {
  return selectedColumns.value.length === allColumns.length;
});

const onCheckAllChange = (e) => {
  selectedColumns.value = e.target.checked ? allColumns.map(col => col.key) : [];
};

// 详情弹窗相关
const detailModalVisible = ref(false);
const detailContent = ref(null);
const detailModalTitle = ref(''); // 动态标题

// 参数详情表格列配置 (从ApiWindow复制)
const parameterColumns = [
  { title: '参数名称', dataIndex: 'name', key: 'name', width: 120 },
  { title: '位置', dataIndex: 'in', key: 'in', width: 80 },
  { title: '类型', dataIndex: 'type', key: 'type', width: 100 },
  { title: '必填', dataIndex: 'required', key: 'required', width: 80, customRender: ({ text }) => (text ? '是' : '否') },
  { title: '描述', dataIndex: 'description', key: 'description' },
];

// 显示详情弹窗 (修改逻辑)
const showDetailModal = (record, type) => {
  detailModalVisible.value = true;
  let contentToShow = null;
  
  switch (type) {
    case 'parameters':
      detailModalTitle.value = '参数详情';
      // 优先使用record.parameters，如果不存在则回退到record.parameters_list
      contentToShow = record.parameters || record.parameters_list;
      detailContent.value = formatParameters(contentToShow);
      console.log('Parameters detail content:', contentToShow);
      break;
    case 'requestBody':
      detailModalTitle.value = '请求体详情';
      // 优先使用record.request_body，如果不存在则回退到record.request_body_list
      contentToShow = record.request_body || record.request_body_list;
      detailContent.value = contentToShow;
      console.log('Request Body detail content:', contentToShow);
      break;
    case 'responses':
      detailModalTitle.value = '响应详情';
      // 优先使用record.responses，如果不存在则回退到record.responses_list
      contentToShow = record.responses || record.responses_list;
      detailContent.value = formatResponses(contentToShow);
      console.log('Responses detail content:', contentToShow);
      break;
    case 'properties':
      detailModalTitle.value = '实体详情';
      detailContent.value = record.properties;
      console.log('Properties detail content:', record.properties);
      break;
    default:
      detailModalTitle.value = '详情';
      detailContent.value = null;
  }
};

// 获取预览文本 (从ApiWindow复制)
const getPreviewText = (content) => {
  if (!content) return '-';
  const text = typeof content === 'string' ? content : JSON.stringify(content);
  return text.length > 50 ? text.substring(0, 50) + '...' : text;
};

// 格式化JSON (从ApiWindow复制)
const formatJson = (content) => {
  if (!content) return '';
  try {
    const json = typeof content === 'string' ? JSON.parse(content) : content;
    return JSON.stringify(json, null, 2);
  } catch (e) {
    return content;
  }
};

// 格式化参数数据 (从ApiWindow复制)
const formatParameters = (content) => {
  if (!content) return [];
  try {
    const params = typeof content === 'string' ? JSON.parse(content) : content;
    if (Array.isArray(params)) {
      return params.map(param => ({
        name: param.name || param.parameter_name,
        in: param.in || param.location,
        type: param.type || param.parameter_type,
        required: param.required || false,
        description: param.description || param.parameter_description || ''
      }));
    }
    return [{
      name: params.name || params.parameter_name,
      in: params.in || params.location,
      type: params.type || params.parameter_type,
      required: params.required || false,
      description: params.description || params.parameter_description || ''
    }];
  } catch (e) {
    return [];
  }
};

// 格式化响应数据 (从ApiWindow复制)
const formatResponses = (content) => {
  if (!content) return {};
  try {
    const responses = typeof content === 'string' ? JSON.parse(content) : content;
    return Object.entries(responses).map(([status, response]) => ({
      status,
      description: response.description || '',
      schema: response.schema || response.content || {}
    }));
  } catch (e) {
    return [];
  }
};

// 获取状态码标签颜色 (从ApiWindow复制)
const getStatusTagColor = (status) => {
  const statusCode = parseInt(status);
  if (statusCode >= 200 && statusCode < 300) return 'success';
  if (statusCode >= 300 && statusCode < 400) return 'warning';
  if (statusCode >= 400 && statusCode < 500) return 'error';
  if (statusCode >= 500) return 'volcano';
  return 'default';
};

// 获取状态颜色
const getStatusColor = (status) => {
  if (!status) return 'warning'; // 待提取状态使用警告色
  const colorMap = {
    'active': 'success',    // 已嵌入 - 绿色
    'inactive': 'default',  // 未嵌入 - 灰色
    'deprecated': 'blue',   // 已废弃 - 蓝色
    'pending': 'blue'       // 待审核 - 蓝色
  };
  return colorMap[status] || 'default';
};

// 获取状态文本
const getStatusText = (status) => {
  if (!status) return '待提取'; // 增加判断，如果状态为空，则展示为待提取
  const textMap = {
    'active': '激活',
    'inactive': '非激活',
    'deprecated': '已废弃',
    'pending': '待审核'
  };
  return textMap[status] || status;
};

// 获取 Method 标签样式 (从ApiWindow复制)
const getMethodTagStyle = (method) => {
  const color = methodColorMap[method?.toUpperCase()] || '#d9d9d9';
  return {
    backgroundColor: `${color}15`,
    color: color,
    border: `1px solid ${color}30`,
    padding: '2px 8px',
    borderRadius: '4px',
    fontSize: '12px',
    fontWeight: '500',
    display: 'inline-block',
    textAlign: 'center',
    minWidth: '60px'
  };
};

// 获取标签颜色 (从ApiWindow复制)
const getTagColor = (tag) => {
  // 如果标签已经在缓存中，直接返回缓存的颜色
  if (tagColorCache.has(tag)) {
    return tagColorCache.get(tag);
  }

  // 为标签分配一个随机颜色
  const randomColor = tagColors[Math.floor(Math.random() * tagColors.length)];
  
  // 将颜色存入缓存
  tagColorCache.set(tag, randomColor);
  
  return randomColor;
};

// 在组件卸载时清除颜色缓存
onUnmounted(() => {
  tagColorCache.clear();
});

// 修改嵌入处理方法，直接调用接口
const handleEmbedSelected = async (type) => {
  if (!selectedRowKeys.value.length) {
    message.warning('请选择要提取的实体');
    return;
  }

  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }

  if (type === 'increment') {
    try {
      // 调用新的关系提取接口，传递选中的API ID列表
      const extractRes = await http.post(`/knowledge_api/${relationId}/extract_relations`, {
        entity_list: selectedRowKeys.value
      });

      if (extractRes.data && extractRes.data.code === 200) {
        const data = extractRes.data.data;
        message.success(`关系提取成功！分析了 ${selectedRowKeys.value.length} 个API，找到 ${data.total_apis_found} 个相关前置接口，提取了 ${data.total_relations} 个API关系，生成了 ${data.total_data_models} 个数据模型，分析了 ${data.total_api_data_model_relations} 个API数据模型关系，已保存 ${data.saved_relations_count} 个关系到数据库`);

        // 可以在这里处理返回的数据，比如显示提取结果
        console.log('前置依赖分析:', data.prerequisite_analysis);
        console.log('相关接口:', data.related_apis);
        console.log('API关系:', data.api_relations);
        console.log('生成的数据模型:', data.data_models);
        console.log('API数据模型关系:', data.api_data_model_relations);
        console.log('保存到数据库的关系数量:', data.saved_relations_count);

        // 可以在这里添加更详细的结果展示
        if (data.data_models && data.data_models.length > 0) {
          console.log('生成的数据模型详情:');
          data.data_models.forEach((model, index) => {
            console.log(`${index + 1}. ${model.name}`);
            console.log(`   类型: ${model.type}`);
            console.log(`   关系映射:`, model.relation);
            console.log(`   描述: ${model.description}`);
            console.log('---');
          });
        }

        if (data.api_data_model_relations && data.api_data_model_relations.length > 0) {
          console.log('API数据模型关系详情:');
          data.api_data_model_relations.forEach((relation, index) => {
            console.log(`${index + 1}. 关系类型: ${relation.upstream_api?.relation_type || relation.downstream_api?.relation_type}`);
            if (relation.upstream_api) {
              console.log(`   上游API: ${relation.upstream_api.method} ${relation.upstream_api.path}`);
            }
            if (relation.downstream_api) {
              console.log(`   下游API: ${relation.downstream_api.method} ${relation.downstream_api.path}`);
            }
            console.log(`   数据模型: ${relation.data_model.name}`);
            console.log('---');
          });
        }

        emit('close');
        // 不需要轮询进度，因为新接口是同步返回结果的
      } else {
        message.error(extractRes.data?.message || '关系提取失败');
      }
    } catch (error) {
      console.error('关系提取失败:', error);
      if (error.response?.status === 404) {
        message.error('请先配置提示词或导入接口文档');
      } else if (error.response?.status === 400) {
        message.error('请选择要分析的API接口');
      } else {
        message.error(error.response?.data?.message || '关系提取失败');
      }
    }
  } else {
    // 其他类型（如覆盖提取）保持原有逻辑
    try {
      const endpoint = `/knowledge_api/${relationId}/embed_entities_override`;
      const payload = {
        entity_list: selectedRowKeys.value,
        type: 'cover'
      };
      const res = await http.post(endpoint, payload);
      if (res.data.code === 200) {
        message.success('覆盖提取成功');
        emit('close');
      } else {
        message.error(res.data.message || '提取失败');
      }
    } catch (error) {
      message.error('提取失败');
      console.error('提取失败:', error);
    }
  }
};

// 添加防抖的搜索方法
const debouncedSearch = (() => {
  let timer = null;
  return (value) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      searchText.value = value;
      page.value = 1;
    }, 300);
  };
})();
</script>

<style scoped>
/* 自定义列按钮样式 */
.column-settings-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.column-settings-btn :deep(.anticon) {
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 14px;
}

/* 自定义列弹窗样式 */
.column-settings-modal :deep(.ant-modal-content) {
  border-radius: 8px;
  overflow: hidden;
}

.column-settings-modal :deep(.ant-modal-header) {
  margin-bottom: 0;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.column-settings-modal :deep(.ant-modal-title) {
  font-size: 16px;
  font-weight: 500;
}

.column-settings-content {
  padding: 16px 24px;
}

.column-settings-header {
  padding-bottom: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.column-settings-body {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 8px;
}

.column-settings-body::-webkit-scrollbar {
  width: 6px;
}

.column-settings-body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.column-settings-body::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 增量提取按钮样式 */
.increment-embed-btn {
  background-color: #52c41a; /* 绿色 */
  border-color: #52c41a;
}

.increment-embed-btn:hover {
  background-color: #73d13d; /* 悬停时稍亮的绿色 */
  border-color: #73d13d;
}

.increment-embed-btn[disabled] {
  background-color: #f5f5f5; /* 禁用时为浅灰色 */
  border-color: #d9d9d9;     /* 禁用时边框为深灰色 */
  color: rgba(0, 0, 0, 0.25); /* 禁用时文字颜色变浅 */
}

/* 覆盖提取按钮样式 */
.embed-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.embed-btn :deep(.anticon) {
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 14px;
}

/* 内容预览样式 */
.content-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  width: 200px;
}

.preview-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 170px;
}

.view-btn {
  padding: 0;
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  transition: all 0.3s;
}

.view-btn:hover {
  color: #40a9ff;
  transform: scale(1.1);
}

/* 详情弹窗样式 */
.detail-content {
  max-height: 60vh;
  overflow-y: auto;
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
}

.json-content {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
}

/* 美化滚动条 */
.detail-content::-webkit-scrollbar {
  width: 6px;
}

.detail-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.detail-content::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 标签容器样式 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* 响应详情样式 */
.response-item {
  margin-bottom: 12px;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background: #fff;
}

.response-status-tag {
  margin-right: 8px;
  font-weight: 500;
}

.response-description {
  font-size: 14px;
  color: #595959;
}

.response-item .json-content {
  margin-top: 8px;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  font-size: 12px;
}

.response-item .json-content::-webkit-scrollbar {
  width: 6px;
}

.response-item .json-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.response-item .json-content::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 提取类型选择弹窗内容样式 */
.extraction-type-content {
  padding: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
}

</style>
