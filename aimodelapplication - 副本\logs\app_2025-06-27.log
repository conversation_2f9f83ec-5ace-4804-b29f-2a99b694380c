2025-06-27 09:08:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:34 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | INFO | 成功查询知识库 d003270b-3f38-459e-bc4f-a5fefdb2e766 的API文档列表: ['明御防火墙.openapi.3.0.json']
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:39 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | INFO | 成功查询知识库 9c9926fe-eccd-4bf9-8c6a-e64d8f2c8cf1 的API文档列表: ['test.json']
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:42 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:43 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:46 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:08:50 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:09:26 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:31 | ERROR | 查找孤儿实体失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:22:36 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:24 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:43:25 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 09:49:23 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:39 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 10:41:40 | ERROR | 查找关系失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:42 | INFO | 成功查询知识库 f442446c-9a62-4fbe-8457-f98f7e5a6142 的API文档列表: ['明御运维审计与风险控制系统v9.openapi.3.0.json']
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 11:36:43 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:38:22 | ERROR | MySQL连接池初始化失败: (2003, "Can't connect to MySQL server on '*************'")
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:20 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:27 | WARNING | SQL查询失败，尝试重试 (1/3): Cannot acquire connection after closing pool
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | INFO | 成功查询知识库 d604f34c-017d-4204-be1e-f731a2183d2b 的API文档列表: ['test.json']
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | WARNING | 连接池 *************:3306/ai_module 无效，尝试重新初始化: Cannot acquire connection after closing pool
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
2025-06-27 16:40:30 | ERROR | 查找所有节点失败: Failed to read from defunct connection ResolvedIPv4Address(('*************', 7687)) (ResolvedIPv4Address(('*************', 7687)))
