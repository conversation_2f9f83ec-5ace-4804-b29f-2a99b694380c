<template>
  <a-modal
    :visible="visible"
    @update:visible="(val) => emit('update:visible', val)"
    :title="title"
    width="90vw"
    :footer="null"
    @cancel="handleClose"
    :body-style="{ maxHeight: '70vh', overflowY: 'auto', padding: '0 24px 24px 24px' }"
  >
    <a-tabs v-model:activeKey="activeTabKey">
      <!-- 接口-数据模型关系标签页 -->
      <a-tab-pane key="api-model" tab="接口-数据模型关系">
        <div class="tab-content">
          <div class="table-header">
            <div class="left-section">
              <a-select
                v-model:value="apiModelRelationFilter"
                mode="multiple"
                allow-clear
                placeholder="按关系类型筛选"
                style="width: 180px"
                @change="onApiModelRelationFilterChange"
                :max-tag-count="0"
                :max-tag-placeholder="() => `已选${apiModelRelationFilter.length}项`"
              >
                <a-select-option v-for="item in apiModelRelationOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
              <a-select
                v-model:value="apiModelStatusFilter"
                mode="multiple"
                allow-clear
                placeholder="按状态筛选"
                style="width: 180px"
                @change="onApiModelStatusFilterChange"
              >
                <a-select-option v-for="item in apiModelStatusOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
              <a-select
                v-model:value="apiModelTagFilter"
                mode="multiple"
                allow-clear
                placeholder="请选择标签"
                style="width: 180px"
                @change="onApiModelTagFilterChange"
                :max-tag-count="0"
                :max-tag-placeholder="() => `已选${apiModelTagFilter.length}项`"
              >
                <a-select-option v-for="tag in apiModelTagOptions" :key="tag" :value="tag">
                  {{ tag }}
                </a-select-option>
              </a-select>
              <a-input-search
                v-model:value="apiModelSearch"
                placeholder="ID/接口名称/接口路径/数据模型"
                style="width: 300px"
                @search="onApiModelSearch"
                @input="(e) => debouncedSearch(e.target.value, 'apiModel')"
              />
            </div>
            <div class="right-section">
              <a-button 
                type="default"
                :disabled="!apiModelSelectedRowKeys.length"
                @click="handleBatchConfirmApiModel"
                class="batch-btn batch-confirm-green-btn"
              >
                <template #icon><CheckOutlined /></template>
                确认
              </a-button>
              <a-button 
                type="default"
                :disabled="!apiModelSelectedRowKeys.length"
                @click="handleBatchIgnoreApiModel"
                class="batch-btn batch-ignore-blue-btn"
              >
                <template #icon><CloseOutlined /></template>
                忽略
              </a-button>
              <a-button 
                type="default"
                :disabled="!apiModelSelectedRowKeys.length"
                @click="handleBatchDeleteApiModel"
                class="batch-btn batch-delete-red-btn"
              >
                <template #icon><DeleteOutlined /></template>
                删除
              </a-button>
              <a-button type="primary" @click="showColumnSettings('apiModel')" class="column-settings-btn">
                <template #icon><SettingOutlined /></template>
                自定义列
              </a-button>
            </div>
          </div>

          <a-table
            :columns="apiModelColumns"
            :data-source="apiModelPagedList"
            :pagination="{
              current: apiModelPage,
              pageSize: apiModelPageSize,
              pageSizeOptions: ['10', '20', '50'],
              showSizeChanger: true,
              showQuickJumper: true,
              onShowSizeChange: (current, size) => onPageSizeChange(current, size, 'apiModel'),
              total: apiModelTotal,
              onChange: (page, size) => onPageChange(page, size, 'apiModel')
            }"
            rowKey="id"
            bordered
            size="middle"
            :scroll="{ x: 'max-content', y: 'calc(65vh - 200px)' }"
            :loading="apiModelLoading"
            :row-selection="apiModelRowSelection"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'relationType'">
                <a-tag :color="getRelationTypeColor(record.relationType)">
                  {{ getRelationTypeText(record.relationType) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'apiType'">
                <a-tag :color="getApiTypeColor(record.apiType)">
                  {{ record.apiType }}
                </a-tag>
              </template>
              <template v-if="column.key === 'details'">
                <div class="content-preview">
                  <span class="preview-text">{{ getPreviewText(record.details) }}</span>
                  <a-button 
                    type="link" 
                    class="view-btn"
                    @click="showDetailModal(record, 'apiModel')"
                  >
                    <EyeOutlined />
                  </a-button>
                </div>
              </template>
            </template>
          </a-table>
        </div>
      </a-tab-pane>

      <!-- 数据模型-数据模型关系标签页 -->
      <a-tab-pane key="model-model" tab="数据模型-数据模型关系">
        <div class="tab-content">
          <div class="table-header">
            <div class="left-section">
              <a-select
                v-model:value="modelModelStatusFilter"
                mode="multiple"
                allow-clear
                placeholder="按状态筛选"
                style="width: 180px"
                @change="onModelModelStatusFilterChange"
              >
                <a-select-option v-for="item in modelModelStatusOptions" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
              <a-input-search
                v-model:value="modelModelSearch"
                placeholder="ID/数据模型/父模型/子模型"
                style="width: 300px"
                @search="onModelModelSearch"
                @input="(e) => debouncedSearch(e.target.value, 'modelModel')"
              />
            </div>
            <div class="right-section">
              <a-button 
                type="default"
                :disabled="!modelModelSelectedRowKeys.length"
                @click="handleBatchConfirmModelModel"
                class="batch-btn batch-confirm-green-btn"
              >
                <template #icon><CheckOutlined /></template>
                确认
              </a-button>
              <a-button 
                type="default"
                :disabled="!modelModelSelectedRowKeys.length"
                @click="handleBatchIgnoreModelModel"
                class="batch-btn batch-ignore-blue-btn"
              >
                <template #icon><CloseOutlined /></template>
                忽略
              </a-button>
              <a-button 
                type="default"
                :disabled="!modelModelSelectedRowKeys.length"
                @click="handleBatchDeleteModelModel"
                class="batch-btn batch-delete-red-btn"
              >
                <template #icon><DeleteOutlined /></template>
                删除
              </a-button>
              <a-button type="primary" @click="showColumnSettings('modelModel')" class="column-settings-btn">
                <template #icon><SettingOutlined /></template>
                自定义列
              </a-button>
            </div>
          </div>

          <a-table
            :columns="modelModelColumns"
            :data-source="modelModelPagedList"
            :pagination="{
              current: modelModelPage,
              pageSize: modelModelPageSize,
              pageSizeOptions: ['10', '20', '50'],
              showSizeChanger: true,
              showQuickJumper: true,
              onShowSizeChange: (current, size) => onPageSizeChange(current, size, 'modelModel'),
              total: modelModelTotal,
              onChange: (page, size) => onPageChange(page, size, 'modelModel')
            }"
            rowKey="id"
            bordered
            size="middle"
            :scroll="{ x: 'max-content', y: 'calc(65vh - 200px)' }"
            :loading="modelModelLoading"
            :row-selection="modelModelRowSelection"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'relationType'">
                <a-tag :color="getModelRelationTypeColor(record.relationType)">
                  {{ getModelRelationTypeText(record.relationType) }}
                </a-tag>
              </template>
              <template v-if="column.key === 'details'">
                <div class="content-preview">
                  <span class="preview-text">{{ getPreviewText(record.details) }}</span>
                  <a-button 
                    type="link" 
                    class="view-btn"
                    @click="showDetailModal(record, 'modelModel')"
                  >
                    <EyeOutlined />
                  </a-button>
                </div>
              </template>
            </template>
          </a-table>
        </div>
      </a-tab-pane>
    </a-tabs>

    <!-- 列设置弹窗 -->
    <a-modal
      v-model:visible="columnSettingsVisible"
      :title="currentColumnSettingsTitle"
      width="400px"
      :footer="null"
      @cancel="columnSettingsVisible = false"
      class="column-settings-modal"
    >
      <div class="column-settings-content">
        <div class="column-settings-header">
          <a-checkbox
            :indeterminate="isIndeterminate"
            :checked="isAllSelected"
            @change="onCheckAllChange"
          >
            全选
          </a-checkbox>
        </div>
        <div class="column-settings-body">
          <a-checkbox-group v-model:value="selectedColumns" style="width: 100%">
            <a-row>
              <a-col :span="8" v-for="col in currentAllColumns" :key="col.key">
                <a-checkbox :value="col.key">{{ col.title }}</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </div>
      </div>
    </a-modal>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:visible="detailModalVisible"
      :title="detailModalTitle"
      width="800px"
      :footer="null"
      @cancel="detailModalVisible = false"
    >
      <div class="detail-content">
        <pre class="json-content">{{ formatJson(detailContent) }}</pre>
      </div>
    </a-modal>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue';
import { SettingOutlined, EyeOutlined, DeleteOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import http from '@/utils/ai/http'; // 引入http工具
import { h } from 'vue';
import { Tag as ATag, Button as AButton } from 'ant-design-vue';
import qs from 'qs';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '关系确认'
  },
  taskId: {
    type: [String, Number],
    default: null
  }
});

const emit = defineEmits(['update:visible', 'close', 'refresh-graph']);

// 标签页相关
const activeTabKey = ref('api-model');

// 接口-数据模型关系相关
const apiModelSearch = ref('');
const apiModelLoading = ref(false);
const apiModelPage = ref(1);
const apiModelPageSize = ref(10);
const apiModelList = ref([]);

// 新增接口-数据模型关系筛选相关
const apiModelRelationFilter = ref([]);
const apiModelStatusFilter = ref([]);
const apiModelTagFilter = ref([]);

const apiModelRelationOptions = computed(() => {
  const relationSet = new Set(apiModelList.value.map(item => item.relationType));
  return Array.from(relationSet).map(type => ({ value: type, label: getRelationTypeText(type) }));
});

const apiModelStatusOptions = computed(() => {
  const statusSet = new Set(apiModelList.value.map(item => item.status));
  const statusLabelMap = {
    0: '待确认',
    1: '已嵌入',
    2: '已忽略'
  };
  return Array.from(statusSet).map(s => ({ value: s, label: statusLabelMap[s] ?? s }));
});

const apiModelTagOptions = computed(() => {
  const tagSet = new Set();
  apiModelList.value.forEach(item => {
    if (Array.isArray(item.api_tags)) {
      item.api_tags.forEach(tag => tagSet.add(tag));
    }
  });
  return Array.from(tagSet);
});

// 行选择相关
const apiModelSelectedRowKeys = ref([]); // 接口-数据模型关系选中项
const apiModelRowSelection = {
  selectedRowKeys: apiModelSelectedRowKeys,
  onChange: (keys) => {
    apiModelSelectedRowKeys.value = keys;
  }
};

// 获取接口-数据模型关系数据
const fetchApiModelRelations = async () => {
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }
  apiModelLoading.value = true; // 显示加载状态
  try {
    let url = `/knowledge_api/${relationId}/confirm_relations`;
    if (props.taskId) {
      url += `?task_id=${props.taskId}`;
    }
    const res = await http.get(url);
    if (res.data.code === 200) {
      apiModelList.value = res.data.data.map(item => ({
        id: item.id,
        taskId: item.task_id, // 映射 task_id
        apiName: item.api_summary, // 接口名称
        apiType: item.api_method || '', // 请求方法
        modelName: item.data_model, // 映射 data_model
        relationType: item.relation_type, // 映射 relation_type
        path: item.api_path || '', // 接口路径
        api_tags: Array.isArray(item.api_tags) ? item.api_tags : (typeof item.api_tags === 'string' ? item.api_tags.split(',').map(t => t.trim()).filter(Boolean) : []),
        tags: item.tags || [], // 假设tags也在响应中，如果没有则为空数组
        status: item.status, // 直接用原始status字段 0/1/2
        create_time: item.details?.timestamp || '', // 修正：从 details.timestamp 获取更新时间
        details: item.details || null // 映射 details
      }));
      modelModelList.value = res.data.component || [];
      apiModelSelectedRowKeys.value = []; // 每次重新加载数据时清空选中项
    } else {
      apiModelList.value = [];
      modelModelList.value = [];
    }
  } catch (error) {
    apiModelList.value = [];
    modelModelList.value = [];
    console.error('获取接口-数据模型关系失败:', error);
  } finally {
    apiModelLoading.value = false; // 隐藏加载状态
  }
};

// 数据模型-数据模型关系相关
const modelModelSearch = ref('');
const modelModelLoading = ref(false);
const modelModelPage = ref(1);
const modelModelPageSize = ref(10);
const modelModelList = ref([]); // 数据模型-数据模型关系表数据，来源于接口model_data

// 新增数据模型-数据模型关系筛选相关
const modelModelStatusFilter = ref([]);
const modelModelUpdateTimeFilter = ref([]);

const modelModelStatusOptions = computed(() => {
  const statusSet = new Set(modelModelList.value.map(item => item.status));
  const statusLabelMap = {
    0: '待确认',
    1: '已嵌入',
    2: '已忽略'
  };
  return Array.from(statusSet).map(s => ({ value: s, label: statusLabelMap[s] ?? s }));
});


// 行选择相关
const modelModelSelectedRowKeys = ref([]); // 数据模型-数据模型关系选中项
const modelModelRowSelection = {
  selectedRowKeys: modelModelSelectedRowKeys,
  onChange: (keys) => {
    modelModelSelectedRowKeys.value = keys;
  }
};

// 获取数据模型-数据模型关系数据
const fetchModelModelRelations = async () => {
  // 只清空选中项，不再赋值数据
  modelModelSelectedRowKeys.value = [];
};

// 列设置相关
const columnSettingsVisible = ref(false);
const currentColumnSettingsType = ref(''); // 'apiModel' 或 'modelModel'
const currentColumnSettingsTitle = computed(() => 
  currentColumnSettingsType.value === 'apiModel' ? '接口-数据模型关系列设置' : '数据模型-数据模型关系列设置'
);

// 当前选中的列 (用于列设置弹窗的 v-model)
const selectedColumns = ref([]);

// 接口-数据模型关系列配置
const apiModelAllColumns = [
  { title: 'ID', key: 'id' },
  { title: '任务ID', key: 'taskId' },
  { title: '标签', key: 'api_tags' },
  { title: '接口名称', key: 'apiName' },
  { title: '接口类型', key: 'apiType' },
  { title: '数据模型', key: 'modelName' },
  { title: '关系类型', key: 'relationType' },
  { title: '接口路径', key: 'path' },
  { title: '标签', key: 'tags' },
  { title: '状态', key: 'status' },
  { title: '更新时间', key: 'create_time' },
  { title: '匹配详情', key: 'details' },
];

// 数据模型-数据模型关系列配置
const modelModelAllColumns = [
  { title: 'ID', key: 'id' },
  { title: '数据模型', key: 'modelName' },
  { title: '父模型', key: 'parentModel' },
  { title: '子模型', key: 'childModel' },
  { title: '状态', key: 'status' },
  { title: '更新时间', key: 'create_time' },
];

// 监听 activeTabKey 变化，更新 selectedColumns 为当前标签页的默认选中列
watch(activeTabKey, (newTabKey) => {
  if (newTabKey === 'api-model') {
    selectedColumns.value = ['id', 'taskId', 'api_tags', 'apiName', 'apiType', 'modelName', 'relationType', 'path', 'status', 'details']; // 默认选中所有列，包括标签
  } else if (newTabKey === 'model-model') {
    selectedColumns.value = ['id', 'modelName', 'parentModel', 'childModel', 'status', 'create_time'];
  }
}, { immediate: true }); // immediate: true 确保在组件初始加载时也执行一次

// 计算当前显示的列配置
const currentAllColumns = computed(() => 
  currentColumnSettingsType.value === 'apiModel' ? apiModelAllColumns : modelModelAllColumns
);

// 接口-数据模型关系表格列
const apiModelColumns = computed(() => {
  const baseColumns = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 60, ellipsis: true },
    { title: '任务ID', dataIndex: 'taskId', key: 'taskId', width: 80, ellipsis: true },
    { 
      title: '标签', 
      dataIndex: 'api_tags', 
      key: 'api_tags', 
      width: 120, 
      ellipsis: true,
      customRender: ({ text }) => {
        if (!text || !Array.isArray(text) || text.length === 0) return '-';
        return h('div', { class: 'tags-container' }, text.map(tag => h(ATag, { color: getTagColor(tag) }, () => tag)));
      }
    },
    { title: '接口名称', dataIndex: 'apiName', key: 'apiName', width: 180, ellipsis: true }, // 修正 dataIndex 为 apiName
    { title: '请求方法', dataIndex: 'apiType', key: 'apiType', width: 100 },
    { title: '接口路径', dataIndex: 'path', key: 'path', width: 200, ellipsis: true },
    { title: '关系', dataIndex: 'relationType', key: 'relationType', width: 120 },
    { 
      title: '数据模型', 
      dataIndex: 'modelName', 
      key: 'modelName', 
      width: 150, 
      ellipsis: true,
      customRender: ({ text }) => {
        if (!text) return '-';
        return h('span', { class: 'preview-text' }, getPreviewText(text));
      }
    },
    { 
      title: '状态', 
      dataIndex: 'status', 
      key: 'status', 
      width: 80, 
      ellipsis: true,
      customRender: ({ text }) => {
        const statusText = getStatusText(text); // 使用getStatusText处理数值状态
        return h(ATag, { color: getStatusColor(text) }, () => statusText);
      }
    },
    { 
      title: '更新时间', 
      dataIndex: 'create_time', 
      key: 'create_time', 
      width: 150, 
      ellipsis: true,
      customRender: ({ text }) => {
        if (!text) return '-';
        const date = new Date(text);
        return date.toLocaleString();
      }
    },
    { 
      title: '匹配详情', 
      dataIndex: 'details', 
      key: 'details', 
      width: 100,
      customRender: ({ text, record }) => {
        if (!text) return '-';
        return h('div', { class: 'content-preview' }, [
          h('span', { class: 'preview-text' }, getPreviewText(text)),
          h(AButton, {
            type: 'link',
            class: 'view-btn',
            onClick: () => showDetailModal(record, 'apiModel_details') // 新的类型标识
          }, () => h(EyeOutlined))
        ]);
      }
    },
  ];
  return baseColumns.filter(col => selectedColumns.value.includes(col.key));
});

// 数据模型-数据模型关系表格列
const modelModelColumns = computed(() => {
  return [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 60, ellipsis: true },
    { title: '源模型', dataIndex: 'data_model_name', key: 'data_model_name', width: 150, ellipsis: true, customRender: ({ text }) => text || '-' },
    { title: '关系', dataIndex: 'relation_type', key: 'relation_type', width: 100, ellipsis: true, customRender: ({ text }) => text || '-' },
    { title: '目标模型', dataIndex: 'child_model', key: 'child_model', width: 150, ellipsis: true, customRender: ({ text }) => text || '-' },
    { title: '状态', dataIndex: 'status', key: 'status', width: 80, ellipsis: true, customRender: ({ text }) => { if (text === undefined || text === null) return '-'; return h(ATag, { color: getStatusColor(text) }, () => getStatusText(text)); } },
    { title: '更新时间', dataIndex: 'create_time', key: 'create_time', width: 150, ellipsis: true, customRender: ({ text }) => { if (!text) return '-'; const date = new Date(text); return date.toLocaleString(); } }
  ];
});

// 详情弹窗相关
const detailModalVisible = ref(false);
const detailModalTitle = ref('');
const detailContent = ref(null);

// 方法
const handleClose = () => {
  emit('update:visible', false);
  emit('close');
};

// 获取关系类型颜色
const getRelationTypeColor = (type) => {
  const colorMap = {
    'parameter': 'blue',
    'requestBody': 'green',
    'response': 'purple'
  };
  return colorMap[type] || 'default';
};

// 获取关系类型文本
const getRelationTypeText = (type) => {
  const textMap = {
    'parameter': '参数',
    'requestBody': '请求体',
    'response': '响应'
  };
  return textMap[type] || type;
};

// 获取API类型颜色
const getApiTypeColor = (type) => {
  const colorMap = {
    'GET': '#52c41a',
    'POST': '#1890ff',
    'PUT': '#faad14',
    'DELETE': '#ff4d4f',
    'PATCH': '#722ed1'
  };
  return colorMap[type] || '#d9d9d9';
};

// 获取模型关系类型颜色
const getModelRelationTypeColor = (type) => {
  const colorMap = {
    'contains': 'green',
    'extends': 'blue',
    'implements': 'purple',
    'references': 'orange'
  };
  return colorMap[type] || 'default';
};

// 获取模型关系类型文本
const getModelRelationTypeText = (type) => {
  const textMap = {
    'contains': '包含',
    'extends': '继承',
    'implements': '实现',
    'references': '引用'
  };
  return textMap[type] || type;
};

// 显示列设置
const showColumnSettings = (type) => {
  currentColumnSettingsType.value = type;
  // 初始化 selectedColumns 为当前显示的列的 key，确保弹窗状态与表格当前显示状态一致
  if (type === 'apiModel') {
    selectedColumns.value = apiModelColumns.value.map(col => col.key);
  } else {
    selectedColumns.value = modelModelColumns.value.map(col => col.key);
  }
  columnSettingsVisible.value = true;
};

// 列设置确认
const handleColumnSettingsConfirm = () => {
  columnSettingsVisible.value = false;
};

// 全选状态
const isIndeterminate = computed(() => {
  return selectedColumns.value.length > 0 && selectedColumns.value.length < currentAllColumns.value.length;
});

const isAllSelected = computed(() => {
  return selectedColumns.value.length === currentAllColumns.value.length;
});

// 全选/取消全选
const onCheckAllChange = (e) => {
  selectedColumns.value = e.target.checked ? currentAllColumns.value.map(col => col.key) : [];
};

// 显示详情弹窗
const showDetailModal = (record, type) => {
  detailModalVisible.value = true;
  detailContent.value = null; // 重置内容

  switch (type) {
    case 'apiModel': // 针对原始的apiModel表格的详情列
      detailModalTitle.value = '接口-数据模型关系详情';
      detailContent.value = record.details;
      break;
    case 'apiModel_modelName': // 针对apiModel表格中数据模型列的预览
      detailModalTitle.value = `接口数据模型: ${record.modelName || ''} 详情`; // 修正为 record.modelName
      detailContent.value = record.details; // 假设 details 包含相关 JSON
      break;
    case 'apiModel_details': // 针对apiModel表格中新增的详情列
      detailModalTitle.value = '详情内容';
      detailContent.value = record.details;
      break;
    case 'modelName': // 针对modelModel表格中数据模型列的预览
      detailModalTitle.value = `数据模型: ${record.modelName || ''} 详情`;
      detailContent.value = record.details;
      break;
    case 'parentModel':
      detailModalTitle.value = `父模型: ${record.parentModel || ''} 详情`;
      detailContent.value = record.details;
      break;
    case 'childModel':
      detailModalTitle.value = `子模型: ${record.childModel || ''} 详情`;
      detailContent.value = record.details;
      break;
    default:
      detailModalTitle.value = '详情';
      detailContent.value = null;
  }
};

// 获取预览文本
const getPreviewText = (content) => {
  if (!content) return '-';
  const text = typeof content === 'string' ? content : JSON.stringify(content);
  return text.length > 50 ? text.substring(0, 50) + '...' : text;
};

// 格式化JSON
const formatJson = (content) => {
  if (!content) return '';
  try {
    const json = typeof content === 'string' ? JSON.parse(content) : content;
    return JSON.stringify(json, null, 2);
  } catch (e) {
    return content;
  }
};

// 搜索方法
const onApiModelSearch = () => {
  apiModelPage.value = 1;
};

const onApiModelRelationFilterChange = () => {
  apiModelPage.value = 1;
};

const onApiModelStatusFilterChange = () => {
  apiModelPage.value = 1;
};

const onApiModelTagFilterChange = () => {
  apiModelPage.value = 1;
};

const onModelModelSearch = () => {
  modelModelPage.value = 1;
};

const onModelModelStatusFilterChange = () => {
  modelModelPage.value = 1;
};

// 分页方法
const onPageChange = (page, size, type) => {
  if (type === 'apiModel') {
    apiModelPage.value = page;
    apiModelPageSize.value = size;
  } else {
    modelModelPage.value = page;
    modelModelPageSize.value = size;
  }
};

const onPageSizeChange = (current, size, type) => {
  if (type === 'apiModel') {
    apiModelPage.value = 1;
    apiModelPageSize.value = size;
  } else {
    modelModelPage.value = 1;
    modelModelPageSize.value = size;
  }
};

// 防抖搜索
const debouncedSearch = (() => {
  let timer = null;
  return (value, type) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      if (type === 'apiModel') {
        apiModelSearch.value = value;
        apiModelPage.value = 1;
      } else {
        modelModelSearch.value = value;
        modelModelPage.value = 1;
      }
    }, 300);
  };
})();

// 计算分页后的列表
const apiModelPagedList = computed(() => {
  const start = (apiModelPage.value - 1) * apiModelPageSize.value;
  return apiModelFilteredList.value.slice(start, start + apiModelPageSize.value);
});

const modelModelPagedList = computed(() => {
  const start = (modelModelPage.value - 1) * modelModelPageSize.value;
  return modelModelFilteredList.value.slice(start, start + modelModelPageSize.value);
});

// 新增接口-数据模型关系的筛选逻辑
const apiModelFilteredList = computed(() => {
  let list = apiModelList.value;

  if (apiModelRelationFilter.value.length > 0) {
    list = list.filter(item => apiModelRelationFilter.value.includes(item.relationType));
  }

  if (apiModelStatusFilter.value.length > 0) {
    list = list.filter(item => apiModelStatusFilter.value.includes(item.status));
  }

  if (apiModelTagFilter.value.length > 0) {
    list = list.filter(item => {
      return item.api_tags && apiModelTagFilter.value.some(tag => item.api_tags.includes(tag));
    });
  }

  if (apiModelSearch.value) {
    const searchText = apiModelSearch.value.toLowerCase().trim();
    list = list.filter(item => {
      const id = (item.id || '').toLowerCase();
      const apiName = (item.apiName || '').toLowerCase();
      const modelName = (item.modelName || '').toLowerCase();
      const path = (item.path || '').toLowerCase();
      return id.includes(searchText) || 
             apiName.includes(searchText) || 
             modelName.includes(searchText) ||
             path.includes(searchText);
    });
  }
  return list;
});

// 新增数据模型-数据模型关系的筛选逻辑
const modelModelFilteredList = computed(() => {
  let list = modelModelList.value;

  if (modelModelStatusFilter.value.length > 0) {
    list = list.filter(item => modelModelStatusFilter.value.includes(item.status));
  }

  if (modelModelUpdateTimeFilter.value.length > 0) {
    list = list.filter(item => {
      if (!item.create_time) return false;
      const itemDate = new Date(item.create_time).toLocaleDateString();
      return modelModelUpdateTimeFilter.value.includes(itemDate);
    });
  }

  if (modelModelSearch.value) {
    const searchText = modelModelSearch.value.toLowerCase().trim();
    list = list.filter(item => {
      const id = (item.id || '').toLowerCase();
      const modelName = (item.modelName || '').toLowerCase();
      const parentModel = (item.parentModel || '').toLowerCase();
      const childModel = (item.childModel || '').toLowerCase();

      return id.includes(searchText) || 
             modelName.includes(searchText) || 
             parentModel.includes(searchText) || 
             childModel.includes(searchText);
    });
  }
  return list;
});

// 计算总数
const apiModelTotal = computed(() => apiModelFilteredList.value.length);
const modelModelTotal = computed(() => modelModelFilteredList.value.length);

// 为表格添加动态key，以强制重新渲染
const apiModelTableKey = computed(() => selectedColumns.value.join('-'));
const modelModelTableKey = computed(() => selectedColumns.value.join('-'));

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    fetchApiModelRelations();
    fetchModelModelRelations();
  }
});

// 批量删除接口-数据模型关系
const handleBatchDeleteApiModel = async () => {
  if (apiModelSelectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的接口-数据模型关系');
    return;
  }
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }
  try {
    const res = await http.delete(`/knowledge_api/${relationId}/delete_relations`, {
      data: { ids: apiModelSelectedRowKeys.value }
    });
    if (res.data.code === 200) {
      message.success(res.data.message || '批量删除成功');
      fetchApiModelRelations(); // 刷新列表
      apiModelSelectedRowKeys.value = [];
      emit('update:visible', false);
      emit('close');
    } else {
      message.error(res.data.message || '批量删除失败');
    }
  } catch (error) {
    message.error('批量删除失败');
    console.error('批量删除失败:', error);
  }
};

// 批量删除数据模型-数据模型关系
const handleBatchDeleteModelModel = () => {
  if (modelModelSelectedRowKeys.value.length === 0) {
    message.warning('请选择要删除的数据模型-数据模型关系');
    return;
  }
  console.log('批量删除数据模型-数据模型关系:', modelModelSelectedRowKeys.value);
  // 这里可以添加实际的API调用来执行删除操作
  // 例如:
  // http.post(`/knowledge_api/batch_delete_model_model_relations`, { ids: modelModelSelectedRowKeys.value })
  //   .then(() => {
  //     message.success('删除成功');
  //     fetchModelModelRelations(); // 刷新列表
  //   })
  //   .catch(error => {
  //     message.error('删除失败');
  //     console.error('删除失败:', error);
  //   });
};

// 批量确认接口-数据模型关系
const handleBatchConfirmApiModel = async () => {
  if (apiModelSelectedRowKeys.value.length === 0) {
    message.warning('请选择要确认的接口-数据模型关系');
    return;
  }
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }
  try {
    const res = await http.post(`/knowledge_api/${relationId}/confirm_and_insert_neo4j`, {
      ids: apiModelSelectedRowKeys.value
    });
    if (res.data && res.data.code === 200) {
      message.success(res.data.message || '批量确认成功');
      fetchApiModelRelations(); // 刷新列表
      apiModelSelectedRowKeys.value = [];
      emit('refresh-graph');
    } else {
      message.error(res.data?.message || '批量确认失败');
    }
  } catch (error) {
    message.error('批量确认失败');
    console.error('批量确认失败:', error);
  }
};

// 批量忽略接口-数据模型关系
const handleBatchIgnoreApiModel = async () => {
  if (apiModelSelectedRowKeys.value.length === 0) {
    message.warning('请选择要忽略的接口-数据模型关系');
    return;
  }
  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];
  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }
  try {
    const res = await http.post(`/knowledge_api/${relationId}/ignore_relations`, {
      ids: apiModelSelectedRowKeys.value
    });
    if (res.data.code === 200) {
      message.success(res.data.message || '批量忽略成功');
      fetchApiModelRelations(); // 刷新列表
      apiModelSelectedRowKeys.value = [];
      emit('update:visible', false);
      emit('close');
    } else {
      message.error(res.data.message || '批量忽略失败');
    }
  } catch (error) {
    message.error('批量忽略失败');
    console.error('批量忽略失败:', error);
  }
};

// 批量确认数据模型-数据模型关系
const handleBatchConfirmModelModel = () => {
  if (modelModelSelectedRowKeys.value.length === 0) {
    message.warning('请选择要确认的数据模型-数据模型关系');
    return;
  }
  console.log('批量确认数据模型-数据模型关系:', modelModelSelectedRowKeys.value);
  // 这里可以添加实际的API调用来执行确认操作
  // 例如:
  // http.post(`/knowledge_api/batch_confirm_model_model_relations`, { ids: modelModelSelectedRowKeys.value })
  //   .then(() => {
  //     message.success('确认成功');
  //     fetchModelModelRelations(); // 刷新列表
  //   })
  //   .catch(error => {
  //     message.error('确认失败');
  //     console.error('确认失败:', error);
  //   });
};

// 批量忽略数据模型-数据模型关系
const handleBatchIgnoreModelModel = () => {
  if (modelModelSelectedRowKeys.value.length === 0) {
    message.warning('请选择要忽略的数据模型-数据模型关系');
    return;
  }
  console.log('批量忽略数据模型-数据模型关系:', modelModelSelectedRowKeys.value);
  // 这里可以添加实际的API调用来执行忽略操作
  // 例如:
  // http.post(`/knowledge_api/batch_ignore_model_model_relations`, { ids: modelModelSelectedRowKeys.value })
  //   .then(() => {
  //     message.success('忽略成功');
  //     fetchModelModelRelations(); // 刷新列表
  //   })
  //   .catch(error => {
  //     message.error('忽略失败');
  //     console.error('忽略失败:', error);
  //   });
};

// 标签颜色映射 (从ApiWindow复制)
const tagColors = [
  '#1890ff', // 蓝色
  '#faad14', // 黄色
  '#13c2c2', // 青色
  '#eb2f96', // 粉色
  '#a0d911', // 浅绿
  '#2f54eb', // 极客蓝
  '#faad14', // 金色
  '#13c2c2', // 青色
  '#fa8c16', // 橙色
  '#a0d911', // 浅绿
  '#2f54eb', // 极客蓝
  '#1890ff', // 蓝色
];

// 标签颜色缓存 (从ApiWindow复制)
const tagColorCache = new Map();

// 获取标签颜色 (从EntityWindow复制)
const getTagColor = (tag) => {
  // 如果标签已经在缓存中，直接返回缓存的颜色
  if (tagColorCache.has(tag)) {
    return tagColorCache.get(tag);
  }

  // 为标签分配一个随机颜色
  const randomColor = tagColors[Math.floor(Math.random() * tagColors.length)];
  
  // 将颜色存入缓存
  tagColorCache.set(tag, randomColor);
  
  return randomColor;
};

// 获取状态颜色 (从EntityWindow复制)
const getStatusColor = (status) => {
  const colorMap = {
    0: 'orange',    // 待确认 - 橙色
    1: 'success',   // 已嵌入 - 绿色
    2: 'default'    // 已忽略 - 灰色
  };
  return colorMap[status] || 'default';
};

// 获取状态文本 (从EntityWindow复制)
const getStatusText = (status) => {
  const textMap = {
    0: '待确认',
    1: '已嵌入',
    2: '已忽略'
  };
  return textMap[status] ?? status;
};

// 在组件卸载时清除颜色缓存
onUnmounted(() => {
  tagColorCache.clear();
});
</script>

<style scoped>
.tab-content {
  padding: 0;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.left-section {
  display: flex;
  gap: 12px;
  align-items: center;
}

.right-section {
  display: flex;
  gap: 8px;
}

/* 自定义列按钮样式 */
.column-settings-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.column-settings-btn :deep(.anticon) {
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 14px;
}

/* 自定义列弹窗样式 */
.column-settings-modal :deep(.ant-modal-content) {
  border-radius: 8px;
  overflow: hidden;
}

.column-settings-modal :deep(.ant-modal-header) {
  margin-bottom: 0;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.column-settings-modal :deep(.ant-modal-title) {
  font-size: 16px;
  font-weight: 500;
}

.column-settings-content {
  padding: 16px 24px;
}

.column-settings-header {
  padding-bottom: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.column-settings-body {
  max-height: 300px;
  overflow-y: auto;
  padding-right: 8px;
}

.column-settings-body::-webkit-scrollbar {
  width: 6px;
}

.column-settings-body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.column-settings-body::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 内容预览样式 */
.content-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  width: 100%;
}

.preview-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 170px;
}

.view-btn {
  padding: 0;
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  transition: all 0.3s;
}

.view-btn:hover {
  color: #40a9ff;
  transform: scale(1.1);
}

/* 详情弹窗样式 */
.detail-content {
  max-height: 60vh;
  overflow-y: auto;
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
}

.json-content {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #333;
}

/* 美化滚动条 */
.detail-content::-webkit-scrollbar {
  width: 6px;
}

.detail-content::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.detail-content::-webkit-scrollbar-track {
  background-color: transparent;
}

/* 批量操作按钮样式 */
.batch-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.batch-btn :deep(.anticon) {
  display: flex;
  align-items: center;
  margin-right: 4px;
  font-size: 14px;
}

.batch-btn[type="danger"]:not([disabled]) {
  background-color: #ff4d4f;
  border-color: #ff4d4f;
  color: #fff;
}

.batch-btn[type="danger"]:not([disabled]):hover {
  background-color: #ff7875;
  border-color: #ff7875;
}

.batch-btn[disabled] {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: rgba(0, 0, 0, 0.25) !important;
  cursor: not-allowed;
}

.batch-confirm-green-btn {
  background-color: #52c41a !important;
  border-color: #52c41a !important;
  color: #fff !important;
}

.batch-confirm-green-btn:not([disabled]):hover {
  background-color: #73d13d !important;
  border-color: #73d13d !important;
}

.batch-ignore-blue-btn {
  background-color: #1890ff !important;
  border-color: #1890ff !important;
  color: #fff !important;
}

.batch-ignore-blue-btn:not([disabled]):hover {
  background-color: #40a9ff !important;
  border-color: #40a9ff !important;
}

.batch-delete-red-btn {
  background-color: #ff4d4f !important;
  border-color: #ff4d4f !important;
  color: #fff !important;
}

.batch-delete-red-btn:not([disabled]):hover {
  background-color: #ff7875 !important;
  border-color: #ff7875 !important;
}

.right-section {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* 标签容器样式 (从EntityWindow复制) */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}
</style>
