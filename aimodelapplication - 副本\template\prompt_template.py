class PromptTemplate:
    @staticmethod
    def api_component_relationship(api_doc: str, component_doc: str) -> str:
        """
        生成接口与数据模型关系抽取的提示词，api和component内容可参数化。
        :param api_doc: API文档内容
        :param component_doc: 数据模型文档内容
        :return: 完整的提示词字符串
        """
        prompt = f'''
        你是一个接口文档的实体关系抽取专家，专注于从接口(api)和数据模型组件(component)文档中抽取二者之间的关系。

        【任务说明】
        1. 给定一个API文档和若干数据模型文档，请找出API与数据模型组件之间的关系，并用标准格式输出。
        2. 只输出JSON结果，**不要输出任何解释、说明、分析、步骤、前后缀等内容**。
        3. 只能使用component_doc中明确存在的数据模型组件名称和字段，不允许添加、推断或编造任何未列出的数据模型组件或字段
        4. 只能使用api_doc中明确存在的接口参数和返回参数，不允许添加、推断或编造任何未列出的参数。
    
        
        【关系类型定义（数据双向流：接口↔模型↔接口）】  
        # BY关系（数据下行）：
            ## 定义特征
                - **数据生产者**：接口响应(Response)作为数据源
                - **数据消费者**：数据模型(Component)存储字段
                - **方向性**：单向流动 `API → Component`
                - **格式规范**： "api.[HTTP方法:路径].[响应字段]": "component.[模型名].[存储字段]"
            ## 示例
                - **创建用户接口和用户数据模型组件**：api/createUser接口的Response中包含id、username字段，apiUser组件中包含id、username字段
        # QUTO关系（数据上行）：
            ## 定义特征
                - **数据生产者**：数据模型(Component)存储字段  
                - **数据消费者**：接口请求(Request)参数
                - **方向性**：单向流动 `Component → API`
                - **格式规范**： "component.[模型名].[存储字段]": "api.[HTTP方法:路径].[请求参数]"
            ## 示例
                - **支付订单接口和订单模型组件**：api/order接口的Response中包含id字段，apiOrder组件中包含orderId字段
        
        【关系核心约束（优先级从高到低）】  
        # 语义映射：根据api内容和component内容（包括但不限于命名、描述信息、字段名的语义推测等方式）相似度进行映射
        # 字段映射：满足语义映射的前提下，匹配的字段越多越越匹配
        
        【数据】
        - API文档如下：
        {api_doc}
        - 数据模型文档如下：
        {component_doc}

        【输出要求】
        - 只输出JSON，**不要输出任何解释、说明、分析、步骤、前后缀等内容**，格式如下（parameters为api参数与数据模型参数的映射）：
        - 其中parameters为api参数与数据模型参数的映射，格式为：api.response.200.参数名:component.模型名称.properties（enum）.参数名(其中模型名称为component_doc的entity_name字段）
        - 参数映射必须严格遵守以下格式：
            - BY 类型：`"api.路径.字段名": "component.模型名.字段名"` （API -> Component）
            - QUTO 类型：`"component.模型名.字段名": "api.路径.字段名"` （Component -> API）
        - 不允许调换键值对顺序，否则视为错误。
        
        {{
          "relationships": [
            {{
              "model_name": "数据模型名称",
              "relationship": "关系类型，如BY、QUTO",
              "mapping_reason": "语义映射",
              "parameters": {{
                "api参数1": "模型参数1",
                "api参数2": "模型参数2"
              }}    
            }}
          ]
        }}

        【BY类型示例】（API -> Component)
        {{
          "relationships": [
            {{
              "model_name": "User",
              "relationship": "BY",
              "mapping_reason": "语义映射",
              "parameters": {{
                "api.response.userId": "Component.模型名.userId",
                "api.response.userName": "Component.模型名.userName"
              }}
            }}
          ]
        }}
        【QUTO关系示例】（Component → API）：
        {{
          "relationships": [
            {{
              "model_name": "Servicepool",
              "relationship": "QUTO",
              "mapping_reason": "语义映射",
              "parameters": {{
                "Component.模型名.userId": "api.parameters.userId",
                "Component.模型名.userName": "api.request_body.userName"
              }}
            }}
          ]
        }}
        '''
        return prompt
