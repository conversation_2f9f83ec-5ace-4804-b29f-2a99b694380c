2025-04-25 11:00:30 | ERROR | 初始化Neo4j驱动失败: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 1225] 远程计算机拒绝网络连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 1225] 远程计算机拒绝网络连接。)
2025-04-25 11:02:17 | ERROR | 初始化Neo4j驱动失败: type object 'Neo4jPool' has no attribute 'get_instance'
2025-04-25 11:02:45 | ERROR | 初始化Neo4j驱动失败: 'Neo4jPool' object has no attribute 'get_driver'
2025-04-25 11:03:18 | ERROR | 初始化Neo4j驱动失败: 'coroutine' object has no attribute 'verify_connectivity'
2025-04-25 11:03:18 | ERROR | 初始化Neo4j驱动失败: 'coroutine' object has no attribute 'verify_connectivity'
2025-04-25 11:03:43 | INFO | 正在连接Neo4j: bolt://localhost:7687
2025-04-25 11:03:43 | INFO | 正在连接Neo4j: bolt://localhost:7687
2025-04-25 11:03:47 | ERROR | Neo4j驱动初始化失败: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 1225] 远程计算机拒绝网络连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 1225] 远程计算机拒绝网络连接。)
2025-04-25 11:03:47 | ERROR | 初始化Neo4j驱动失败: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 1225] 远程计算机拒绝网络连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 1225] 远程计算机拒绝网络连接。)
2025-04-25 11:03:47 | ERROR | Neo4j驱动初始化失败: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 1225] 远程计算机拒绝网络连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 1225] 远程计算机拒绝网络连接。)
2025-04-25 11:03:47 | ERROR | 初始化Neo4j驱动失败: Couldn't connect to localhost:7687 (resolved to ('[::1]:7687', '127.0.0.1:7687')):
Failed to establish connection to ResolvedIPv6Address(('::1', 7687, 0, 0)) (reason [WinError 1225] 远程计算机拒绝网络连接。)
Failed to establish connection to ResolvedIPv4Address(('127.0.0.1', 7687)) (reason [WinError 1225] 远程计算机拒绝网络连接。)
2025-04-25 11:04:22 | INFO | 正在连接Neo4j: bolt://*************:7687
2025-04-25 11:04:22 | INFO | 正在连接Neo4j: bolt://*************:7687
2025-04-25 11:04:22 | INFO | Neo4j连接成功！
2025-04-25 11:04:22 | INFO | Neo4j连接成功！
2025-04-25 11:04:22 | INFO | Neo4j连接成功！
2025-04-25 11:04:22 | INFO | Neo4j连接成功！
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74-e89b-4d95-b144-aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:04:22 | ERROR | 查找所有节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (n:9a693d74-e89b-4d95-b144-aab0e6f756fd)"
                              ^}
2025-04-25 11:04:22 | ERROR | 查找所有节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (n:9a693d74-e89b-4d95-b144-aab0e6f756fd)"
                              ^}
2025-04-25 11:04:22 | ERROR | 根据属性查找节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {name: $value})"
                              ^}
2025-04-25 11:04:22 | ERROR | 根据属性查找节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (n:9a693d74-e89b-4d95-b144-aab0e6f756fd {name: $value})"
                              ^}
2025-04-25 11:05:01 | INFO | 正在连接Neo4j: bolt://*************:7687
2025-04-25 11:05:01 | INFO | 正在连接Neo4j: bolt://*************:7687
2025-04-25 11:05:01 | INFO | Neo4j连接成功！
2025-04-25 11:05:01 | INFO | Neo4j连接成功！
2025-04-25 11:05:01 | INFO | Neo4j连接成功！
2025-04-25 11:05:01 | INFO | Neo4j连接成功！
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, method: $method, path: $path, description: $description, type: $type})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 31 (offset: 31))
"                    CREATE (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $id, name: $name, type: $type, required: $required, description: $description})"
                               ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 创建关系失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (a:9a693d74_e89b_4d95_b144_aab0e6f756fd {id: $from_id})"
                              ^}
2025-04-25 11:05:01 | ERROR | 查找所有节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (n:9a693d74_e89b_4d95_b144_aab0e6f756fd)"
                              ^}
2025-04-25 11:05:01 | ERROR | 查找所有节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (n:9a693d74_e89b_4d95_b144_aab0e6f756fd)"
                              ^}
2025-04-25 11:05:01 | ERROR | 根据属性查找节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {name: $value})"
                              ^}
2025-04-25 11:05:01 | ERROR | 根据属性查找节点失败: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '9a693d74_e89b_4d95_b144_aab0e6f756fd': expected a node label/relationship type name, '$', '%' or '(' (line 2, column 30 (offset: 30))
"                    MATCH (n:9a693d74_e89b_4d95_b144_aab0e6f756fd {name: $value})"
                              ^}
2025-04-25 11:05:50 | INFO | 正在连接Neo4j: bolt://*************:7687
2025-04-25 11:05:50 | INFO | 正在连接Neo4j: bolt://*************:7687
2025-04-25 11:05:50 | INFO | Neo4j连接成功！
2025-04-25 11:05:50 | INFO | Neo4j连接成功！
2025-04-25 11:05:50 | INFO | Neo4j连接成功！
2025-04-25 11:05:50 | INFO | Neo4j连接成功！
2025-04-25 11:38:03 | INFO | 正在连接Neo4j: bolt://*************:7687
2025-04-25 11:38:03 | INFO | 正在连接Neo4j: bolt://*************:7687
2025-04-25 11:38:03 | INFO | Neo4j连接成功！
2025-04-25 11:38:03 | INFO | Neo4j连接成功！
2025-04-25 11:38:03 | INFO | Neo4j连接成功！
2025-04-25 11:38:03 | INFO | Neo4j连接成功！
