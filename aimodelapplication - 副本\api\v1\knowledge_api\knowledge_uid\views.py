from fastapi import APIRouter, Path, Query, Body, Form, File, UploadFile
from fastapi.responses import JSONResponse
from logger import logging
from template.response_temp import BaseResponse
from db.mysql.models.knowledge_dify_mapping import KnowledgeDifyMappingModel
from service.dify.dify import Dify, DifyAdmin
from service.dify.dify import DifyAPIKey
from typing import Dict, Any
import os

router = APIRouter()
logger = logging()


@router.post("/retrieve", response_model=BaseResponse)
async def retrieve(
    knowledge_uid: str = Path(..., description="知识库ID"),
    data: Dict[str, Any] = Body(..., description="请求数据")
):
    """
    检索知识库
    
    Args:
        knowledge_uid: 知识库ID
        data: 请求数据，包含以下字段：
            - username: 用户名【必填】
            - user_id: 用户ID【必填】
            - query: 检索内容【必填】
            - search_method: 检索方式【选填】，支持以下模式：
                - hybrid_search: 混合检索（默认）
                - keyword_search: 关键词检索
                - full_text_search: 全文检索
                - semantic_search: 向量检索
            - top_k: 返回结果数量【选填】，默认为5
            - reranking_mode: 重排序模式【选填】
            - reranking_provider_name: 重排序模型提供商【选填】
            - reranking_model_name: 重排序模型名称【选填】
            - weights: 权重设置【选填】，包含：
                - weight_type: 权重类型，固定为"customized"
                - vector_setting: 向量设置
                    - vector_weight: 向量权重
                    - embedding_provider_name: 向量模型提供商
                    - embedding_model_name: 向量模型名称
                - keyword_setting: 关键词设置
                    - keyword_weight: 关键词权重
            - score_threshold_enabled: 是否启用阈值【选填】，默认为False
            - score_threshold: 相似度阈值【选填】
            
    Returns:
        BaseResponse: 响应对象，包含：
            - code: 状态码
                - 200: 成功
                - 400: 参数错误
                - 404: 未找到用户Dify账号
                - 500: 服务器错误
            - message: 响应消息
            - data: 响应数据，包含检索结果
    """
    try:
        # 检查必要参数
        required_fields = ["username", "user_id", "query"]
        for field in required_fields:
            if field not in data:
                return BaseResponse(
                    code=400,
                    message=f"缺少必要参数: {field}",
                    data={}
                )
        # 获取参数
        username = data.get("username")
        user_id = data.get("user_id")
        query = data.get("query")
        search_method = data.get("search_method", "hybrid_search")
        top_k = data.get("top_k", 5)
        reranking_mode = data.get("reranking_mode", None)
        reranking_provider_name = data.get("reranking_provider_name", "")
        reranking_model_name = data.get("reranking_model_name", "")
        weights = {
                      "weight_type": "customized",
                      "vector_setting": {
                        "vector_weight": data.get("vector_weight", 0.3),
                        "embedding_provider_name": "",
                        "embedding_model_name": ""
                      },
                      "keyword_setting": {
                        "keyword_weight": data.get("keyword_weight", 0.7)
                      }
                  }
        score_threshold_enabled = data.get("score_threshold_enabled", False)
        # 阈值设置
        score_threshold = data.get("score_threshold", None)

        # 关键词检索
        if search_method == "keyword_search":
            retrieval_model = {
                "search_method": search_method,
                "reranking_enable": True if reranking_mode else False,
                "reranking_mode": reranking_mode,
                "reranking_model": {
                    "reranking_provider_name": reranking_provider_name,
                    "reranking_model_name": reranking_model_name
                },
                "weights": [],
                "top_k": top_k,
                "score_threshold_enabled": True if score_threshold else False,
                "score_threshold": score_threshold
            }

        # 全文检索
        elif search_method == "full_text_search":
            retrieval_model = {
                "search_method": search_method,
                "reranking_enable": True if reranking_mode else False,
                "reranking_mode": reranking_mode,
                "reranking_model": {
                    "reranking_provider_name": reranking_provider_name,
                    "reranking_model_name": reranking_model_name
                },
                "weights": [],
                "top_k": top_k,
                "score_threshold_enabled": True if score_threshold else False,
                "score_threshold": score_threshold
            }

        # 混合检索模式
        elif search_method == "hybrid_search":
            rerank_enable = True if reranking_mode else False
            retrieval_model = {
                "search_method": "hybrid_search",
                "reranking_enable": rerank_enable,
                "reranking_mode": reranking_mode,
                "reranking_model": {
                    "reranking_provider_name": reranking_provider_name,
                    "reranking_model_name": reranking_model_name
                },
                "weights": None,
                "top_k": top_k,
                "score_threshold_enabled": score_threshold_enabled,
                "score_threshold": score_threshold
            }
            # rerank模式
            if rerank_enable:
                retrieval_model = retrieval_model
            else:
                retrieval_model = {
                    "search_method": "hybrid_search",
                    "reranking_enable": rerank_enable,
                    "reranking_mode": reranking_mode,
                    "reranking_model": {
                        "reranking_provider_name": "",
                        "reranking_model_name": ""
                    },
                    "weights": weights,
                    "top_k": top_k,
                    "score_threshold_enabled": True if score_threshold else False,
                    "score_threshold": score_threshold
                }

        # 向量检索模式
        elif search_method == "semantic_search":
            retrieval_model = {
                "search_method": search_method,
                "reranking_enable": True if reranking_mode else False,
                "reranking_mode": reranking_mode,
                "reranking_model": {
                    "reranking_provider_name": reranking_provider_name,
                    "reranking_model_name": reranking_model_name
                },
                "weights": None,
                "top_k": top_k,
                "score_threshold_enabled": True if score_threshold else False,
                "score_threshold": score_threshold
            }

        else:
            return BaseResponse(
                code=400,
                message=f"检索方式不支持: {search_method}",
                data={}
            )
        # 查询数据库获取dify_email
        knowledge_dify_mapping = KnowledgeDifyMappingModel()
        try:
            await knowledge_dify_mapping.initialize()
            record = await knowledge_dify_mapping.find_by_userid_and_username(user_id, username)
            
            if not record:
                return BaseResponse(
                    code=404,
                    message="未找到用户对应的Dify账号信息",
                    data={}
                )
                
            dify_email = record[0].get("dify_email")
            
        except Exception as e:
            logger.error(f"查询Dify账号信息失败: {str(e)}")
            return BaseResponse(
                code=500,
                message="查询Dify账号信息失败",
                data={}
            )
        finally:
            await knowledge_dify_mapping.close()
        
        # 创建DifyAPIKey实例并检索知识库
        dify = DifyAPIKey(email=dify_email, username=username)
        status_code, response_data = await dify.retrieve(
            knowledge_uid=knowledge_uid,
            query=query,
            retrieval_model=retrieval_model
        )
        
        if status_code == 200:
            return BaseResponse(
                code=200,
                message="检索知识库成功",
                data=response_data
            )
        else:
            return BaseResponse(
                code=status_code,
                message="检索知识库失败",
                data=response_data
            )
            
    except Exception as e:
        logger.error(f"检索知识库失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"检索知识库失败: {str(e)}",
            data={}
        )

@router.get("/recall_test_history", response_model=BaseResponse)
async def recall_test_history(
    knowledge_uid: str = Path(..., description="知识库ID"),
    username: str = Query(..., description="用户名"),
    user_id: str = Query(..., description="用户ID")
):
    """
    获取召回历史
    Args:
        knowledge_uid: 知识库ID
        username: 用户名
        user_id: 用户ID
            
    Returns:
        BaseResponse: 响应对象
    """
    try:    
        # 查询数据库获取dify_email和dify_password
        knowledge_dify_mapping = KnowledgeDifyMappingModel()
        try:
            await knowledge_dify_mapping.initialize()
            record = await knowledge_dify_mapping.find_by_userid_and_username(user_id, username)
            
            if not record:
                return BaseResponse(
                    code=404,
                    message="未找到用户对应的Dify账号信息",
                    data={}
                )
                
            dify_email = record[0].get("dify_email")
            dify_password = record[0].get("dify_password")
            
        except Exception as e:
            logger.error(f"查询Dify账号信息失败: {str(e)}")
            return BaseResponse(
                code=500,
                message="查询Dify账号信息失败",
                data={}
            )
        finally:
            await knowledge_dify_mapping.close()
        
        # 创建Dify实例并获取召回历史
        dify = Dify(email=dify_email, password=dify_password)
        status_code, response_data = await dify.recall_test_history(knowledge_uid)
        
        if status_code == 200:
            return BaseResponse(
                code=200,
                message="获取召回历史成功",
                data=response_data
            )
        else:
            return BaseResponse(
                code=status_code,
                message="获取召回历史失败",
                data=response_data
            )
            
    except Exception as e:
        logger.error(f"获取召回历史失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"获取召回历史失败: {str(e)}",
            data={}
        )

@router.post("/embed_text", response_model=BaseResponse)
async def embed_text(
    knowledge_uid: str = Path(..., description="知识库ID"),
    data: Dict[str, Any] = Body(..., description="请求数据")
):
    """
    嵌入文本
    
    Args:
        knowledge_uid: 知识库ID
        data: 请求数据，包含以下字段：
            - username: 用户名
            - user_id: 用户ID
            - file_ids: 文件id列表，例如["847fd249-e457-4def-b68b-e59db81d914c"]
            - remove_extra_spaces: 是否去除多余空格换行符和制表符
            - remove_urls_emails: 是否去除URL和邮箱
            - separator: 分段符，例如"\n\n"
            - max_tokens: 最大token数，例如500
            - chunk_overlap: 分段重叠，例如50
    Returns:
        BaseResponse: 响应对象
    """
    try:
        username = data.get("username")
        user_id = data.get("user_id")

        # 获取embedding模型列表
        dify_admin = DifyAdmin()
        try:
            embedding_models_status, embedding_models_list = await dify_admin.get_embedding_model()
            embedding_model = embedding_models_list.get("data")[0] if embedding_models_status == 200 else []

        except Exception as e:
            logger.error(f"嵌入文本失败，无法正常获取embedding模型列表: {str(e)}")
            return BaseResponse(
                code=500,
                message="嵌入文本失败，无法正常获取embedding模型列表",
                data={}
            )

        rerank_model = {
            "search_method": "semantic_search",
            "reranking_enable": False,
            "reranking_mode": None,
            "reranking_model": {
                "reranking_provider_name": "",
                "reranking_model_name": ""
            },
            "weights": None,
            "top_k": 2,
            "score_threshold_enabled": False,
            "score_threshold": None
        }
        # 获取参数
        _data = {
            "data_source": {
                "type": "upload_file",
                "info_list": {
                    "data_source_type": "upload_file",
                    "file_info_list": {
                    "file_ids": data.get("file_ids", [])
                    }
                }
            },
            "doc_form": data.get("doc_form", "text_model"),
            "doc_language": data.get("doc_language", "Chinese"),
            "embedding_model": data.get("embedding_model", embedding_model.get("models")[0].get("model")),
            "embedding_model_provider": data.get("embedding_model_provider", embedding_model.get("provider")),
            "indexing_technique": data.get("indexing_technique", "high_quality"),
            "process_rule": {
                "rules": {
                    "pre_processing_rules": [
                    {
                        "id": "remove_extra_spaces",
                        "enabled": data.get("remove_extra_spaces", True)
                    },
                    {
                        "id": "remove_urls_emails",
                        "enabled": data.get("remove_urls_emails", False)
                    }
                    ],
                    "segmentation": {
                        "separator": data.get("separator", '\n\n'),
                        "max_tokens": data.get("max_tokens", 500),
                        "chunk_overlap": data.get("chunk_overlap", 50)
                    }
                },
                "mode": "custom"
            },
            "retrieval_model": data.get("retrieval_model", rerank_model),
        }

        # 查询数据库获取dify_email和dify_password
        knowledge_dify_mapping = KnowledgeDifyMappingModel()
        try:
            await knowledge_dify_mapping.initialize()
            record = await knowledge_dify_mapping.find_by_userid_and_username(user_id, username)
            
            if not record:
                return BaseResponse(
                    code=404,
                    message="未找到用户对应的Dify账号信息",
                    data={}
                )
                
            dify_email = record[0].get("dify_email")
            dify_password = record[0].get("dify_password")
            
        except Exception as e:
            logger.error(f"查询Dify账号信息失败: {str(e)}")
            return BaseResponse(
                code=500,
                message="查询Dify账号信息失败",
                data={}
            )
        finally:
            await knowledge_dify_mapping.close()
        
        # 创建Dify实例并嵌入文本
        dify = Dify(email=dify_email, password=dify_password)
        status_code, response_data = await dify.embed_text(
            knowledge_uid=knowledge_uid,
            data=_data
        )
        
        if status_code == 200:
            return BaseResponse(
                code=200,
                message="文本嵌入成功",
                data=response_data
            )
        else:
            return BaseResponse(
                code=status_code,
                message="文本嵌入失败",
                data=response_data
            )
            
    except Exception as e:
        logger.error(f"文本嵌入失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"文本嵌入失败: {str(e)}",
            data={}
        )

@router.get("/get_process_rule", response_model=BaseResponse)
async def get_process_rule(
        username: str = Query(..., description="用户名"),
        user_id: str = Query(..., description="用户ID")
):
    """
    获取知识库分段设置信息

    Args:
        username: 用户名
        user_id: 用户ID

    Returns:
        BaseResponse: 响应对象
    """
    knowledge_dify_mapping = None
    try:
        # 查询数据库获取dify_email和dify_password
        knowledge_dify_mapping = KnowledgeDifyMappingModel()
        await knowledge_dify_mapping.initialize()
        
        record = await knowledge_dify_mapping.find_by_userid_and_username(user_id, username)

        if not record:
            return BaseResponse(
                code=404,
                message="未找到用户对应的Dify账号信息",
                data={}
            )

        dify_email = record[0].get("dify_email")
        dify_password = record[0].get("dify_password")

        # 创建Dify实例并获取分段设置信息
        dify = Dify(email=dify_email, password=dify_password)
        status_code, response_data = await dify.get_process_rule()

        if status_code == 200:
            return BaseResponse(
                code=200,
                message="获取知识库分段设置信息成功",
                data=response_data
            )
        else:
            return BaseResponse(
                code=status_code,
                message="获取知识库分段设置信息失败",
                data=response_data
            )

    except Exception as e:
        logger.error(f"获取知识库分段设置信息失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"获取知识库分段设置信息失败: {str(e)}",
            data={}
        )
    finally:
        # 确保关闭数据库连接
        if knowledge_dify_mapping:
            await knowledge_dify_mapping.close()

@router.post("/doc_upload", response_model=BaseResponse)
async def upload_knowledge_doc(
    knowledge_uid: str = Path(..., description="知识库ID"),
    username: str = Form(..., description="用户名"),
    user_id: str = Form(..., description="用户ID"),
    file: UploadFile = File(..., description="上传的文件")
):
    """
    上传知识库文档
    
    Args:
        knowledge_uid: 知识库ID
        username: 用户名
        user_id: 用户ID
        file: 上传的文件
        
    Returns:
        BaseResponse: 响应对象
    """
    try:
        # 检查文件后缀并处理
        file_name = file.filename
        file_ext = os.path.splitext(file_name)[1].lower()
        
        # 如果是Python文件，转换为txt
        if file_ext == '.py':
            file_name = os.path.splitext(file_name)[0] + '.txt'
            logger.info(f"检测到Python文件，已转换为txt格式: {file_name}")
        
        # 创建临时文件保存上传的文件
        temp_file_path = f"temp_{file_name}"
        try:
            with open(temp_file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
        except Exception as e:
            logger.error(f"保存上传文件失败: {str(e)}")
            return BaseResponse(
                code=500,
                message="保存上传文件失败",
                data={}
            )
        
        # 查询数据库获取dify_email和dify_password
        knowledge_dify_mapping = KnowledgeDifyMappingModel()
        await knowledge_dify_mapping.initialize()

        try:
            record = await knowledge_dify_mapping.find_by_userid_and_username(user_id, username)
            
            if not record:
                return BaseResponse(
                    code=404,
                    message="未找到用户对应的Dify账号信息",
                    data={}
                )
                
            dify_email = record[0].get("dify_email")
            dify_password = record[0].get("dify_password")
            
        except Exception as e:
            logger.error(f"查询Dify账号信息失败: {str(e)}")
            return BaseResponse(
                code=500,
                message="查询Dify账号信息失败",
                data={}
            )
        finally:
            await knowledge_dify_mapping.close()
        
        # 创建Dify实例并上传文件
        dify = Dify(email=dify_email, password=dify_password)
        status_code, response_data = await dify.upload_database_doc(
            knowledge_uid=knowledge_uid,
            file_path=temp_file_path,
            file_name=file_name
        )
        
        # 删除临时文件
        try:
            os.remove(temp_file_path)
        except Exception as e:
            logger.warning(f"删除临时文件失败: {str(e)}")
        
        if status_code == 201:
            return BaseResponse(
                code=200,
                message="上传知识库文档成功",
                data=response_data
            )
        else:
            return BaseResponse(
                code=status_code,
                message="上传知识库文档失败",
                data=response_data
            )
            
    except Exception as e:
        logger.error(f"上传知识库文档失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"上传知识库文档失败: {str(e)}",
            data={}
        )

@router.post("/doc_rename", response_model=BaseResponse)
async def rename_knowledge_doc(
    knowledge_uid: str = Path(..., description="知识库ID"),
    data: Dict[str, Any] = Body(..., description="请求数据")
):
    """
    修改知识库文档名接口
    
    Args:
        knowledge_uid: 知识库ID
        data: 请求数据，包含以下字段：
            - username: 用户名
            - user_id: 用户ID
            - document_id: 文档ID
            - name: 文档名
            
    Returns:
        BaseResponse: 响应结果
    """
    try:
        # 从请求数据中获取参数
        username = data.get("username")
        user_id = data.get("user_id")
        document_id = data.get("document_id")
        name = data.get("name")
        
        # 检查必要参数
        if not all([username, user_id, document_id, name]):
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="缺少必要参数",
                    data={}
                ).dict()
            )
        
        # 创建知识库映射模型实例
        dify_mapping = KnowledgeDifyMappingModel()
        await dify_mapping.initialize()
        
        try:
            # 查询用户Dify账号信息
            records = await dify_mapping.find_by_userid_and_username(user_id, username)
            
            if not records:
                return JSONResponse(
                    status_code=404,
                    content=BaseResponse(
                        code=404,
                        message="未找到用户Dify账号信息",
                        data={}
                    ).dict()
                )
            
            # 获取dify_email和dify_password
            dify_email = records[0].get("dify_email")
            dify_password = records[0].get("dify_password")
            
            # 创建Dify实例
            dify = Dify(email=dify_email, password=dify_password)
            
            # 调用更新文档名方法
            result = await dify.update_database_document_name(
                knowledge_uid=knowledge_uid,
                document_id=document_id,
                name=name
            )
            
            # 检查返回值
            if result is None:
                return JSONResponse(
                    status_code=500,
                    content=BaseResponse(
                        code=500,
                        message="修改知识库文档失败：服务器无响应",
                        data={}
                    ).dict()
                )
            
            # 解包返回值
            status_code, response_data = result
            
            # 根据状态码返回不同的响应
            if status_code == 200:
                return JSONResponse(
                    status_code=200,
                    content=BaseResponse(
                        code=200,
                        message="修改知识库文档成功",
                        data=response_data
                    ).dict()
                )
            else:
                return JSONResponse(
                    status_code=status_code,
                    content=BaseResponse(
                        code=status_code,
                        message="修改知识库文档失败",
                        data=response_data
                    ).dict()
                )
                
        finally:
            # 确保关闭数据库连接
            await dify_mapping.close()
            
    except Exception as e:
        logger.error(f"修改知识库文档失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"修改知识库文档失败: {str(e)}",
                data={}
            ).dict()
        )

        
@router.delete("/docs", response_model=BaseResponse)
async def delete_knowledge_docs(
    knowledge_uid: str = Path(..., description="知识库ID"),
    data: Dict[str, Any] = Body(..., description="请求数据")
):
    """
    批量删除知识库文档接口
    Args:
        knowledge_uid: 知识库ID
        data: 请求数据，包含以下字段：
            - username: 用户名【必填】
            - user_id: 用户ID【必填】
            - document_ids: 文档ID列表【必填】
            
    Returns:
        BaseResponse: 响应结果，包含：
            - code: 状态码
                - 200: 成功
                - 400: 参数错误
                - 404: 未找到用户Dify账号
                - 500: 服务器错误
            - message: 响应消息
            - data: 响应数据
    """
    try:
        # 检查必要参数
        required_fields = ["username", "user_id", "document_ids"]
        for field in required_fields:
            if field not in data:
                return BaseResponse(
                    code=400,
                    message=f"缺少必要参数: {field}",
                    data={}
                )
        
        # 获取参数
        username = data.get("username")
        user_id = data.get("user_id")
        document_ids = data.get("document_ids")
        
        # 查询数据库获取dify_email和dify_password
        knowledge_dify_mapping = KnowledgeDifyMappingModel()
        try:
            await knowledge_dify_mapping.initialize()
            record = await knowledge_dify_mapping.find_by_userid_and_username(user_id, username)
            
            if not record:
                return BaseResponse(
                    code=404,
                    message="未找到用户对应的Dify账号信息",
                    data={}
                )
                
            dify_email = record[0].get("dify_email")
            dify_password = record[0].get("dify_password")
            
        except Exception as e:
            logger.error(f"查询Dify账号信息失败: {str(e)}")
            return BaseResponse(
                code=500,
                message="查询Dify账号信息失败",
                data={}
            )
        finally:
            await knowledge_dify_mapping.close()
        
        # 创建Dify实例并删除文档
        dify = Dify(email=dify_email, password=dify_password)
        status_code, response_data = await dify.delete_knowledge_docs(
            knowledge_uid=knowledge_uid,
            document_ids=document_ids
        )
        
        # 如果状态码是204，转换为200表示成功
        if status_code == 204:
            return BaseResponse(
                code=200,
                message="批量删除知识库文档成功",
                data=response_data
            )
        else:
            return BaseResponse(
                code=status_code,
                message="批量删除知识库文档失败",
                data=response_data
            )
            
    except Exception as e:
        logger.error(f"批量删除知识库文档失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"批量删除知识库文档失败: {str(e)}",
            data={}
        )

@router.delete("/doc", response_model=BaseResponse)
async def delete_knowledge_doc(
    knowledge_uid: str = Path(..., description="知识库ID"),
    username: str = Query(..., description="用户名"),
    user_id: str = Query(..., description="用户ID"),
    document_id: str = Query(..., description="文档ID")
):
    """
    删除知识库文档接口

    Args:
        knowledge_uid: 知识库ID
        username: 用户名
        user_id: 用户ID
        document_id: 文档ID

    Returns:
        BaseResponse: 响应结果
    """
    try:
        # 创建知识库映射模型实例
        dify_mapping = KnowledgeDifyMappingModel()
        await dify_mapping.initialize()

        try:
            # 查询用户Dify账号信息
            records = await dify_mapping.find_by_userid_and_username(user_id, username)

            if not records:
                return JSONResponse(
                    status_code=404,
                    content=BaseResponse(
                        code=404,
                        message="未找到用户Dify账号信息",
                        data={}
                    ).dict()
                )

            dify_email = records[0].get("dify_email")

            # 创建Dify实例
            dify = DifyAPIKey(email=dify_email, username=username)

            # 调用get_database_segments方法获取文档分片数据
            status_code, response_data = await dify.delete_database_document(
                knowledge_uid=knowledge_uid,
                document_id=document_id,
            )

            # 根据状态码返回不同的响应
            if status_code == 200:
                return JSONResponse(
                    status_code=200,
                    content=BaseResponse(
                        code=200,
                        message="删除知识库文档成功",
                        data=response_data
                    ).dict()
                )
            else:
                return JSONResponse(
                    status_code=status_code,
                    content=BaseResponse(
                        code=status_code,
                        message="删除知识库文档分片失败",
                        data=response_data
                    ).dict()
                )

        finally:
            # 确保关闭数据库连接
            await dify_mapping.close()

    except Exception as e:
        logger.error(f"获取知识库文档分片失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"获取知识库文档分片失败: {str(e)}",
                data={}
            ).dict()
        )


@router.get("/doc", response_model=BaseResponse)
async def get_knowledge_doc(
    knowledge_uid: str = Path(..., description="知识库ID"),
    username: str = Query(..., description="用户名"),
    user_id: str = Query(..., description="用户ID")
):
    """
    获取知识库文档接口
    
    Args:
        knowledge_uid: 知识库ID
        username: 用户名
        user_id: 用户ID
        
    Returns:
        BaseResponse: 响应结果
    """
    try:
        # 创建知识库映射模型实例
        dify_mapping = KnowledgeDifyMappingModel()
        await dify_mapping.initialize()
        
        try:
            # 查询用户Dify账号信息
            records = await dify_mapping.find_by_userid_and_username(user_id, username)
            
            if not records:
                return JSONResponse(
                    status_code=404,
                    content=BaseResponse(
                        code=404,
                        message="未找到用户Dify账号信息",
                        data={}
                    ).dict()
                )
            
            # 获取dify_email
            dify_email = records[0].get("dify_email")
            
            # 创建DifyAPIKey实例，传入dify_email和username
            dify = DifyAPIKey(email=dify_email, username=username)
            
            # 调用get_database_doc方法获取文档数据
            status_code, response_data = await dify.get_database_doc(knowledge_uid)
            
            # 根据状态码返回不同的响应
            if status_code == 200:
                return JSONResponse(
                    status_code=200,
                    content=BaseResponse(
                        code=200,
                        message="获取知识库文档成功",
                        data=response_data.get('data')
                    ).dict()
                )
            else:
                return JSONResponse(
                    status_code=status_code,
                    content=BaseResponse(
                        code=status_code,
                        message="获取知识库文档失败",
                        data=response_data
                    ).dict()
                )
                
        finally:
            # 确保关闭数据库连接
            await dify_mapping.close()
            
    except Exception as e:
        logger.error(f"获取知识库文档失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"获取知识库文档失败: {str(e)}",
                data={}
            ).dict()
        )

@router.get("/segments", response_model=BaseResponse)
async def get_knowledge_segments(
    knowledge_uid: str = Path(..., description="知识库ID"),
    username: str = Query(..., description="用户名"),
    user_id: str = Query(..., description="用户ID"),
    document_id: str = Query(..., description="文档ID"),
    page: str = Query("1", description="页码"),
    limit: str = Query("10", description="每页数量"),
    keyword: str = Query("", description="搜索关键词"),
    enabled: str = Query("all", description="是否启用")
):
    """
    获取知识库文档分片接口
    
    Args:
        knowledge_uid: 知识库ID
        username: 用户名
        user_id: 用户ID
        document_id: 文档ID
        page: 页码，默认1
        limit: 每页数量，默认10
        keyword: 搜索关键词，默认为空
        enabled: 是否启用，默认为all
        
    Returns:
        BaseResponse: 响应结果
    """
    try:
        # 创建知识库映射模型实例
        dify_mapping = KnowledgeDifyMappingModel()
        await dify_mapping.initialize()
        
        try:
            # 查询用户Dify账号信息
            records = await dify_mapping.find_by_userid_and_username(user_id, username)
            
            if not records:
                return JSONResponse(
                    status_code=404,
                    content=BaseResponse(
                        code=404,
                        message="未找到用户Dify账号信息",
                        data={}
                    ).dict()
                )
            
            # 获取dify_email和dify_password
            dify_email = records[0].get("dify_email")
            dify_password = records[0].get("dify_password")
            
            # 创建Dify实例
            dify = Dify(email=dify_email, password=dify_password)
            
            # 调用get_database_segments方法获取文档分片数据
            status_code, response_data = await dify.get_database_segments(
                knowledge_uid=knowledge_uid,
                document_id=document_id,
                page=page,
                limit=limit,
                keyword=keyword,
                enabled=enabled
            )
            
            # 根据状态码返回不同的响应
            if status_code == 200:
                return JSONResponse(
                    status_code=200,
                    content=BaseResponse(
                        code=200,
                        message="获取知识库文档分片成功",
                        data=response_data
                    ).dict()
                )
            else:
                return JSONResponse(
                    status_code=status_code,
                    content=BaseResponse(
                        code=status_code,
                        message="获取知识库文档分片失败",
                        data=response_data
                    ).dict()
                )
                
        finally:
            # 确保关闭数据库连接
            await dify_mapping.close()
            
    except Exception as e:
        logger.error(f"获取知识库文档分片失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"获取知识库文档分片失败: {str(e)}",
                data={}
            ).dict()
        )
    
@router.patch("/disable_segments", response_model=BaseResponse)
async def disable_segments(
    knowledge_uid: str = Path(..., description="知识库ID"),
    data: Dict[str, Any] = Body(..., description="请求数据")
):
    """
    批量禁用知识库文档分段
    
    Args:
        knowledge_uid: 知识库ID
        data: 请求数据，包含以下字段：
            - username: 用户名【必填】
            - user_id: 用户ID【必填】
            - document_id: 文档ID【必填】
            - segment_ids: 分段ID列表【必填】
            
    Returns:
        BaseResponse: 响应结果，包含：
            - code: 状态码
                - 200: 成功
                - 400: 参数错误
                - 404: 未找到用户Dify账号
                - 500: 服务器错误
            - message: 响应消息
            - data: 响应数据
    """
    try:
        # 检查必要参数
        required_fields = ["username", "user_id", "document_id", "segment_ids"]
        for field in required_fields:
            if field not in data:
                return BaseResponse(
                    code=400,
                    message=f"缺少必要参数: {field}",
                    data={}
                )
        
        # 获取参数
        username = data.get("username")
        user_id = data.get("user_id")
        document_id = data.get("document_id")
        segment_ids = data.get("segment_ids")
        
        # 查询数据库获取dify_email和dify_password
        knowledge_dify_mapping = KnowledgeDifyMappingModel()
        try:
            await knowledge_dify_mapping.initialize()
            record = await knowledge_dify_mapping.find_by_userid_and_username(user_id, username)
            
            if not record:
                return BaseResponse(
                    code=404,
                    message="未找到用户对应的Dify账号信息",
                    data={}
                )
                
            dify_email = record[0].get("dify_email")
            dify_password = record[0].get("dify_password")
            
        except Exception as e:
            logger.error(f"查询Dify账号信息失败: {str(e)}")
            return BaseResponse(
                code=500,
                message="查询Dify账号信息失败",
                data={}
            )
        finally:
            await knowledge_dify_mapping.close()
        
        # 创建Dify实例并禁用分段
        dify = Dify(email=dify_email, password=dify_password)
        status_code, response_data = await dify.disable_doc_segments(
            knowledge_uid=knowledge_uid,
            document_id=document_id,
            segment_ids=segment_ids
        )
        
        if status_code == 200:
            return BaseResponse(
                code=200,
                message="批量禁用文档分段成功",
                data=response_data
            )
        else:
            return BaseResponse(
                code=status_code,
                message="批量禁用文档分段失败",
                data=response_data
            )
            
    except Exception as e:
        logger.error(f"批量禁用文档分段失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"批量禁用文档分段失败: {str(e)}",
            data={}
        )

@router.patch("/enable_segments", response_model=BaseResponse)
async def enable_segments(
    knowledge_uid: str = Path(..., description="知识库ID"),
    data: Dict[str, Any] = Body(..., description="请求数据")
):
    """
    批量启用知识库文档分段
    
    Args:
        knowledge_uid: 知识库ID
        data: 请求数据，包含以下字段：
            - username: 用户名【必填】
            - user_id: 用户ID【必填】
            - document_id: 文档ID【必填】
            - segment_ids: 分段ID列表【必填】
            
    Returns:
        BaseResponse: 响应结果，包含：
            - code: 状态码
                - 200: 成功
                - 400: 参数错误
                - 404: 未找到用户Dify账号
                - 500: 服务器错误
            - message: 响应消息
            - data: 响应数据
    """
    try:
        # 检查必要参数
        required_fields = ["username", "user_id", "document_id", "segment_ids"]
        for field in required_fields:
            if field not in data:
                return BaseResponse(
                    code=400,
                    message=f"缺少必要参数: {field}",
                    data={}
                )
        
        # 获取参数
        username = data.get("username")
        user_id = data.get("user_id")
        document_id = data.get("document_id")
        segment_ids = data.get("segment_ids")
        
        # 查询数据库获取dify_email和dify_password
        knowledge_dify_mapping = KnowledgeDifyMappingModel()
        try:
            await knowledge_dify_mapping.initialize()
            record = await knowledge_dify_mapping.find_by_userid_and_username(user_id, username)
            
            if not record:
                return BaseResponse(
                    code=404,
                    message="未找到用户对应的Dify账号信息",
                    data={}
                )
                
            dify_email = record[0].get("dify_email")
            dify_password = record[0].get("dify_password")
            
        except Exception as e:
            logger.error(f"查询Dify账号信息失败: {str(e)}")
            return BaseResponse(
                code=500,
                message="查询Dify账号信息失败",
                data={}
            )
        finally:
            await knowledge_dify_mapping.close()
        
        # 创建Dify实例并启用分段
        dify = Dify(email=dify_email, password=dify_password)
        status_code, response_data = await dify.enable_doc_segments(
            knowledge_uid=knowledge_uid,
            document_id=document_id,
            segment_ids=segment_ids
        )
        
        if status_code == 200:
            return BaseResponse(
                code=200,
                message="批量启用文档分段成功",
                data=response_data
            )
        else:
            return BaseResponse(
                code=status_code,
                message="批量启用文档分段失败",
                data=response_data
            )
            
    except Exception as e:
        logger.error(f"批量启用文档分段失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"批量启用文档分段失败: {str(e)}",
            data={}
        )

@router.delete("/segments", response_model=BaseResponse)
async def delete_segments(
    knowledge_uid: str = Path(..., description="知识库ID"),
    data: Dict[str, Any] = Body(..., description="请求数据")
):
    """
    批量删除知识库文档分段
    
    Args:
        knowledge_uid: 知识库ID
        data: 请求数据，包含以下字段：
            - username: 用户名【必填】
            - user_id: 用户ID【必填】
            - document_id: 文档ID【必填】
            - segment_ids: 分段ID列表【必填】
            
    Returns:
        BaseResponse: 响应结果，包含：
            - code: 状态码
                - 200: 成功
                - 400: 参数错误
                - 404: 未找到用户Dify账号或文档不存在
                - 500: 服务器错误
            - message: 响应消息
            - data: 响应数据
    """
    try:
        # 检查必要参数
        required_fields = ["username", "user_id", "document_id", "segment_ids"]
        for field in required_fields:
            if field not in data:
                return BaseResponse(
                    code=400,
                    message=f"缺少必要参数: {field}",
                    data={}
                )
        
        # 获取参数
        username = data.get("username")
        user_id = data.get("user_id")
        document_id = data.get("document_id")
        segment_ids = data.get("segment_ids")
        
        # 查询数据库获取dify_email和dify_password
        knowledge_dify_mapping = KnowledgeDifyMappingModel()
        try:
            await knowledge_dify_mapping.initialize()
            record = await knowledge_dify_mapping.find_by_userid_and_username(user_id, username)
            
            if not record:
                return BaseResponse(
                    code=404,
                    message="未找到用户对应的Dify账号信息",
                    data={}
                )
                
            dify_email = record[0].get("dify_email")
            dify_password = record[0].get("dify_password")
            
        except Exception as e:
            logger.error(f"查询Dify账号信息失败: {str(e)}")
            return BaseResponse(
                code=500,
                message="查询Dify账号信息失败",
                data={}
            )
        finally:
            await knowledge_dify_mapping.close()
        
        # 创建Dify实例
        dify = Dify(email=dify_email, password=dify_password)

        # 删除分段
        try:
            status_code, response_data = await dify.delete_doc_segments(
                knowledge_uid=knowledge_uid,
                document_id=document_id,
                segment_ids=segment_ids
            )
            
            if status_code == 204:
                return BaseResponse(
                    code=200,
                    message="批量删除文档分段成功",
                    data=response_data
                )
            elif status_code == 404:
                return BaseResponse(
                    code=404,
                    message=f"文档或分段不存在: {response_data.get('message', '')}",
                    data=response_data
                )
            else:
                return BaseResponse(
                    code=status_code,
                    message=f"批量删除文档分段失败: {response_data.get('message', '')}",
                    data=response_data
                )
                
        except Exception as e:
            logger.error(f"删除文档分段失败: {str(e)}")
            return BaseResponse(
                code=500,
                message=f"删除文档分段失败: {str(e)}",
                data={}
            )
            
    except Exception as e:
        logger.error(f"批量删除文档分段失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"批量删除文档分段失败: {str(e)}",
            data={}
        )

@router.patch("/documents/{document_id}/segments/{segment_id}")
async def edit_doc_segments(
    knowledge_uid: str = Path(..., description="知识库ID"),
    document_id: str = Path(..., description="文档ID"),
    segment_id: str = Path(..., description="分段ID"),
    data: Dict[str, Any] = Body(..., description="请求数据")
) -> BaseResponse:
    """
    编辑文档分段
    
    Args:
        knowledge_uid: 知识库ID
        document_id: 文档ID
        segment_id: 分段ID
        data: 请求数据，包含：
            - username: 用户名
            - user_id: 用户ID
            - content: 分段内容
            - keywords: 关键词列表
            
    Returns:
        BaseResponse: 响应对象
    """
    try:
        # 检查必要参数
        if not all(key in data for key in ["username", "user_id", "content", "keywords"]):
            return BaseResponse(
                code=400,
                message="缺少必要参数",
                data={}
            )
            
        # 获取用户Dify账号信息
        mapping_model = KnowledgeDifyMappingModel()
        try:
            await mapping_model.initialize()
            mapping = await mapping_model.find_by_userid_and_username(
                user_id=data["user_id"],
                username=data["username"]
            )
            
            if not mapping:
                return BaseResponse(
                    code=404,
                    message="用户账号不存在",
                    data={}
                )

            # 创建Dify实例
            dify = Dify(
                email=mapping[0].get("dify_email"),
                password=mapping[0].get("dify_password")
            )
            
            # 调用编辑分段接口
            status_code, response_data = await dify.edit_doc_segments(
                knowledge_uid=knowledge_uid,
                document_id=document_id,
                segment_id=segment_id,
                content=data["content"],
                keywords=data["keywords"]
            )
            
            return BaseResponse(
                code=status_code,
                message="编辑文档分段成功" if status_code == 200 else "编辑文档分段失败2",
                data=response_data
            )
            
        finally:
            await mapping_model.close()
            
    except Exception as e:
        logger.error(f"编辑文档分段失败3: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"编辑文档分段失败4: {str(e)}",
            data={}
        )

@router.post("/documents/{document_id}/segments")
async def add_doc_segments(
    knowledge_uid: str = Path(..., description="知识库ID"),
    document_id: str = Path(..., description="文档ID"),
    data: Dict[str, Any] = Body(..., description="请求数据")
) -> BaseResponse:
    """
    新增文档分段
    
    Args:
        knowledge_uid: 知识库ID
        document_id: 文档ID
        data: 请求数据，包含：
            - username: 用户名
            - user_id: 用户ID
            - content: 分段内容
            - keywords: 关键词列表
            
    Returns:
        BaseResponse: 响应对象
    """
    try:
        # 检查必要参数
        if not all(key in data for key in ["username", "user_id", "content", "keywords"]):
            return BaseResponse(
                code=400,
                message="缺少必要参数",
                data={}
            )
            
        # 获取用户Dify账号信息
        mapping_model = KnowledgeDifyMappingModel()
        try:
            await mapping_model.initialize()
            mapping = await mapping_model.find_by_userid_and_username(
                user_id=data["user_id"],
                username=data["username"]
            )
            
            if not mapping:
                return BaseResponse(
                    code=404,
                    message="用户账号不存在",
                    data={}
                )
                
            # 创建Dify实例
            dify = Dify(
                email=mapping[0].get("dify_email"),
                password=mapping[0].get("dify_password")
            )
            
            # 调用新增分段接口
            status_code, response_data = await dify.add_doc_segments(
                knowledge_uid=knowledge_uid,
                document_id=document_id,
                content=data["content"],
                keywords=data["keywords"]
            )
            
            return BaseResponse(
                code=status_code,
                message="新增文档分段成功" if status_code == 200 else "新增文档分段失败",
                data=response_data
            )
            
        finally:
            await mapping_model.close()
            
    except Exception as e:
        logger.error(f"新增文档分段失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"新增文档分段失败: {str(e)}",
            data={}
        )
    
