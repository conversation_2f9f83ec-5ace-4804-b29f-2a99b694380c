
from fastapi.responses import JSONResponse

class AuthModel:

    def __init__(self, request):
        self.request  = request

    def header_analysis(self):
        """请求头过滤器"""
        header = self.request.headers.get("User-Agent")
        if header is None:
            return JSONResponse(
                status_code=401,
                content={"message": "请求头缺失"}
            )
        else:
            return self.request
