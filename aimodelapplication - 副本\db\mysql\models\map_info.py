from typing import Dict, List, Any, Optional
from db.mysql.mysql_pool import MySQLPool
from logger import logging
import json


class MapInfoModel:
    """知识库模型类"""

    def __init__(self):
        self.table = "map_info"
        self.primary_key = "id"
        self.logger = logging()
        # 创建数据库连接池实例
        self.db_pool = MySQLPool()

    async def initialize(self):
        """初始化连接池"""
        await self.db_pool.initialize()

    async def close(self):
        """关闭连接池，释放资源"""
        await self.db_pool.close()

    async def create_map_info(
        self,
        uid: str,
        kb_name: str,
        doc_list: Optional[List[str]] = None,
        version: str = "1.0.0",
        version_desc: Optional[str] = None,
        kb_type: str = "",
        entity_count: int = 0,
        relation_count: int = 0
    ) -> int:
        """
        创建知识库元信息
        
        Args:
            uid: 知识库UID
            kb_name: 知识库名称
            doc_list: 已嵌入文档列表
            version: 版本号
            version_desc: 版本描述
            kb_type: 知识库类型
            entity_count: 实体数量
            relation_count: 关系数量
            
        Returns:
            int: 插入记录的ID
        """
        try:
            sql = f"""
                INSERT INTO {self.table} (
                    uid, kb_name, doc_list, version, version_desc,
                    type, entity_count, relation_count
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            params = (
                uid, kb_name, json.dumps(doc_list) if doc_list else None,
                version, version_desc, kb_type, entity_count, relation_count
            )
            
            return await self.db_pool.execute(sql, params)
            
        except Exception as e:
            self.logger.error(f"创建知识库元信息失败: {str(e)}")
            raise

    async def update_map_info(
        self,
        uid: str,
        kb_name: Optional[str] = None,
        doc_list: Optional[List[str]] = None,
        version: Optional[str] = None,
        version_desc: Optional[str] = None,
        kb_type: Optional[str] = None,
        entity_count: Optional[int] = None,
        relation_count: Optional[int] = None
    ) -> bool:
        """
        更新知识库元信息
        
        Args:
            uid: 知识库UID
            kb_name: 知识库名称
            doc_list: 已嵌入文档列表
            version: 版本号
            version_desc: 版本描述
            kb_type: 知识库类型
            entity_count: 实体数量
            relation_count: 关系数量
            
        Returns:
            bool: 是否更新成功
        """
        try:
            update_fields = []
            params = []
            
            if kb_name is not None:
                update_fields.append("kb_name = %s")
                params.append(kb_name)
            if doc_list is not None:
                update_fields.append("doc_list = %s")
                params.append(json.dumps(doc_list))
            if version is not None:
                update_fields.append("version = %s")
                params.append(version)
            if version_desc is not None:
                update_fields.append("version_desc = %s")
                params.append(version_desc)
            if kb_type is not None:
                update_fields.append("type = %s")
                params.append(kb_type)
            if entity_count is not None:
                update_fields.append("entity_count = %s")
                params.append(entity_count)
            if relation_count is not None:
                update_fields.append("relation_count = %s")
                params.append(relation_count)
                
            if not update_fields:
                return False
                
            sql = f"""
                UPDATE {self.table}
                SET {", ".join(update_fields)}
                WHERE uid = %s
            """
            params.append(uid)
            
            affected_rows = await self.db_pool.execute(sql, params)
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"更新知识库元信息失败: {str(e)}")
            raise

    async def get_map_info_by_uid(self, uid: str) -> Dict[str, Any]:
        """
        根据UID获取知识库地图信息
        
        Args:
            uid: 知识库UID
            
        Returns:
            Dict[str, Any]: 知识库地图信息
        """
        try:
            query = f"""
                SELECT * FROM {self.table}
                WHERE uid = %s
            """
            result = await self.db_pool.fetch_one(query, (uid,))
            
            if result:
                # 如果存在doc_list字段，将其从JSON字符串转换为列表
                if "doc_list" in result:
                    result["doc_list"] = json.loads(result["doc_list"]) if result["doc_list"] else []
                return result
            return {}
            
        except Exception as e:
            self.logger.error(f"获取知识库地图信息失败: {str(e)}")
            return {}
            
    async def update(self, uid: str, data: Dict[str, Any]) -> bool:
        """
        更新知识库地图信息
        
        Args:
            uid: 知识库UID
            data: 要更新的数据，包含：
                - version: 版本号
                - version_desc: 版本描述
                
        Returns:
            bool: 更新是否成功
        """
        try:
            # 初始化数据库连接池
            await self.initialize()
            
            # 构建更新语句
            set_clauses = []
            values = []
            
            if "version" in data:
                set_clauses.append("version = %s")
                values.append(data["version"])
                
            if "version_desc" in data:
                set_clauses.append("version_desc = %s")
                values.append(data["version_desc"])
                
            if not set_clauses:
                self.logger.warning("没有需要更新的字段")
                return False
                
            # 添加UID到参数列表
            values.append(uid)
            
            # 构建完整的更新语句
            query = f"""
                UPDATE {self.table} 
                SET {', '.join(set_clauses)}
                WHERE uid = %s
            """
            
            # 执行更新
            affected_rows = await self.db_pool.execute(query, values)
            
            if affected_rows > 0:
                self.logger.info(f"成功更新知识库 {uid} 的信息")
                return True
            else:
                self.logger.warning(f"未找到知识库 {uid} 或更新失败")
                return False
                    
        except Exception as e:
            self.logger.error(f"更新知识库地图信息失败: {str(e)}")
            return False
            
        finally:
            # 确保关闭数据库连接
            await self.close()

    async def get_map_info_by_type(self, kb_type: str) -> List[Dict[str, Any]]:
        """
        根据类型获取知识库元信息列表
        
        Args:
            kb_type: 知识库类型
            
        Returns:
            List[Dict[str, Any]]: 知识库元信息列表
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE type = %s
            """
            results = await self.db_pool.fetch_all(sql, (kb_type,))
            
            for result in results:
                result["doc_list"] = json.loads(result["doc_list"]) if result["doc_list"] else []
            return results
            
        except Exception as e:
            self.logger.error(f"获取知识库元信息列表失败: {str(e)}")
            raise

    async def delete_map_info(self, uid: str) -> bool:
        """
        删除知识库元信息
        
        Args:
            uid: 知识库UID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            sql = f"""
                DELETE FROM {self.table}
                WHERE uid = %s
            """
            affected_rows = await self.db_pool.execute(sql, (uid,))
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"删除知识库元信息失败: {str(e)}")
            raise

    async def add_doc_to_map(self, uid: str, doc_id: str) -> bool:
        """
        添加文档到知识库
        
        Args:
            uid: 知识库UID
            doc_id: 文档ID
            
        Returns:
            bool: 是否添加成功
        """
        try:
            # 先获取当前文档列表
            map_info = await self.get_map_info_by_uid(uid)
            if not map_info:
                return False
                
            doc_list = map_info["doc_list"]
            if doc_id not in doc_list:
                doc_list.append(doc_id)
                
            return await self.update_map_info(uid, doc_list=doc_list)
            
        except Exception as e:
            self.logger.error(f"添加文档到知识库失败: {str(e)}")
            raise

    async def remove_doc_from_map(self, uid: str, doc_id: str) -> bool:
        """
        从知识库中移除文档
        
        Args:
            uid: 知识库UID
            doc_id: 文档ID
            
        Returns:
            bool: 是否移除成功
        """
        try:
            # 先获取当前文档列表
            map_info = await self.get_map_info_by_uid(uid)
            if not map_info:
                return False
                
            doc_list = map_info["doc_list"]
            if doc_id in doc_list:
                doc_list.remove(doc_id)
                
            return await self.update_map_info(uid, doc_list=doc_list)
            
        except Exception as e:
            self.logger.error(f"从知识库中移除文档失败: {str(e)}")
            raise


