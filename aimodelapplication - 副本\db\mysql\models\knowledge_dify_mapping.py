from typing import Dict, List, Any
from db.mysql.mysql_pool import MySQLPool
from logger import logging

class KnowledgeDifyMappingModel:
    """知识库模型类"""
    
    def __init__(self):
        self.table = "knowledge_dify_mapping"
        self.primary_key = "id"
        self.logger = logging()
        # 创建数据库连接池实例
        self.db_pool = MySQLPool()
        
    async def initialize(self):
        """初始化连接池"""
        await self.db_pool.initialize()
    
    async def close(self):
        """关闭连接池，释放资源"""
        await self.db_pool.close()
    
    async def find_by_dify_email_and_username(self, dify_email: str, username: str) -> List[Dict[str, Any]]:
        """根据dify_email和username查询知识库
        
        Args:
            dify_email: Dify平台用户邮箱
            
        Returns:
            知识库列表
        """
        try:
            sql = f"SELECT * FROM {self.table} WHERE dify_email = %s AND username = %s"
            return await self.db_pool.fetch_all(sql, (dify_email, username))
        except Exception as e:
            self.logger.error(f"根据dify_email和username查询知识库失败: {str(e)}")
            return []

    async def find_by_userid_and_username(self, user_id: str, username: str) -> List[Dict[str, Any]]:
        """根据userid和username查询知识库

        Returns:
            知识库列表
        """
        try:
            sql = f"SELECT * FROM {self.table} WHERE user_id = %s AND username = %s"
            return await self.db_pool.fetch_all(sql, (user_id, username))
        except Exception as e:
            self.logger.error(f"根据dify_email和username查询知识库失败: {str(e)}")
            return []

    async def delete_by_id(self, id: int) -> bool:
        """根据id删除知识库"""
        try:
            sql = f"DELETE FROM {self.table} WHERE id = %s"
            await self.db_pool.execute(sql, (id,))
            return True
        except Exception as e:
            self.logger.error(f"根据id删除知识库失败: {str(e)}")
            return False
    
    async def insert(self, data: Dict[str, Any]) -> int:
        """插入知识库记录
        
        Args:
            data: 要插入的数据，字段名与值的字典
            
        Returns:
            插入成功返回1，失败返回0
        """
        try:
            # 提取字段名和对应的值
            fields = list(data.keys())
            placeholders = ', '.join(['%s'] * len(fields))
            values = tuple(data.values())
            
            # 构建SQL语句
            sql = f"INSERT INTO {self.table} ({', '.join(fields)}) VALUES ({placeholders})"
            
            # 执行插入操作
            result = await self.db_pool.execute(sql, values)
            return result
        except Exception as e:
            self.logger.error(f"插入知识库记录失败: {str(e)}")
            return 0
    
