# 关系提取功能实现说明

## 功能概述

本次实现了第二步功能：**关系提取**。用户点击关系提取按钮后，系统会调用新增的接口来实现以下功能：

1. 从数据库中根据知识库ID获取提示词信息
2. 将用户填写的提示词与本地写好的提示词进行组装
3. 调用大模型进行前置依赖提取
4. 根据提取出来的前置依赖去接口文档中找出前置依赖相关的接口（从Neo4j数据库中提取所有类型为API的节点）
5. 返回接口的详细信息

## 实现位置

文件路径：`aimodelapplication\api\v1\knowledge_api\relation_uid\views.py`

## 新增接口

### POST `/extract_relations`

**接口描述：** 关系提取接口

**路径参数：**
- `relation_uid` (str): 知识库ID

**功能流程：**

1. **获取提示词配置**
   - 使用 `PromptManagementModel` 从数据库中获取指定知识库的提示词配置
   - 包括：业务介绍、名词定义、业务提示、额外依据等

2. **获取API接口文档**
   - 使用 `Neo4jModel` 从Neo4j数据库中查询所有类型为"API"的节点
   - 这些节点包含了接口的详细信息：方法、路径、参数、请求体等

3. **构建接口文档**
   - 将API节点信息格式化为结构化的接口文档
   - 提取参数信息、请求体字段等关键信息

4. **构建完整提示词**
   - 将用户配置的提示词与预设的提示词模板组合
   - 包含任务描述、业务介绍、判断依据、输出要求等

5. **调用大语言模型**
   - 使用阿里云百炼API（DashScope）进行前置依赖分析
   - 模型会根据接口文档和业务规则分析前置依赖关系

6. **智能匹配相关接口**
   - 使用大语言模型进行语义理解和智能匹配
   - 构建专门的API匹配提示词，让大模型分析前置依赖与接口的关联关系
   - 大模型会根据以下维度进行匹配：
     - 操作类型语义匹配（如"创建"对应POST方法）
     - 实体名称语义匹配（如"项目"对应包含project的接口）
     - 参数依赖逻辑匹配（如提到projectId，则需要能创建project的接口）
     - 业务逻辑关联匹配（如分配资产前需要先创建项目）
   - 提供匹配度评分（0-100）和详细的匹配原因
   - 如果大模型匹配失败，自动回退到传统关键词匹配方法

**返回结果：**
```json
{
  "code": 200,
  "message": "关系提取成功",
  "data": {
    "prerequisite_analysis": "创建项目（projectId）、创建用户（userName）、创建资产(assetsId)",
    "related_apis": [
      {
        "id": "api-uuid",
        "name": "POST /api/projects",
        "method": "POST",
        "path": "/api/projects",
        "summary": "创建新项目",
        "match_score": 95,
        "match_reason": ["创建类接口", "实体名称匹配: 项目", "业务逻辑相关"],
        "parameters": [...],
        "request_body": {...},
        "responses": {...},
        "tags": [...]
      }
    ],
    "total_apis_found": 3
  }
}
```

## 核心函数说明

### 1. `build_api_document(api_nodes: List[dict]) -> str`
- **功能：** 将API节点列表构建为格式化的接口文档
- **输入：** API节点列表
- **输出：** 结构化的接口文档字符串

### 2. `build_complete_prompt_for_relation_extraction(prompt_config: dict, api_document: str) -> str`
- **功能：** 构建用于关系提取的完整提示词
- **输入：** 提示词配置、接口文档
- **输出：** 完整的提示词字符串

### 3. `find_related_apis(llm_result: str, api_nodes: List[dict]) -> List[dict]`
- **功能：** 使用大语言模型智能匹配相关API接口
- **输入：** 大模型结果、API节点列表
- **输出：** 相关API接口详细信息列表（包含匹配度评分）

### 4. `build_api_matching_prompt(prerequisite_analysis: str, api_descriptions: List[str]) -> str`
- **功能：** 构建用于API智能匹配的提示词
- **输入：** 前置依赖分析结果、API接口描述列表
- **输出：** 专门用于API匹配的提示词

### 5. `parse_api_matching_result(matching_result: str, api_nodes: List[dict]) -> List[dict]`
- **功能：** 解析大模型返回的API匹配结果
- **输入：** 大模型匹配结果、原始API节点列表
- **输出：** 解析后的相关API列表

### 6. `find_related_apis_fallback(llm_result: str, api_nodes: List[dict]) -> List[dict]`
- **功能：** 传统关键词匹配方法（回退方案）
- **输入：** 大模型结果、API节点列表
- **输出：** 基于关键词匹配的API接口列表

### 7. `DashScopeService.call_llm(prompt: str, model: str) -> str`
- **功能：** 调用阿里云百炼大语言模型
- **输入：** 提示词、模型名称
- **输出：** 模型分析结果

## 错误处理

- **404错误：** 未找到提示词配置或API节点
- **500错误：** 数据库连接失败、大模型调用失败等
- **资源清理：** 确保数据库连接在finally块中正确关闭

## 使用示例

前端调用示例：
```javascript
const response = await fetch(`/api/v1/knowledge_api/${relationUid}/extract_relations`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  }
});

const result = await response.json();
console.log('关系提取结果:', result.data);
```

## 依赖模块

- `PromptManagementModel`: 提示词管理数据模型
- `Neo4jModel`: Neo4j图数据库模型
- `DashScopeService`: 阿里云百炼API服务
- `BaseResponse`: 统一响应格式

## 注意事项

1. **API密钥安全：** 阿里云百炼API密钥应该从环境变量或配置文件中读取，不应硬编码
2. **性能优化：** 对于大量API节点，可以考虑分批处理或缓存机制
3. **错误重试：** 大模型调用可能失败，建议添加重试机制
4. **日志记录：** 已添加详细的日志记录，便于调试和监控

## 智能匹配的优势

1. **语义理解：** 大模型能够理解业务逻辑和语义关系，而不仅仅是字符串匹配
2. **上下文分析：** 能够根据业务场景分析接口之间的依赖关系
3. **灵活匹配：** 即使用词不完全一致，也能通过语义理解找到相关接口
4. **评分机制：** 提供匹配度评分，帮助用户判断匹配的可信度
5. **容错性强：** 提供回退机制，确保在大模型失败时仍能正常工作

## 后续优化建议

1. **配置化：** 将API密钥、模型参数等配置化
2. **缓存机制：** 对频繁查询的数据添加缓存，减少大模型调用次数
3. **异步处理：** 对于耗时操作考虑异步处理
4. **结果优化：** 根据实际使用情况优化匹配算法和提示词
5. **监控告警：** 添加接口调用监控和异常告警
6. **A/B测试：** 对比智能匹配和传统匹配的效果，持续优化
7. **用户反馈：** 收集用户对匹配结果的反馈，用于模型优化
