-- 提示词管理表
-- 用于存储提示词管理的所有配置信息
CREATE TABLE `prompt_management` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `knowledge_base_id` varchar(64) NOT NULL COMMENT '知识库ID',
  `business_introduction` text COMMENT '业务介绍',
  `term_definitions` json COMMENT '名词定义，JSON格式存储 [{"name":"名词","definition":"定义"}]',
  `business_prompts` json COMMENT '业务提示，JSON格式存储 [{"content":"提示内容"}]',
  `extra_evidence` json COMMENT '额外依据，JSON格式存储 [{"name":"依据名称","description":"依据描述"}]',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_knowledge_base_id` (`knowledge_base_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提示词管理表';

-- 插入示例数据
INSERT INTO `prompt_management` (
  `knowledge_base_id`, 
  `business_introduction`, 
  `term_definitions`, 
  `business_prompts`, 
  `extra_evidence`,
  `created_by`
) VALUES (
  'kb_example_001',
  '当前系统为堡垒机系统',
  JSON_ARRAY(
    JSON_OBJECT('name', '资产', 'definition', '本地添加的资产，包括主机数据库等'),
    JSON_OBJECT('name', '账号', 'definition', '创建资产时同步创建或者绑定资产创建')
  ),
  JSON_ARRAY(
    JSON_OBJECT('content', '授权是对用户进行（资产，资产账号等）授权'),
    JSON_OBJECT('content', '账号是关联在资产下，创建资产可以同时创建账号，也可以创建资产后再创建账号')
  ),
  JSON_ARRAY(
    JSON_OBJECT('name', '接口类型', 'description', '接口类型为创建类型接口')
  ),
  'system'
);

-- 查询示例
-- 查询某个知识库的配置
/*
SELECT 
  id,
  knowledge_base_id,
  business_introduction,
  JSON_PRETTY(term_definitions) as term_definitions,
  JSON_PRETTY(business_prompts) as business_prompts,
  JSON_PRETTY(extra_evidence) as extra_evidence,
  created_at,
  updated_at
FROM prompt_management 
WHERE knowledge_base_id = 'kb_example_001' AND is_deleted = 0;
*/

-- 更新示例
/*
UPDATE prompt_management 
SET 
  business_introduction = '更新后的业务介绍',
  term_definitions = JSON_ARRAY(
    JSON_OBJECT('name', '新名词', 'definition', '新定义')
  ),
  updated_by = 'user_001',
  updated_at = CURRENT_TIMESTAMP
WHERE knowledge_base_id = 'kb_example_001';
*/
