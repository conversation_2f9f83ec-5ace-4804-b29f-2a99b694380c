
# 登录api
CONSOLE_API_LOGIN = "/console/api/login"

# 申请账号
INVITE_EMAIL = "/console/api/workspaces/current/members/invite-email"

# 删除账号
DELETE_ACCOUNT = "/console/api/workspaces/current/members/"

# 查询知识库
GET_KNOWLEDGE_BASE = "/console/api/datasets"

# 创建知识库
CREATE_KNOWLEDGE_BASE = "/console/api/datasets"

# 删除知识库
DELETE_KNOWLEDGE_BASE = "/console/api/datasets/"

# 获取DIFY知识库的API密钥
GET_DIFY_API_KEY = "/console/api/datasets/api-keys"

# 获取DIFY知识库文档信息
GET_KNOWLEDGE_BASE_DOC = "/v1/datasets/{knowledge_uid}/documents"

# 获取DIFY知识库文档分片信息
GET_KNOWLEDGE_BASE_DOC_SEGMENTS_API_KEY = "/v1/datasets/{knowledge_uid}/documents/{document_id}/segments"

# 获取DIFY知识库文档分片信息
GET_KNOWLEDGE_BASE_DOC_SEGMENTS = "/console/api/datasets/{knowledge_uid}/documents/{document_id}/segments"

# 修改DIFY知识库文档名
UPDATE_KNOWLEDGE_BASE_DOC_NAME = "/console/api/datasets/{knowledge_uid}/documents/{document_id}/rename"

# 上传DIFY知识库文档
UPLOAD_KNOWLEDGE_BASE_DOC = "/console/api/files/upload?source=datasets"

# 获取分段设置信息
GET_KNOWLEDGE_BASE_DOC_SEGMENTS_SETTING = "/console/api/datasets/process-rule"

# 嵌入文本
EMBED_TEXT = "/console/api/datasets/{knowledge_uid}/documents"

# 删除DIFY知识库文档
DELETE_KNOWLEDGE_BASE_DOC = "/v1/datasets/{knowledge_uid}/documents/{document_id}"

# 批量删除DIFY知识库文档
DELETE_KNOWLEDGE_BASE_DOCS = "/console/api/datasets/{knowledge_uid}/documents"

# 召回测试历史记录
RECALL_TEST_HISTORY = "/console/api/datasets/{knowledge_uid}/queries?limit=20&page=1"

# 召回
RETRIEVE = "/v1/datasets/{knowledge_uid}/retrieve"

# 获取rerank模型
GET_RERANK_MODEL = "/console/api/workspaces/current/models/model-types/rerank"

# 获取embedding模型
GET_TXT_EMBEDDING_MODEL = "/console/api/workspaces/current/models/model-types/text-embedding"

# 删除文档分段
DELETE_KNOWLEDGE_BASE_DOC_SEGMENTS = "/console/api/datasets/{knowledge_uid}/documents/{document_id}/segments"

# 启用文档分段
ENABLE_KNOWLEDGE_BASE_DOC_SEGMENTS = "/console/api/datasets/{knowledge_uid}/documents/{document_id}/segment/enable"

# 禁用文档分段
DISABLE_KNOWLEDGE_BASE_DOC_SEGMENTS = "/console/api/datasets/{knowledge_uid}/documents/{document_id}/segment/disable"

# 编辑文档分段
EDIT_KNOWLEDGE_BASE_DOC_SEGMENTS = "/console/api/datasets/{knowledge_uid}/documents/{document_id}/segments/{segment_id}"

# 添加文档分段
ADD_KNOWLEDGE_BASE_DOC_SEGMENTS = "/console/api/datasets/{knowledge_uid}/documents/{document_id}/segment"