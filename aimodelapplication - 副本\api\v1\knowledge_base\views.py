from fastapi import APIRouter, Query
from dotenv import load_dotenv
from fastapi.responses import JSONResponse

from db.mysql.models.knowledge_dify_mapping import KnowledgeDifyMappingModel
from service.dify.dify import Dify
from template.response_temp import BaseResponse
from db.mysql.models.knowledge_base import KnowledgeBaseModel
from typing import Dict, Any
from datetime import datetime
from service.dify.dify import DifyAdmin

# 加载环境变量
load_dotenv()

router = APIRouter()

def serialize_datetime(obj: Dict[str, Any]) -> Dict[str, Any]:
    """
    序列化字典中的datetime对象为字符串
    
    Args:
        obj: 原始字典
        
    Returns:
        处理后的字典
    """
    result = {}
    for key, value in obj.items():
        if isinstance(value, datetime):
            result[key] = value.strftime("%Y-%m-%d %H:%M:%S")
        else:
            result[key] = value
    return result

@router.get("/rerank_model", response_model=BaseResponse)
async def get_rerank_model():
    """
    获取rerank模型列表
    Args:
        无参数
        
    Returns:
        BaseResponse: 响应结果，包含以下字段：
            - code: 状态码
            - message: 响应消息
            - data: 数据，包含rerank模型列表
    """
    try:
        # 创建DifyAdmin实例
        dify_admin = DifyAdmin()
        
        # 调用get_rerank_model方法获取模型列表
        status_code, response_data = await dify_admin.get_rerank_model()
        
        if status_code == 200:
            return BaseResponse(
                code=200,
                message="获取rerank模型列表成功",
                data=response_data.get("data", [])
            )
        else:
            return BaseResponse(
                code=status_code,
                message="获取rerank模型列表失败",
                data=response_data
            )
            
    except Exception as e:
        return BaseResponse(
            code=500,
            message=f"获取rerank模型列表失败: {str(e)}",
            data={}
        )


@router.get("/txt_embedding_model", response_model=BaseResponse)
async def get_rerank_model():
    """
    获取embedding模型列表
    Args:
        无参数
    Returns:
        BaseResponse: 响应结果，包含以下字段：
            - code: 状态码
            - message: 响应消息
            - data: 数据，包含rerank模型列表
    """
    try:
        # 创建DifyAdmin实例
        dify_admin = DifyAdmin()

        # 调用get_rerank_model方法获取模型列表
        status_code, response_data = await dify_admin.get_embedding_model()

        if status_code == 200:
            return BaseResponse(
                code=200,
                message="获取embedding模型列表成功",
                data=response_data.get("data", [])
            )
        else:
            return BaseResponse(
                code=status_code,
                message="获取embedding模型列表失败",
                data=response_data
            )

    except Exception as e:
        return BaseResponse(
            code=500,
            message=f"获取embedding模型列表失败: {str(e)}",
            data={}
        )

@router.get("/", response_model=BaseResponse)
async def get_knowledge_base(
    username: str = Query(..., description="用户名"),
    user_id: str = Query(..., description="用户id"),
):
    """
    获取知识库列表
    
    Args:
        username: 用户名
        user_id: 用户id
    
    Returns:
        BaseResponse: 响应结果
    """
    # 参数验证
    if not username:
        return JSONResponse(
            status_code=400,
            content=BaseResponse(
                code=400,
                message="用户名不能为空",
                data={
                    "count": 0,
                    "records": []
                }
            ).dict()
        )
    
    # 创建知识库模型实例
    kb_model = KnowledgeBaseModel()
    mapping_model = KnowledgeDifyMappingModel()
    
    try:
        # 初始化数据库连接
        await kb_model.initialize()
        await mapping_model.initialize()
        
        # 查询当前username所绑定的Dify邮箱和密码
        mapping_records = await mapping_model.find_by_userid_and_username(username=username, user_id=user_id)
        
        # 如果有绑定的Dify账号，实例化Dify对象
        dify = None
        extracted_data = []
        if mapping_records:
            dify_email = mapping_records[0].get("dify_email")
            dify_password = mapping_records[0].get("dify_password")
            if dify_email and dify_password:
                dify = Dify(email=dify_email, password=dify_password)
                status_code, dify_data = await dify.get_database()  # 使用await调用异步函数
                if status_code == 200:
                    # 提取所需字段
                    extracted_data = [
                        {
                            "id": item.get("id"),
                            "title": item.get("name", "").replace("【CODE】", ""),
                            "created_at": item.get("created_at"),
                            "created_by": item.get("created_by"),
                            "description": item.get("description"),
                            "docs": item.get("document_count"),
                            "chars": item.get("word_count"),
                            "permission": item.get("permission"),
                            "category": "B" if "【CODE】" in item.get("name", "") else "A"
                        }
                        for item in dify_data.get("data", [])
                    ]
        
        # 查询所有知识库记录
        all_records = await kb_model.find_all()
        
        # 过滤记录并序列化datetime
        filtered_records = []
        for record in all_records:
            record['category'] = "C"
            # 如果是公开权限，直接添加
            if record.get("permission") == "all_team_members":
                filtered_records.append(serialize_datetime(record))
            # 如果是仅自己可见，且创建者是当前用户，则添加
            elif record.get("permission") == "only_me" and record.get("created_by") == username:
                filtered_records.append(serialize_datetime(record))

        # 合并提取的数据
        filtered_records += extracted_data

        # 转换数据格式
        for record in filtered_records:
            # 检查并处理不同的键名
            if "name" in record:
                record["title"] = record.pop("name")
            if "document_count" in record:
                record["docs"] = record.pop("document_count")
            if "word_count" in record:
                record["chars"] = record.pop("word_count")
            
            # 权限和标签管理
            permission = record.pop("permission", None)
            record["permission"] = permission
            if permission == "only_me":
                record["tags"] = ["私有"]
            elif permission == "all_team_members":
                record["tags"] = ["公开"]
            elif permission:
                record["tags"] = [permission]

        # 返回结果
        return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message="查询成功",
                data={
                    "count": len(filtered_records),
                    "records": filtered_records
                }
            ).dict()
        )
        
    except Exception as e:
        # 记录错误并返回错误响应
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"查询失败: {str(e)}",
                data={
                    "count": 0,
                    "records": []
                }
            ).dict()
        )
        
    finally:
        # 确保关闭数据库连接
        if 'kb_model' in locals():
            await kb_model.close()
        if 'mapping_model' in locals():
            await mapping_model.close()