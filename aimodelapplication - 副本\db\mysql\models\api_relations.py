from typing import Dict, List, Any, Optional
from db.mysql.mysql_pool import MySQLPool
from logger import logging
import json


class ApiRelationsModel:
    """API关系模型类 - 专门处理 api_relations 表"""

    def __init__(self):
        self.table = "api_relations"
        self.primary_key = "id"
        self.logger = logging()
        self.db_pool = MySQLPool()

    async def initialize(self):
        """初始化连接池"""
        await self.db_pool.initialize()

    async def close(self):
        """关闭连接池，释放资源"""
        await self.db_pool.close()

    async def create_relation(
        self,
        relation_uid: str,
        api_name: str,
        api_type: str,
        data_model: Optional[str] = None,
        relation_type: Optional[str] = None,
        details: Optional[Dict] = None
    ) -> int:
        """
        创建API关系记录

        Args:
            relation_uid: 关系唯一标识
            api_name: API名称
            api_type: API类型
            data_model: 数据模型（可选）
            relation_type: 关系类型（可选）
            details: 其他详细信息（JSON对象）

        Returns:
            int: 插入记录的主键ID
        """
        try:
            sql = f"""
                INSERT INTO {self.table} (
                    relation_uid, api_name, api_type,
                    data_model, relation_type, details
                ) VALUES (%s, %s, %s, %s, %s, %s)
            """
            params = (
                relation_uid,
                api_name,
                api_type,
                data_model,
                relation_type,
                json.dumps(details) if details else None
            )

            return await self.db_pool.execute(sql, params)

        except Exception as e:
            self.logger.error(f"创建API关系记录失败: {str(e)}")
            raise

    async def get_relations_by_api_name(self, api_name: str) -> List[Dict[str, Any]]:
        """
        根据API名称查询所有关联记录

        Args:
            api_name: API名称

        Returns:
            List[Dict]: 查询结果列表（包含解析后的JSON字段）
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE api_name = %s
            """
            results = await self.db_pool.fetch_all(sql, (api_name,))

            for result in results:
                if result.get("details") and isinstance(result["details"], str):
                    result["details"] = json.loads(result["details"])

            return results

        except Exception as e:
            self.logger.error(f"查询API关系记录失败: {str(e)}")
            raise

    async def get_relations_by_relation_uid(self, relation_uid: str) -> List[Dict[str, Any]]:
        """
        根据relation_uid查询关联记录

        Args:
            relation_uid: 关系唯一标识

        Returns:
            List[Dict]: 查询结果列表
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE relation_uid = %s
            """
            results = await self.db_pool.fetch_all(sql, (relation_uid,))

            for result in results:
                if result.get("details") and isinstance(result["details"], str):
                    result["details"] = json.loads(result["details"])

            return results

        except Exception as e:
            self.logger.error(f"查询API关系记录失败: {str(e)}")
            raise

    async def delete_relation_by_uid(self, relation_uid: str) -> bool:
        """
        根据relation_uid删除关系记录

        Args:
            relation_uid: 关系唯一标识

        Returns:
            bool: 是否删除成功
        """
        try:
            sql = f"""
                DELETE FROM {self.table}
                WHERE relation_uid = %s
            """
            affected_rows = await self.db_pool.execute(sql, (relation_uid,))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"删除API关系记录失败: {str(e)}")
            raise

    async def update_relation_details(self, relation_uid: str, details: Dict) -> bool:
        """
        更新指定relation_uid的关系详情

        Args:
            relation_uid: 关系唯一标识
            details: 新的详情内容（字典）

        Returns:
            bool: 是否更新成功
        """
        try:
            sql = f"""
                UPDATE {self.table}
                SET details = %s
                WHERE relation_uid = %s
            """
            affected_rows = await self.db_pool.execute(sql, (json.dumps(details), relation_uid))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"更新API关系详情失败: {str(e)}")
            raise

    async def get_all_relations(self) -> List[Dict[str, Any]]:
        """
        获取所有API关系记录

        Returns:
            List[Dict]: 所有记录列表（包含解析后的JSON字段）
        """
        try:
            sql = f"SELECT * FROM {self.table}"
            results = await self.db_pool.fetch_all(sql)

            for result in results:
                if result.get("details") and isinstance(result["details"], str):
                    result["details"] = json.loads(result["details"])

            return results

        except Exception as e:
            self.logger.error(f"获取所有API关系记录失败: {str(e)}")
            raise

    async def count_relations_by_api_name(self, api_name: str) -> int:
        """
        统计某个API名称对应的关系数量

        Args:
            api_name: API名称

        Returns:
            int: 关系数量
        """
        try:
            sql = f"""
                SELECT COUNT(*) AS count 
                FROM {self.table}
                WHERE api_name = %s
            """
            result = await self.db_pool.fetch_one(sql, (api_name,))
            return result.get("count", 0)

        except Exception as e:
            self.logger.error(f"统计API关系数量失败: {str(e)}")
            raise
