from typing import Dict, List, Any, Optional
from db.mysql.mysql_pool import MySQLPool
from logger import logging
import json


class MapDocModel:
    """文档映射表模型类"""

    def __init__(self):
        self.table = "map_doc"
        self.primary_key = "id"
        self.logger = logging()
        # 创建数据库连接池实例
        self.db_pool = MySQLPool()

    async def initialize(self):
        """初始化连接池"""
        await self.db_pool.initialize()

    async def close(self):
        """关闭连接池，释放资源"""
        await self.db_pool.close()
    # 根据知识库uid和文档名称删除文档映射记录
    async def delete_doc_mapping_by_name(self, document_name: str, knowledge_base_uid: str) -> bool:
        """
        根据知识库uid和文档名称删除文档映射记录
        
        Args:
            document_name: 文档名称
            knowledge_base_uid: 知识库UID
            
        Returns:
            bool: 删除是否成功
                - True: 删除成功
                - False: 删除失败
        """
        try:
            sql = f"""
                DELETE FROM {self.table}
                WHERE document_name = %s 
                AND knowledge_base_uid = %s
            """
            affected_rows = await self.db_pool.execute(sql, (document_name, knowledge_base_uid))
            
            if affected_rows > 0:
                self.logger.info(f"成功删除知识库 {knowledge_base_uid} 下的文档映射记录: {document_name}")
                return True
            self.logger.warning(f"未找到知识库 {knowledge_base_uid} 下的文档映射记录: {document_name}")
            return False
            
        except Exception as e:
            self.logger.error(f"删除文档映射记录失败: {str(e)}")
            raise

    # 查询是否存在同名文档
    async def check_doc_name(self, document_name: str, knowledge_base_uid: str) -> bool:
        """
        根据知识库uid查询知识库下是否存在同名文档
        
        Args:
            document_name: 文档名称
            knowledge_base_uid: 知识库UID
            
        Returns:
            bool: 是否存在同名文档
                - True: 存在同名文档
                - False: 不存在同名文档
        """
        try:
            sql = f"""
                SELECT COUNT(*) as count 
                FROM {self.table}
                WHERE document_name = %s 
                AND knowledge_base_uid = %s
            """
            result = await self.db_pool.fetch_one(sql, (document_name, knowledge_base_uid))
            
            if result and result.get("count", 0) > 0:
                self.logger.info(f"知识库 {knowledge_base_uid} 下存在同名文档: {document_name}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"检查同名文档失败: {str(e)}")
            raise

    async def create_doc_mapping(
        self,
        knowledge_base_uid: str,
        document_name: str,
        entity_name: str,
        entity_type: str,
        entity_params: Optional[Dict[str, Any]] = None,
        status: str = "未嵌入"
    ) -> int:
        """
        创建文档映射记录
        
        Args:
            knowledge_base_uid: 知识库UID
            document_name: 文档名称
            entity_name: 实体名称
            entity_type: 实体类型
            entity_params: 实体参数
            status: 实体状态
            
        Returns:
            int: 插入记录的ID
        """
        try:
            sql = f"""
                INSERT INTO {self.table} (
                    knowledge_base_uid, document_name, entity_name,
                    entity_type, entity_params, status
                ) VALUES (%s, %s, %s, %s, %s, %s)
            """
            params = (
                knowledge_base_uid,
                document_name,
                entity_name,
                entity_type,
                json.dumps(entity_params) if entity_params else None,
                status
            )
            
            return await self.db_pool.execute(sql, params)
            
        except Exception as e:
            self.logger.error(f"创建文档映射记录失败: {str(e)}")
            raise

    async def update_doc_mapping(
        self,
        id: int,
        document_name: Optional[str] = None,
        entity_name: Optional[str] = None,
        entity_type: Optional[str] = None,
        entity_params: Optional[Dict[str, Any]] = None,
        status: Optional[str] = None
    ) -> bool:
        """
        更新文档映射记录
        
        Args:
            id: 记录ID
            document_name: 文档名称
            entity_name: 实体名称
            entity_type: 实体类型
            entity_params: 实体参数
            status: 实体状态
            
        Returns:
            bool: 是否更新成功
        """
        try:
            update_fields = []
            params = []
            
            if document_name is not None:
                update_fields.append("document_name = %s")
                params.append(document_name)
            if entity_name is not None:
                update_fields.append("entity_name = %s")
                params.append(entity_name)
            if entity_type is not None:
                update_fields.append("entity_type = %s")
                params.append(entity_type)
            if entity_params is not None:
                update_fields.append("entity_params = %s")
                params.append(json.dumps(entity_params))
            if status is not None:
                update_fields.append("status = %s")
                params.append(status)
                
            if not update_fields:
                return False
                
            sql = f"""
                UPDATE {self.table}
                SET {", ".join(update_fields)}
                WHERE id = %s
            """
            params.append(id)
            
            affected_rows = await self.db_pool.execute(sql, params)
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"更新文档映射记录失败: {str(e)}")
            raise

    async def get_doc_mapping_by_id(self, id: int) -> Dict[str, Any]:
        """
        根据ID获取文档映射记录
        
        Args:
            id: 记录ID
            
        Returns:
            Dict[str, Any]: 文档映射记录
        """
        try:
            query = f"""
                SELECT * FROM {self.table}
                WHERE id = %s
            """
            result = await self.db_pool.fetch_one(query, (id,))
            
            if result:
                # 如果存在entity_params字段，将其从JSON字符串转换为字典
                if "entity_params" in result and result["entity_params"]:
                    result["entity_params"] = json.loads(result["entity_params"])
                return result
            return {}
            
        except Exception as e:
            self.logger.error(f"获取文档映射记录失败: {str(e)}")
            return {}

    async def get_doc_mappings_by_kb_uid(self, knowledge_base_uid: str) -> list:
        """
        根据知识库uid查询文档，返回去重后的文档名称列表
        
        Args:
            knowledge_base_uid: 知识库UID
            
        Returns:
            list: 文档名称列表
        """
        try:
            sql = f"""
                SELECT DISTINCT document_name
                FROM {self.table}
                WHERE knowledge_base_uid = %s
                ORDER BY document_name
            """
            result = await self.db_pool.fetch_all(sql, (knowledge_base_uid,))
            self.logger.info(f"查询结果: {result}")
            
            if result:
                doc_list = [row["document_name"] for row in result]
                self.logger.info(f"成功查询知识库 {knowledge_base_uid} 的文档列表: {doc_list}")
                return doc_list
                
            self.logger.warning(f"知识库 {knowledge_base_uid} 下没有找到任何文档")
            return []
            
        except Exception as e:
            self.logger.error(f"查询知识库文档列表失败: {str(e)}")
            raise

    async def get_doc_mappings_by_entity_name(self, entity_name: str) -> List[Dict[str, Any]]:
        """
        根据实体名称获取文档映射记录列表
        
        Args:
            entity_name: 实体名称
            
        Returns:
            List[Dict[str, Any]]: 文档映射记录列表
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE entity_name = %s
            """
            results = await self.db_pool.fetch_all(sql, (entity_name,))
            
            for result in results:
                if "entity_params" in result and result["entity_params"]:
                    result["entity_params"] = json.loads(result["entity_params"])
            return results
            
        except Exception as e:
            self.logger.error(f"获取文档映射记录列表失败: {str(e)}")
            raise

    async def delete_doc_mapping(self, id: int) -> bool:
        """
        删除文档映射记录
        
        Args:
            id: 记录ID
            
        Returns:
            bool: 是否删除成功
        """
        try:
            sql = f"""
                DELETE FROM {self.table}
                WHERE id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (id,))
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"删除文档映射记录失败: {str(e)}")
            raise

    async def update_status(self, id: int, status: str) -> bool:
        """
        更新文档映射记录状态
        
        Args:
            id: 记录ID
            status: 新状态
            
        Returns:
            bool: 是否更新成功
        """
        try:
            sql = f"""
                UPDATE {self.table}
                SET status = %s
                WHERE id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (status, id))
            return affected_rows > 0
            
        except Exception as e:
            self.logger.error(f"更新文档映射记录状态失败: {str(e)}")
            raise

    async def get_doc_mappings_by_status(self, status: str) -> List[Dict[str, Any]]:
        """
        根据状态获取文档映射记录列表
        
        Args:
            status: 状态
            
        Returns:
            List[Dict[str, Any]]: 文档映射记录列表
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE status = %s
            """
            results = await self.db_pool.fetch_all(sql, (status,))
            
            for result in results:
                if "entity_params" in result and result["entity_params"]:
                    result["entity_params"] = json.loads(result["entity_params"])
            return results
            
        except Exception as e:
            self.logger.error(f"获取文档映射记录列表失败: {str(e)}")
            raise

    async def get_doc_mappings_by_kb_uid_and_doc_name(self, knowledge_base_uid: str, document_name: str) -> List[Dict[str, Any]]:
        """
        根据知识库UID和文档名称查询该文档下的所有详细信息

        Args:
            knowledge_base_uid: 知识库UID
            document_name: 文档名称

        Returns:
            List[Dict[str, Any]]: 该文档下的所有详细信息
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE knowledge_base_uid = %s AND document_name = %s
                ORDER BY id
            """
            results = await self.db_pool.fetch_all(sql, (knowledge_base_uid, document_name))
            for result in results:
                if "entity_params" in result and result["entity_params"]:
                    result["entity_params"] = json.loads(result["entity_params"])
            return results
        except Exception as e:
            self.logger.error(f"根据知识库ID和文档名查询详细信息失败: {str(e)}")
            raise
