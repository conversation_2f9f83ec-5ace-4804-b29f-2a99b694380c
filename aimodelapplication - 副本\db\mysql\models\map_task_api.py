from typing import Dict, List, Any, Optional
from db.mysql.mysql_pool import MySQLPool
from logger import logging
import json


class MapTaskApiModel:
    """任务接口映射模型类 - 专门处理 map_task_api 表"""

    def __init__(self):
        self.table = "map_task_api"
        self.primary_key = "id"
        self.logger = logging()
        self.db_pool = MySQLPool()

    async def initialize(self):
        """初始化连接池"""
        await self.db_pool.initialize()

    async def close(self):
        """关闭连接池，释放资源"""
        await self.db_pool.close()

    async def create_task_api_record(
            self,
            knowledge_base_id: str,
            task_id: Optional[str] = None,
            api_id: Optional[str] = None,
            status: int = 0
    ) -> int:
        """
        创建任务API记录

        Args:
            knowledge_base_id: 知识库ID
            task_id: 任务ID
            api_id: 接口ID
            status: 状态（默认0）

        Returns:
            int: 插入记录的主键ID
        """
        try:
            sql = f"""
                INSERT INTO {self.table} (
                    knowledge_base_id, task_id, api_id, status
                ) VALUES (%s, %s, %s, %s)
            """
            params = (knowledge_base_id, task_id, api_id, status)
            return await self.db_pool.execute(sql, params)

        except Exception as e:
            self.logger.error(f"创建任务API记录失败: {str(e)}")
            raise

    async def get_task_api_by_id(self, id: int) -> Dict[str, Any]:
        """
        根据ID获取任务API记录

        Args:
            id: 记录ID

        Returns:
            Dict[str, Any]: 任务API记录
        """
        try:
            query = f"""
                SELECT * FROM {self.table}
                WHERE id = %s
            """
            result = await self.db_pool.fetch_one(query, (id,))
            return result if result else {}

        except Exception as e:
            self.logger.error(f"获取任务API记录失败: {str(e)}")
            return {}

    async def update_task_api_status(self, task_id: str, status: int) -> bool:
        """
        根据 task_id 更新任务API记录状态

        Args:
            task_id: 任务ID（UUID）
            status: 新状态

        Returns:
            bool: 是否更新成功
        """
        try:
            sql = f"""
                UPDATE {self.table}
                SET status = %s
                WHERE task_id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (status, task_id))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"更新任务API记录状态失败: {str(e)}")
            raise

    async def delete_task_api_by_id(self, id: int) -> bool:
        """
        删除任务API记录

        Args:
            id: 记录ID

        Returns:
            bool: 是否删除成功
        """
        try:
            sql = f"""
                DELETE FROM {self.table}
                WHERE id = %s
            """
            affected_rows = await self.db_pool.execute(sql, (id,))
            return affected_rows > 0

        except Exception as e:
            self.logger.error(f"删除任务API记录失败: {str(e)}")
            raise

    async def get_task_apis_by_kb_id(self, knowledge_base_id: str) -> List[Dict[str, Any]]:
        """
        根据知识库ID查询所有任务API记录

        Args:
            knowledge_base_id: 知识库ID

        Returns:
            List[Dict[str, Any]]: 任务API记录列表
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE knowledge_base_id = %s
                ORDER BY id
            """
            results = await self.db_pool.fetch_all(sql, (knowledge_base_id,))
            return results if results else []

        except Exception as e:
            self.logger.error(f"根据知识库ID查询任务API记录失败: {str(e)}")
            raise

    async def get_task_apis_by_status(self, status: int) -> List[Dict[str, Any]]:
        """
        根据状态查询任务API记录

        Args:
            status: 状态值

        Returns:
            List[Dict[str, Any]]: 任务API记录列表
        """
        try:
            sql = f"""
                SELECT * FROM {self.table}
                WHERE status = %s
                ORDER BY id
            """
            results = await self.db_pool.fetch_all(sql, (status,))
            return results if results else []

        except Exception as e:
            self.logger.error(f"根据状态查询任务API记录失败: {str(e)}")
            raise

    async def get_all_tasks_with_progress(self) -> List[Dict[str, Any]]:
        """
        获取所有任务的进度信息（包含状态、创建时间等）

        Returns:
            List[Dict[str, Any]]: 所有任务的进度记录列表
        """
        try:
            sql = f"""
                SELECT id, knowledge_base_id, task_id, api_id, status, create_time
                FROM {self.table}
                ORDER BY create_time DESC
            """
            results = await self.db_pool.fetch_all(sql)
            return results if results else []

        except Exception as e:
            self.logger.error(f"查询所有任务进度信息失败: {str(e)}")
            raise
