from fastapi import APIRouter, Path, Body, Query, Form, File, UploadFile
from fastapi.responses import JSONResponse
from jsonschema import ValidationError
from pydantic import BaseModel
from typing import List, Optional

from db.mysql.models.api_relations import ApiRelationsModel
from db.mysql.models.map_task_api import MapTaskApiModel
from db.neo.models.neo4j_map import Neo4jModel
from db.mysql.models.map_info import MapInfoModel
from template.response_temp import BaseResponse
from logger import logging
import uuid
import os
import json
from db.mysql.models.knowledge_base import KnowledgeBaseModel
from db.mysql.models.map_info_history import MapInfoHistoryModel
from db.mysql.models.recall_test_history import RecallTestHistoryModel
from datetime import datetime
from db.mysql.models.map_doc import MapDocModel
from db.mysql.models.map_doc_api import MapDocApiModel
from db.mysql.models.map_doc_component import MapDocComponentsModel
from service.map.paths_extractor import ExtractSwaggerPathEndpoints
from service.map.components_extractor import ExtractSwaggerComponents
from service.map import json_utils
from db.mysql.models.prompt_management import PromptManagementModel


router = APIRouter()
logger = logging()

@router.get("/map")
async def get_map_list(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    获取知识库地图列表

    Args:
        relation_uid: 知识库ID

    Returns:
        JSONResponse: 包含知识库地图列表的响应
    """
    try:
        # 初始化Neo4j模型
        model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await model.initialize()
        
        # 查询所有节点
        nodes = await model.find_all()

        # 查询所有关系
        relationships = await model.find_relationships()
        
        # 获取所有类型
        categories = await model.get_categories()
        
        # 处理节点数据
        processed_nodes = []
        for node in nodes:
            # 确定节点类型
            node_type = node.get("type", "未知")
            
            # 构建节点数据
            node_data = {
                "id": node.get("id"),
                "name": node.get("name", ""),
                "category": 0,  # 默认分类
                "type": node_type,
                "fields": {
                    key: value for key, value in node.items()
                    if key not in ["id", "name", "type", "embedding", "text"]
                }
            }
            processed_nodes.append(node_data)
        
        
        # 处理分类数据
        processed_categories = [{"name": "未知"}] if not categories else [{"name": category} for category in sorted(categories)]
        
        # 构建返回数据
        data = {
            "nodes": processed_nodes,
            "links": relationships,
            "categories": processed_categories
        }
        
        return JSONResponse(
            status_code=200,
            content={
                "code": 200,
                "message": "获取知识库地图成功",
                "data": data
            }
        )
        
    except Exception as e:
        logger.error(f"获取知识库地图失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "message": f"获取知识库地图失败: {str(e)}",
                "data": None
            }
        )
    finally:
        # 确保关闭连接
        if 'model' in locals():
            await model.close()

@router.get("/")
async def get_map_info(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    获取知识库地图信息
    
    Args:
        relation_uid: 知识库ID
        
    Returns:
        JSONResponse: 包含知识库地图信息的响应
    """
    try:
        # 创建MapInfoModel实例
        map_model = MapInfoModel()
        await map_model.initialize()
        
        try:
            # 获取知识库地图信息
            map_info = await map_model.get_map_info_by_uid(relation_uid)
            
            if map_info:
                # 如果存在version_desc字段，从knowledge_base中查询description
                if "version_desc" in map_info:
                    knowledge_model = KnowledgeBaseModel()
                    await knowledge_model.initialize()
                    try:
                        knowledge_info = await knowledge_model.find_by_id(map_info["uid"])
                        if knowledge_info and "description" in knowledge_info:
                            map_info["version_desc"] = knowledge_info["description"]
                    finally:
                        await knowledge_model.close()
                
                return JSONResponse(
                    status_code=200,
                    content=BaseResponse(
                        code=200,
                        message="获取知识库地图信息成功",
                        data=map_info
                    ).dict()
                )
            else:
                return JSONResponse(
                    status_code=404,
                    content=BaseResponse(
                        code=404,
                        message="未找到知识库地图信息",
                        data={}
                    ).dict()
                )
                
        finally:
            await map_model.close()
            
    except Exception as e:
        logger.error(f"获取知识库地图信息失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"获取知识库地图信息失败: {str(e)}",
                data={}
            ).dict()
        )
    
@router.post("/entity")
async def add_entity(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="实体数据")
):
    """
    添加实体节点

    Args:
        relation_uid: 知识库ID
        data: 实体数据，包含以下字段：
            - name: 实体名称
            - type: 实体类型
            - fields: 其他属性字段

    Returns:
        JSONResponse: 包含操作结果的响应
    """
    try:
        # 获取知识库版本信息
        map_model = MapInfoModel()
        await map_model.initialize()
        try:
            map_info = await map_model.get_map_info_by_uid(relation_uid)
            if not map_info:
                version = map_info.get("version")
            else:
                version = map_info.get("version", "V1")
        finally:
            await map_model.close()
            
        # 自动生成节点ID
        node_id = str(uuid.uuid4())

        entity_data = data.get("entity_data")
        # 构建节点数据，将fields字段展开
        node_data = {
            "id": node_id,
            "name": entity_data.get("name", ""),
            "type": entity_data.get("type", "未知"),
            "version": version,
            **entity_data.get("fields", {})  # 展开fields字段
        }
        
        # 初始化Neo4j模型，使用多个标签
        model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await model.initialize()
        
        # 创建节点
        success = await model.create(node_data)
        
        if success:
            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message="添加实体成功",
                    data=node_data  # 直接返回完整的node_data
                ).dict()
            )
        else:
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message="添加实体失败",
                    data={}
                ).dict()
            )
            
    except Exception as e:
        logger.error(f"添加实体失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"添加实体失败: {str(e)}",
                data={}
            ).dict()
        )
    finally:
        # 确保关闭连接
        if 'model' in locals():
            await model.close()

@router.delete("/entity")
async def delete_entity(
    relation_uid: str = Path(..., description="知识库关系ID"),
    data: dict = Body(..., description="实体数据")
):
    """
    删除实体及其关系
    
    Args:
        relation_uid: 知识库关系ID
        data: 实体数据，包含以下字段：
            - version: 版本
            - entity_id: 实体id
    Returns:
        JSONResponse: 删除结果
    """
    model = None
    try:
        # 获取知识库版本信息
        map_model = MapInfoModel()
        await map_model.initialize()
        try:
            map_info = await map_model.get_map_info_by_uid(relation_uid)
            if not map_info:
                return JSONResponse(
                    status_code=404,
                    content=BaseResponse(
                        code=404,
                        message="知识库不存在",
                        data={}
                    ).dict()
                )
            version = map_info.get("version")
        finally:
            await map_model.close()
            
        # 初始化Neo4j模型，使用多个标签
        model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await model.initialize()
        
        # 删除实体及其关系
        success = await model.delete(data.get("entity_id"))
        if not success:
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message="删除实体失败",
                    data={}
                ).dict()
            )
            
        return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message="删除实体成功",
                data={
                    "entity_id": data.get("entity_id"),
                    "relation_uid": relation_uid
                }
            ).dict()
        )
        
    except Exception as e:
        logger.error(f"删除实体失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"删除实体失败: {str(e)}",
                data={}
            ).dict()
        )
    finally:
        # 确保关闭连接
        if model:
            await model.close()

@router.post("/relation")
async def add_relation(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="关系数据")
):
    """
    添加关系

    Args:
        relation_uid: 知识库ID
        data: 关系数据，包含以下字段：
            - source_id: 源节点ID
            - target_id: 目标节点ID
            - type: 关系类型
            - fields: 其他属性字段

    Returns:
        JSONResponse: 包含操作结果的响应
    """
    model = None
    try:
        # 获取知识库版本信息
        map_model = MapInfoModel()
        await map_model.initialize()
        try:
            map_info = await map_model.get_map_info_by_uid(relation_uid)
            if not map_info:
                return JSONResponse(
                    status_code=404,
                    content=BaseResponse(
                        code=404,
                        message="知识库不存在",
                        data={}
                    ).dict()
                )
            version = map_info.get("version", "V1")
        finally:
            await map_model.close()
            
        # 自动生成关系ID
        relation_id = str(uuid.uuid4())
        
        # 构建关系数据
        relation_data = {
            "id": relation_id,
            "source_id": data.get("source_id"),
            "target_id": data.get("target_id"),
            "type": data.get("type", "未知"),
            "version": version,
            **data.get("fields", {})  # 展开fields字段
        }
        
        # 初始化Neo4j模型，使用多个标签
        model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await model.initialize()
        
        # 创建关系，只传递关系属性
        success = await model.create_relationship(
            source_id=relation_data["source_id"],
            target_id=relation_data["target_id"],
            relation_type=relation_data["type"],
            properties={
                "id": relation_data["id"],
                "version": relation_data["version"],
                **data.get("fields", {})  # 只传递fields中的属性
            }
        )
        
        if success:
            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message="添加关系成功",
                    data=relation_data
                ).dict()
            )
        else:
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message="添加关系失败",
                    data={}
                ).dict()
            )
            
    except Exception as e:
        logger.error(f"添加关系失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"添加关系失败: {str(e)}",
                data={}
            ).dict()
        )
    finally:
        # 确保关闭连接
        if model:
            await model.close()


@router.post("/archive")
async def archive(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="归档数据")
):
    """
    归档知识库版本
    
    Args:
        relation_uid: 知识库ID
        data: 归档数据，包含：
            - version: 版本号
            - version_desc: 版本描述
            
    Returns:
        JSONResponse: 操作结果
    """
    try:
        # 验证必要参数
        if not data.get("version"):
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="版本号不能为空",
                    data={}
                ).dict()
            )
            
        # 1. 更新 map_info 表的版本信息
        map_info_model = MapInfoModel()
        await map_info_model.initialize()
        update_result = await map_info_model.update(
            relation_uid,
            {
                "version": data["version"],
                "version_desc": data.get("version_desc", "")
            }
        )
        
        if not update_result:
            await map_info_model.close()
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message="更新知识库版本信息失败",
                    data={}
                ).dict()
            )
            
        # 2. 插入版本历史记录
        history_model = MapInfoHistoryModel()
        await history_model.initialize()
        
        insert_result = await history_model.insert_all([{
            "uid": relation_uid,
            "version": data["version"],
            "version_desc": data.get("version_desc", "")
        }])
        
        if not insert_result:
            await map_info_model.close()
            await history_model.close()
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message="保存版本历史记录失败",
                    data={}
                ).dict()
            )
            
        # 关闭连接
        await map_info_model.close()
        await history_model.close()
        
        return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message="版本归档成功",
                data={
                    "version": data["version"],
                    "version_desc": data.get("version_desc", "")
                }
            ).dict()
        )
        
    except Exception as e:
        logger.error(f"版本归档失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"版本归档失败: {str(e)}",
                data={}
            ).dict()
        )

@router.get("/archive_history")
async def get_history(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    获取知识库的历史版本记录
    
    Args:
        relation_uid: 知识库ID
        
    Returns:
        JSONResponse: 历史版本记录列表
    """
    try:
        # 初始化模型
        history_model = MapInfoHistoryModel()
        await history_model.initialize()
        
        # 获取历史版本记录
        history_list = await history_model.get_history_by_uid(relation_uid)
        
        # 关闭连接
        await history_model.close()
        
        return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message="获取历史版本成功",
                data={
                    "history": history_list
                }
            ).dict()
        )
        
    except Exception as e:
        logger.error(f"获取历史版本失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"获取历史版本失败: {str(e)}",
                data={}
            ).dict()
        )

@router.delete("/history/{version}")
async def delete_history(
    relation_uid: str = Path(..., description="知识库ID"),
    version: str = Path(..., description="版本号")
):
    """
    删除指定版本的历史记录
    
    Args:
        relation_uid: 知识库ID
        version: 版本号
        
    Returns:
        JSONResponse: 操作结果
    """
    try:
        # 初始化模型
        history_model = MapInfoHistoryModel()
        await history_model.initialize()
        
        # 删除历史记录
        result = await history_model.delete_by_version(relation_uid, version)
        
        # 关闭连接
        await history_model.close()
        
        if result:
            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message="删除历史版本成功",
                    data={}
                ).dict()
            )
        else:
            return JSONResponse(
                status_code=404,
                content=BaseResponse(
                    code=404,
                    message="未找到指定的历史版本",
                    data={}
                ).dict()
            )
        
    except Exception as e:
        logger.error(f"删除历史版本失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"删除历史版本失败: {str(e)}",
                data={}
            ).dict()
        )

@router.post("/rollback/{version}")
async def rollback_version(
    relation_uid: str = Path(..., description="知识库ID"),
    version: str = Path(..., description="版本号")
):
    """
    回退到指定版本
    
    Args:
        relation_uid: 知识库ID
        version: 版本号
        
    Returns:
        JSONResponse: 操作结果
    """
    try:
        # 1. 获取指定版本的信息
        history_model = MapInfoHistoryModel()
        await history_model.initialize()
        
        history_list = await history_model.get_history_by_uid(relation_uid)
        target_version = next((item for item in history_list if item["version"] == version), None)
        
        if not target_version:
            await history_model.close()
            return JSONResponse(
                status_code=404,
                content=BaseResponse(
                    code=404,
                    message="未找到指定的历史版本",
                    data={}
                ).dict()
            )
            
        # 2. 更新 map_info 表的版本信息
        map_info_model = MapInfoModel()
        await map_info_model.initialize()
        
        update_result = await map_info_model.update(
            relation_uid,
            {
                "version": target_version["version"],
                "version_desc": target_version["version_desc"]
            }
        )
        
        # 关闭连接
        await history_model.close()
        await map_info_model.close()
        
        if update_result:
            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message="版本回退成功",
                    data={
                        "version": target_version["version"],
                        "version_desc": target_version["version_desc"]
                    }
                ).dict()
            )
        else:
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message="版本回退失败",
                    data={}
                ).dict()
            )
        
    except Exception as e:
        logger.error(f"版本回退失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"版本回退失败: {str(e)}",
                data={}
            ).dict()
        )

@router.get("/recall_history")
async def get_recall_history(
    relation_uid: str = Path(..., description="知识库ID"),
):
    """
    获取用户的召回测试历史记录
    
    Args:
        relation_uid: 知识库ID
        
    Returns:
        JSONResponse: 历史记录列表
    """
    try:
        # 初始化模型
        history_model = RecallTestHistoryModel()
        await history_model.initialize()
        
        # 获取历史记录
        history_list = await history_model.get_recent_history(relation_uid)
        
        # 关闭连接
        await history_model.close()
        
        return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message="获取召回测试历史记录成功",
                data={
                    "history": history_list
                }
            ).dict()
        )
        
    except Exception as e:
        logger.error(f"获取召回测试历史记录失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"获取召回测试历史记录失败: {str(e)}",
                data={}
            ).dict()
        )

@router.get("/node_properties")
async def get_node_properties(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    获取节点的属性类型信息
    
    Args:
        relation_uid: 知识库ID
        
    Returns:
        JSONResponse: 属性类型信息
    """
    try:
        # 初始化模型
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()
        
        # 获取属性类型
        properties = await neo4j_model.get_node_properties()
        
        # 获取节点类型
        node_types = await neo4j_model.get_node_types()
        
        # 关闭连接
        await neo4j_model.close()
        
        return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message="获取节点属性类型成功",
                data={
                    "properties": properties,
                    "node_types": node_types
                }
            ).dict()
        )
        
    except Exception as e:
        logger.error(f"获取节点属性类型失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"获取节点属性类型失败: {str(e)}",
                data={}
            ).dict()
        )


@router.post("/search_relations")
async def search_relations(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="请求数据")
):
    """
    搜索实体并获取其关系
    
    Args:
        relation_uid: 知识库ID
        data: 请求数据，包含：
            - content: 查询信息
            - topk: 返回数量
            - field: 搜索字段
            - method: 匹配方式
            - target_type: 目标实体类型列表
            - target_types: 目标实体类型列表（与target_type相同）
            - type: 匹配方式
            
    Returns:
        JSONResponse: 搜索结果和关系
    """
    try:
        # 验证必要参数
        if not data.get("content"):
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="查询信息不能为空",
                    data={}
                ).dict()
            )
            
        if not data.get("field"):
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="搜索字段不能为空",
                    data={}
                ).dict()
            )
            
        # 获取目标类型列表，支持两种参数名称
        target_types = data.get("target_types") or data.get("target_type")
        if not target_types:
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="目标实体类型不能为空",
                    data={}
                ).dict()
            )
            
        # 确保target_types是列表类型
        if isinstance(target_types, str):
            target_types = [target_types]
            
        # 初始化模型
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()
        
        # 搜索实体
        entities = await neo4j_model.search_entities(
            field=data["field"],
            value=data["content"],
            method=data.get("method", "cover"),
            topk=data.get("topk", 10),
            target_types=target_types
        )
        
        # 获取每个实体的关系
        result = []
        for entity in entities:
            # 获取实体的所有一级关系
            relations = await neo4j_model.find_entity_relations(str(entity["id"]))
            entity['relations'] = relations
            # 构建结果
            result.append(entity)
        
        # 关闭连接
        await neo4j_model.close()
        
        # 保存查询历史记录
        history_model = RecallTestHistoryModel()
        await history_model.initialize()
        try:
            await history_model.insert_all([{
                "uid": relation_uid,
                "type": "模糊匹配" if data["type"] == "txt" else "向量检索",
                "content": data["content"],
                "time": datetime.now()
            }])
        finally:
            await history_model.close()
        
        return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message="搜索成功",
                data=result
            ).dict()
        )
        
    except Exception as e:
        logger.error(f"搜索失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"搜索失败: {str(e)}",
                data={}
            ).dict()
        )

@router.delete("/delete_relation/{relation_id}", response_model=BaseResponse)
async def delete_relation(
    relation_uid: str = Path(..., description="知识库ID"),
    relation_id: str = Path(..., description="关系ID")
):
    """
    根据关系ID删除知识图谱中的关系

    Args:
        relation_uid: 知识库ID (图谱标签的一部分)
        relation_id: 要删除的关系ID

    Returns:
        BaseResponse: 响应结果，包含：
            - code: 状态码
                - 200: 删除成功f
                - 404: 关系未找到或知识库信息不存在
                - 500: 服务器错误
            - message: 响应消息
            - data: {}
    """

    neo4j_model = None  # 初始化为 None

    try:
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()

        deleted = await neo4j_model.delete_relationship(relation_id)

        if deleted:
            logger.info(f"成功删除关系 {relation_id} (知识库: {relation_uid}")
            return BaseResponse(
                code=200,
                message=f"关系删除成功: {relation_id}",
                data={}
            )
        else:
            logger.warning(f"尝试删除关系 {relation_id} 但未找到 (知识库: {relation_uid}")
            return BaseResponse(
                code=404,
                message=f"关系未找到或无法删除: {relation_id}",
                data={}
            )

    except Exception as e:
        logger.error(f"删除关系 {relation_id} (知识库: {relation_uid}) 时发生未处理的错误: {e}", exc_info=True)
        return BaseResponse(
            code=500,
            message=f"删除关系时发生内部服务器错误",
            data={}
        )
    finally:
        # 5. 关闭 Neo4j 连接 (确保即使出错也关闭)
        if neo4j_model:
            await neo4j_model.close()

# @router.get("/get_doc_list", response_model=BaseResponse)
# async def get_doc_list(
#     relation_uid: str = Path(..., description="知识库ID")
# ):
#     """
#     获取知识库下的文档列表
#
#     Args:
#         relation_uid: 知识库ID
#
#     Returns:
#         BaseResponse: 响应对象，包含：
#             - document_name: 文档名称
#             - entity_count: 实体数量
#             - status: 文档状态
#             - create_time: 创建时间
#     """
#     try:
#         map_doc_model = MapDocModel()
#         await map_doc_model.initialize()
#         try:
#             # 调用查询方法获取文档列表
#             doc_list = await map_doc_model.get_doc_mappings_by_kb_uid(relation_uid)
#             print(doc_list)
#
#             return BaseResponse(
#                 code=200,
#                 message="获取文档列表成功",
#                 data=doc_list
#             )
#
#         finally:
#             await map_doc_model.close()
#
#     except Exception as e:
#         logger.error(f"获取文档列表失败: {str(e)}")
#         return BaseResponse(
#             code=500,
#             message="获取文档列表失败",
#             data={}
#         )
@router.get("/get_doc_list", response_model=BaseResponse)
async def get_doc_list(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    获取知识库下的文档列表（来自 map_doc_api 表）

    Args:
        relation_uid: 知识库ID

    Returns:
        BaseResponse: 响应对象，包含：
            - document_name: 文档名称
            - entity_count: 实体数量
            - status: 文档状态
            - create_time: 创建时间
    """
    try:
        # 使用 MapDocApiModel 替换原来的 MapDocModel
        map_doc_api_model = MapDocApiModel()
        await map_doc_api_model.initialize()
        try:
            # 查询文档列表
            doc_list = await map_doc_api_model.get_doc_mappings_by_kb_uid(relation_uid)

            if not doc_list:
                return BaseResponse(
                    code=200,
                    message="未找到相关文档",
                    data=[]
                )

            # 直接返回文件名列表，不包装成对象
            return BaseResponse(
                code=200,
                message="获取文档列表成功",
                data=doc_list
            )

        finally:
            await map_doc_api_model.close()

    except Exception as e:
        logger.error(f"获取文档列表失败: {str(e)}")
        return BaseResponse(
            code=500,
            message="获取文档列表失败",
            data={}
        )


@router.delete("/delete_endpoints_doc", response_model=BaseResponse)
async def delete_endpoints_doc(
    relation_uid: str = Path(..., description="知识库ID"),
    document_name: str = Query(..., description="文档名称")
):
    """
    删除知识库下的文档映射记录
    
    Args:
        relation_uid: 知识库ID
        document_name: 文档名称
        
    Returns:
        BaseResponse: 响应对象
    """
    try:
        map_doc_model = MapDocApiModel()
        await map_doc_model.initialize()
        try:
            # 调用删除方法
            success = await map_doc_model.delete_doc_mapping_by_name(document_name, relation_uid)
            
            if success:
                return BaseResponse(
                    code=200,
                    message=f"成功删除文档映射记录: {document_name}",
                    data={}
                )
            else:
                return BaseResponse(
                    code=404,
                    message=f"未找到文档映射记录: {document_name}",
                    data={}
                )
                
        finally:
            await map_doc_model.close()
            
    except Exception as e:
        logger.error(f"删除文档映射记录失败: {str(e)}")
        return BaseResponse(
            code=500,
            message="删除文档映射记录失败",
            data={}
        )

@router.post("/endpoints_upload", response_model=BaseResponse)
async def endpoints_upload(
        relation_uid: str = Path(..., description="知识库ID"),
        file: UploadFile = File(..., description="上传的Swagger JSON文件")
):
    """
    上传并处理Swagger文档，提取API端点和组件信息存储到数据库

    Args:
        relation_uid: 知识库ID
        file: 上传的Swagger JSON文件

    Returns:
        BaseResponse: 响应对象
    """
    try:
        # 1. 检查文件后缀是否为json
        file_name = file.filename
        file_ext = os.path.splitext(file_name)[1].lower()
        if file_ext != '.json':
            return BaseResponse(
                code=400,
                message="只支持上传JSON文件",
                data={}
            )

        # 2. 创建临时文件保存上传的文件
        temp_file_path = f"temp_{uuid.uuid4().hex}_{file_name}"
        try:
            with open(temp_file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
        except Exception as e:
            logger.error(f"保存上传文件失败: {str(e)}")
            return BaseResponse(
                code=500,
                message="保存上传文件失败",
                data={}
            )

        # 3. 创建模型实例
        api_model = MapDocApiModel()
        comp_model = MapDocComponentsModel()

        try:
            # 4. 初始化数据库连接
            await api_model.initialize()
            await comp_model.initialize()

            # 5. 检查是否存在同名文档（两个表都检查）
            api_exists = await api_model.check_doc_name(file_name, relation_uid)
            comp_exists = await comp_model.check_doc_name(file_name, relation_uid)

            if api_exists or comp_exists:
                logger.warning(f"知识库 {relation_uid} 中已存在同名文档: {file_name}")
                return BaseResponse(
                    code=400,
                    message=f"知识库中已存在同名文档: {file_name}",
                    data={}
                )

            # 6. 提取API端点信息
            try:
                logger.info(f"临时文件路径: {temp_file_path}")
                logger.info(f"文件是否存在: {os.path.exists(temp_file_path)}")

                api_extractor = ExtractSwaggerPathEndpoints(temp_file_path)
                api_entities = api_extractor.extract_swagger_endpoints()

                if not api_entities:
                    logger.warning("未能提取到任何 API 端点，请检查文件内容是否符合 Swagger/OpenAPI 格式")
                    return BaseResponse(
                        code=400,
                        message="未能提取到有效的 API 实体",
                        data={}
                    )

                logger.info(f"从 {file_name} 中提取到 {len(api_entities)} 个API端点")
            except Exception as e:
                logger.error(f"提取API端点失败: {str(e)}", exc_info=True)
                return BaseResponse(
                    code=500,
                    message="提取API端点失败",
                    data={}
                )

            # 7. 提取组件信息
            try:
                comp_extractor = ExtractSwaggerComponents(temp_file_path)
                comp_entities = comp_extractor.extract_swagger_components()
                logger.info(f"从 {file_name} 中提取到 {len(comp_entities)} 个组件")
            except Exception as e:
                logger.error(f"提取组件失败: {str(e)}")
                return BaseResponse(
                    code=500,
                    message="提取组件失败",
                    data={}
                )

            # 8. 保存API端点数据到map_doc_api表
            api_success = 0
            for entity in api_entities:
                try:
                    # 提取API实体信息
                    path = entity.get("path", "")
                    method = entity.get("method", "").upper()
                    summary = entity.get("summary", "")

                    # 实体名称使用"方法 路径"格式，但确保不会超过长度限制
                    entity_name = f"{method} {path}"
                    if len(entity_name) > 100:
                        entity_name = entity_name[:100]

                    # 创建API记录
                    await api_model.create_api_record(
                        knowledge_base_uid=relation_uid,
                        document_name=file_name,
                        entity_name=entity_name,
                        path=path,
                        method=method,
                        tags=entity.get("tags", []),
                        summary=summary,
                        parameters=entity.get("parameters", []),
                        request_body=entity.get("request_body", {}),
                        responses=entity.get("responses", {}),
                        status="未嵌入"
                    )
                    api_success += 1
                except Exception as e:
                    logger.error(f"保存API端点失败: {str(e)}")
                    logger.debug(f"失败API实体: {json.dumps(entity, indent=2)}")

            # 9. 保存组件数据到map_doc_components表
            comp_success = 0
            for component in comp_entities:
                try:
                    # 提取组件信息
                    schema_name = component.get("schema_name", "")

                    # 创建组件记录
                    await comp_model.create_component_record(
                        knowledge_base_uid=relation_uid,
                        document_name=file_name,
                        entity_name=schema_name,
                        schema_type=component.get("type", ""),
                        title=component.get("title", ""),
                        default_value=component.get("default_value"),
                        enum_values=component.get("enum_values", []),
                        properties=component.get("properties", {}),
                        required_properties=component.get("required_properties", []),
                        status="未嵌入"
                    )
                    comp_success += 1
                except Exception as e:
                    logger.error(f"保存组件失败: {str(e)}")
                    logger.debug(f"失败组件: {json.dumps(component, indent=2)}")

            # 10. 返回结果
            total_api = len(api_entities)
            total_comp = len(comp_entities)
            total_success = api_success + comp_success
            total_failed = (total_api - api_success) + (total_comp - comp_success)

            message = f"成功保存 {api_success} 个API端点和 {comp_success} 个组件"
            if total_failed > 0:
                message += f"（失败: {total_failed}）"

            return BaseResponse(
                code=200,
                message=message,
                data={
                    "api": {
                        "total": total_api,
                        "success": api_success,
                        "failed": total_api - api_success
                    },
                    "components": {
                        "total": total_comp,
                        "success": comp_success,
                        "failed": total_comp - comp_success
                    }
                }
            )

        except Exception as e:
            logger.error(f"数据库操作失败: {str(e)}")
            return BaseResponse(
                code=500,
                message="数据库操作失败",
                data={}
            )
        finally:
            # 确保清理资源
            await api_model.close()
            await comp_model.close()
            try:
                os.remove(temp_file_path)
            except Exception as e:
                logger.warning(f"删除临时文件失败: {str(e)}")

    except Exception as e:
        logger.error(f"处理上传文件失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"处理过程中发生错误: {str(e)}",
            data={}
        )

@router.get("/get_entiy_list", response_model=BaseResponse)
async def get_entiy_list(
    relation_uid: str = Path(..., description="知识库ID"),
    # document_name: str = Query(..., description="文档名称")
    document_name: str = Query(None, description="文档名称（可选）")
):

    """
    获取指定知识库下指定文档的所有实体详细信息
    """
    try:
        map_doc_model = MapDocApiModel()
        await map_doc_model.initialize()
        try:
            if document_name:
                entity_list = await map_doc_model.get_api_mappings_by_kb_uid_and_doc_name(relation_uid, document_name)
            else:
                entity_list = await map_doc_model.get_api_mappings_by_kb_uid_all(relation_uid)

            # 新增字段 parameters_list 和 request_body_list
            for entity in entity_list:
                # 提取 parameters.name -> parameters_list
                parameters = entity.get("parameters", [])
                name_list = [param.get("name") for param in parameters if param.get("name")]
                entity["parameters_list"] = "、".join(name_list) if name_list else ""

                # 提取 request_body.example 的 keys -> request_body_list
                request_body = entity.get("request_body", {})
                example_str = request_body.get("content", {}).get("application/json", {}).get("example", "")
                try:
                    if isinstance(example_str, str) and example_str.strip():
                        example_dict = json.loads(example_str)
                        keys = list(example_dict.keys())
                        entity["request_body_list"] = "、".join(keys) if keys else ""
                    else:
                        entity["request_body_list"] = ""
                except Exception as e:
                    logger.warning(f"解析 request_body.example 失败: {str(e)}")
                    entity["request_body_list"] = ""

                # 新增：提取 responses 中 200 或 default 的 example 的 keys -> responses_list
                responses = entity.get("responses", {})
                example_data = None

                if "200" in responses:
                    example_data = responses["200"].get("content", {}).get("application/json", {}).get("example", "")
                elif "default" in responses:
                    example_data = responses["default"].get("content", {}).get("application/json", {}).get("example", "")

                try:
                    if isinstance(example_data, str) and example_data.strip():
                        example_dict = json.loads(example_data)
                        keys = list(example_dict.keys())
                        entity["responses_list"] = "、".join(keys) if keys else ""
                    else:
                        entity["responses_list"] = ""
                except Exception as e:
                    logger.warning(f"解析 responses.example 失败: {str(e)}")
                    entity["responses_list"] = ""

            return BaseResponse(
                code=200,
                message="获取实体列表成功",
                data=entity_list
            )
        finally:
            await map_doc_model.close()

    except Exception as e:
        logger.error(f"获取实体列表失败: {str(e)}")
        return BaseResponse(
            code=500,
            message="获取实体列表失败",
            data={}
        )

@router.get("/get_data_model_list/")
async def get_data_model_list(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    获取指定知识库下所有组件记录的完整信息（不依赖文档名称）

    Args:
        relation_uid: 知识库ID

    Returns:
        BaseResponse: 响应对象，包含：
            - 所有字段信息（包括 enum_values、properties 等 JSON 字段）
    """
    try:
        component_model = MapDocComponentsModel()
        await component_model.initialize()

        try:
            # 调用封装好的数据库方法，只使用 knowledge_base_uid 查询所有组件
            results = await component_model.get_component_mappings_by_kb_uid_and_doc_name(
                knowledge_base_uid=relation_uid
            )

            if not results:
                return BaseResponse(
                    code=200,
                    message="未找到相关组件记录",
                    data=[]
                )

            return BaseResponse(
                code=200,
                message="获取组件记录成功",
                data=results
            )

        finally:
            await component_model.close()

    except Exception as e:
        logger.error(f"获取组件记录失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"获取组件记录失败: {str(e)}",
            data={}
        )

@router.post("/entities")
async def add_entities(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="实体ID列表")
):
    """
    批量添加实体节点（基于 map_doc_api 表）

    Args:
        relation_uid: 知识库ID
        data: 实体ID列表，包含以下字段：
            - entity_list: 实体ID列表，如 [1019, 1020]

    Returns:
        JSONResponse: 包含操作结果的响应
    """
    try:
        # 获取知识库版本信息
        map_model = MapInfoModel()
        await map_model.initialize()
        try:
            map_info = await map_model.get_map_info_by_uid(relation_uid)
            version = map_info.get("version", "V1")
        finally:
            await map_model.close()

        # 获取实体ID列表
        entity_ids = data.get("entity_list", [])
        if not entity_ids:
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="实体ID列表为空",
                    data={}
                ).dict()
            )

        # 初始化MapDocApi模型
        map_doc_model = MapDocApiModel()
        await map_doc_model.initialize()

        # 初始化Neo4j模型
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()

        success_nodes = []
        failed_nodes = []
        skipped_nodes = []  # 新增：跳过添加的节点

        try:
            for entity_id in entity_ids:
                try:
                    # 获取API实体信息
                    entity_info = await map_doc_model.get_api_record_by_id(entity_id)
                    if not entity_info:
                        failed_nodes.append({
                            "id": entity_id,
                            "error": "实体不存在"
                        })
                        continue

                    # 检查实体是否属于当前知识库
                    if entity_info.get("knowledge_base_uid") != relation_uid:
                        failed_nodes.append({
                            "id": entity_id,
                            "error": "实体不属于当前知识库"
                        })
                        continue

                    # 构建节点数据
                    method = entity_info.get("method", "").upper()
                    path = entity_info.get("path", "")
                    entity_name = f"{method} {path}"  # 方法+路径组成唯一标识

                    # 查询是否已存在相同 method + path 的节点
                    existing_node = await neo4j_model.find_node_by_method_and_path(method, path)
                    if existing_node:
                        skipped_nodes.append({
                            "id": entity_id,
                            "reason": f"已存在方法为 '{method}' 且路径为 '{path}' 的接口"
                        })
                        await map_doc_model.update_status(entity_id, "已跳过（重复）")
                        continue

                    # 自动生成节点ID
                    node_id = str(uuid.uuid4())

                    node_data = {
                        "id": node_id,
                        "name": entity_name,
                        "type": "API",  # 固定类型为 API
                        "version": version,
                        "path": path,
                        "method": method,
                        "summary": entity_info.get("summary", ""),
                        "tags": entity_info.get("tags", []),
                        "parameters": entity_info.get("parameters", []),
                        "request_body": entity_info.get("request_body", {}),
                        "responses": entity_info.get("responses", {})
                    }

                    # 创建节点
                    success = await neo4j_model.create(node_data)

                    if success:
                        # 更新map_doc_api表中对应记录的状态为"已嵌入"
                        await map_doc_model.update_status(entity_id, "已嵌入")
                        success_nodes.append(node_data)
                    else:
                        # 更新状态为"嵌入失败"
                        await map_doc_model.update_status(entity_id, "嵌入失败")
                        failed_nodes.append({
                            "id": entity_id,
                            "error": "创建节点失败"
                        })

                except Exception as e:
                    # 更新状态为"嵌入失败"
                    await map_doc_model.update_status(entity_id, "嵌入失败")
                    failed_nodes.append({
                        "id": entity_id,
                        "error": str(e)
                    })
                    logger.error(f"创建节点失败: {str(e)}")

            # 返回批量创建结果
            response_data = {
                "success_nodes": success_nodes,
                "failed_nodes": failed_nodes
            }

            if skipped_nodes:
                response_data["skipped_nodes"] = skipped_nodes

            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message=f"批量添加实体完成，成功: {len(success_nodes)}，失败: {len(failed_nodes)}，跳过: {len(skipped_nodes)}",
                    data=response_data
                ).dict()
            )

        finally:
            # 确保关闭连接
            await map_doc_model.close()
            await neo4j_model.close()

    except Exception as e:
        logger.error(f"批量添加实体失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"批量添加实体失败: {str(e)}",
                data={}
            ).dict()
        )


@router.post("/add_components")
async def add_components(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="组件ID列表")
):
    """
    批量添加组件节点（基于 map_doc_component 表）

    Args:
        relation_uid: 知识库ID
        data: 组件ID列表，包含以下字段：
            - component_list: 组件ID列表，如 [1001, 1002]

    Returns:
        JSONResponse: 包含操作结果的响应
    """
    try:
        # 获取知识库版本信息
        map_model = MapInfoModel()
        await map_model.initialize()
        try:
            map_info = await map_model.get_map_info_by_uid(relation_uid)
            version = map_info.get("version", "V1")
        finally:
            await map_model.close()

        # 获取组件ID列表
        component_ids = data.get("entity_list", [])
        if not component_ids:
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="组件ID列表为空",
                    data={}
                ).dict()
            )

        # 初始化MapDocComponents模型
        component_model = MapDocComponentsModel()
        await component_model.initialize()

        # 初始化Neo4j模型
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()

        success_nodes = []
        failed_nodes = []

        try:
            for component_id in component_ids:
                try:
                    # 获取组件实体信息
                    component_info = await component_model.get_component_record_by_id(component_id)
                    if not component_info:
                        failed_nodes.append({
                            "id": component_id,
                            "error": "组件不存在"
                        })
                        continue

                    # 检查组件是否属于当前知识库
                    if component_info.get("knowledge_base_uid") != relation_uid:
                        failed_nodes.append({
                            "id": component_id,
                            "error": "组件不属于当前知识库"
                        })
                        continue

                    # 自动生成节点ID
                    node_id = str(uuid.uuid4())

                    # 构建节点数据
                    node_data = {
                        "id": node_id,
                        "name": component_info.get("entity_name", ""),
                        "type": "Component",  # 固定类型为 Component
                        "version": version,
                        "schema_type": component_info.get("schema_type", ""),
                        "title": component_info.get("title", ""),
                        "default_value": component_info.get("default_value", ""),
                        "enum_values": component_info.get("enum_values", []),
                        "properties": component_info.get("properties", {}),
                        "required_properties": component_info.get("required_properties", [])
                    }

                    # 创建节点
                    success = await neo4j_model.create(node_data)

                    if success:
                        # 更新map_doc_component表中对应记录的状态为"已嵌入"
                        await component_model.update_status(component_id, "已嵌入")
                        success_nodes.append(node_data)
                    else:
                        # 更新状态为"嵌入失败"
                        await component_model.update_status(component_id, "嵌入失败")
                        failed_nodes.append({
                            "id": component_id,
                            "error": "创建节点失败"
                        })

                except Exception as e:
                    # 更新状态为"嵌入失败"
                    await component_model.update_status(component_id, "嵌入失败")
                    failed_nodes.append({
                        "id": component_id,
                        "error": str(e)
                    })
                    logger.error(f"创建组件节点失败: {str(e)}")

            # 返回批量创建结果
            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message=f"批量添加组件完成，成功: {len(success_nodes)}，失败: {len(failed_nodes)}",
                    data={
                        "success_nodes": success_nodes,
                        "failed_nodes": failed_nodes
                    }
                ).dict()
            )

        finally:
            # 确保关闭连接
            await component_model.close()
            await neo4j_model.close()

    except Exception as e:
        logger.error(f"批量添加组件失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"批量添加组件失败: {str(e)}",
                data={}
            ).dict()
        )


@router.get("/get_graph_api_data")
async def get_graph_api_data(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    查询Neo4j图数据库中的数据，返回类型为API的节点，并新增：
        - parameters_list
        - request_body_list
        - responses_list
    """
    try:
        # 初始化Neo4j模型
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()

        # 查询类型为 API 的节点
        nodes = await neo4j_model.find_nodes_by_type("API")

        # 关闭连接
        await neo4j_model.close()

        # 处理节点数据并添加新字段
        processed_nodes = []
        for node in nodes:
            # 尝试将字符串类型的字段转为 Python 对象
            try:
                parameters = json.loads(node.get("parameters", "[]"))
            except json.JSONDecodeError:
                parameters = []

            try:
                request_body = json.loads(node.get("request_body", "{}"))
            except json.JSONDecodeError:
                request_body = {}

            try:
                responses = json.loads(node.get("responses", "{}"))
            except json.JSONDecodeError:
                responses = {}

            node_data = {
                "id": node.get("id"),
                "name": node.get("name", ""),
                "type": node.get("type", "未知"),
                "path": node.get("path", ""),
                "method": node.get("method", "").upper(),
                "summary": node.get("summary", ""),
                "tags": node.get("tags", []),
                "parameters": parameters,
                "request_body": request_body,
                "responses": responses,
            }

            # 新增字段：parameters_list
            name_list = [param.get("name") for param in parameters if isinstance(param, dict) and param.get("name")]
            node_data["parameters_list"] = "、".join(name_list) if name_list else ""

            # 新增字段：request_body_list
            example_data = ""
            content = request_body.get("content", {})
            if isinstance(content, dict):
                app_json = content.get("application/json", {})
                if isinstance(app_json, dict):
                    example_data = app_json.get("example", "")

            try:
                if isinstance(example_data, str) and example_data.strip():
                    example_dict = json.loads(example_data)
                    keys = list(example_dict.keys())
                    node_data["request_body_list"] = "、".join(keys) if keys else ""
                else:
                    node_data["request_body_list"] = ""
            except Exception as e:
                logger.warning(f"解析 request_body.example 失败: {str(e)}")
                node_data["request_body_list"] = ""

            # 新增字段：responses_list
            example_data = ""
            if isinstance(responses, dict):
                resp_200 = responses.get("200")
                default_resp = responses.get("default")

                if resp_200 and isinstance(resp_200, dict):
                    content = resp_200.get("content", {})
                    if isinstance(content, dict):
                        app_json = content.get("application/json", {})
                        if isinstance(app_json, dict):
                            example_data = app_json.get("example", "")
                elif default_resp and isinstance(default_resp, dict):
                    content = default_resp.get("content", {})
                    if isinstance(content, dict):
                        app_json = content.get("application/json", {})
                        if isinstance(app_json, dict):
                            example_data = app_json.get("example", "")

            try:
                if isinstance(example_data, str) and example_data.strip():
                    example_dict = json.loads(example_data)
                    keys = list(example_dict.keys())
                    node_data["responses_list"] = "、".join(keys) if keys else ""
                else:
                    node_data["responses_list"] = ""
            except Exception as e:
                logger.warning(f"解析 responses.example 失败: {str(e)}")
                node_data["responses_list"] = ""

            processed_nodes.append(node_data)

        return BaseResponse(
            code=200,
            message="获取接口列表成功",
            data=processed_nodes
        )

    except Exception as e:
        logger.error(f"查询图数据失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"查询图数据失败: {str(e)}",
            data={}
        )

@router.get("/entity/count")
async def get_entity_count(
    relation_uid: str = Path(..., description="知识库ID"),
    document_name: str = Query(..., description="文档名称")
):
    """
    获取指定知识库和文档下的实体数量

    Returns:
        JSONResponse: 包含实体数量的响应
    """
    try:
        # 初始化模型
        map_doc_api_model = MapDocApiModel()
        await map_doc_api_model.initialize()

        try:
            # 查询指定知识库和文档下的所有API记录
            entities = await map_doc_api_model.get_api_mappings_by_kb_uid_and_doc_name(relation_uid, document_name)

            # 统计实体数量
            total_count = len(entities)

            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message="获取实体数量成功",
                    data={"total_entities": total_count}
                ).dict()
            )

        finally:
            await map_doc_api_model.close()

    except Exception as e:
        logger.error(f"获取实体数量失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"获取实体数量失败: {str(e)}",
                data={}
            ).dict()
        )

@router.get("/entity/pending_count")
async def get_pending_entity_count(
    relation_uid: str = Path(..., description="知识库ID"),
    document_name: str = Query(..., description="文档名称")
):
    """
    查询指定知识库和文档名下的待嵌入实体数量（status=未嵌入）

    Returns:
        JSONResponse: 包含待嵌入实体数量的响应
    """
    try:
        map_doc_api_model = MapDocApiModel()
        await map_doc_api_model.initialize()

        try:
            # 调用新方法，直接从数据库查询符合条件的数量
            pending_count = await map_doc_api_model.get_pending_entities_count_by_kb_uid_and_doc_name(
                relation_uid, document_name
            )

            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message="获取待嵌入实体数量成功",
                    data={"pending_entities": pending_count}
                ).dict()
            )

        finally:
            await map_doc_api_model.close()

    except Exception as e:
        logger.error(f"获取待嵌入实体数量失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"获取待嵌入实体数量失败: {str(e)}",
                data={}
            ).dict()
        )

@router.get("/entity/orphaned")
async def get_orphaned_entities(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    查询指定知识库中没有关系的实体节点（孤儿实体）

    Args:
        relation_uid: 知识库ID

    Returns:
        JSONResponse: 包含孤儿实体列表的响应
    """
    try:
        # 初始化Neo4j模型，使用relation_uid作为标签
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()

        try:
            # 查询没有关系的实体节点
            orphaned_entities = await neo4j_model.find_orphaned_entities()

            if not orphaned_entities:
                return JSONResponse(
                    status_code=200,
                    content=BaseResponse(
                        code=200,
                        message="未找到孤儿实体",
                        data=[]
                    ).dict()
                )

            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message=f"成功获取 {len(orphaned_entities)} 个孤儿实体",
                    data=orphaned_entities
                ).dict()
            )

        finally:
            await neo4j_model.close()

    except Exception as e:
        logger.error(f"查询孤儿实体失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"查询孤儿实体失败: {str(e)}",
                data={}
            ).dict()
        )

@router.post("/embed_entities_increment")
async def embed_entities_increment(
        relation_uid: str = Path(..., description="知识库ID"),
        data: dict = Body(..., description="请求数据，包含 ids 和 type")
):
    """
    增量提取 API 与组件的关系，并存储到 MySQL 的 api_relations 表中
    匹配规则：
    1. QUERY_QUOTE: 匹配API参数(query类型)与组件属性
    2. BODY_QUOTE: 匹配API请求体与组件属性
    3. {METHOD}_BY: 匹配API响应与组件属性
    """
    task_id = None  # 初始化 task_id
    try:
        # === 插入任务记录 ===
        task_id = str(uuid.uuid4())
        task_api_model = MapTaskApiModel()
        await task_api_model.initialize()

        # 插入初始任务记录（状态 0：待提取）
        await task_api_model.create_task_api_record(
            knowledge_base_id=relation_uid,
            task_id=task_id,
            api_id=None,  # 暂时不关联具体API ID
            status=0  # 状态：待提取
        )

        # 初始化其他模型
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()

        api_relations_model = ApiRelationsModel()
        await api_relations_model.initialize()

        component_model = MapDocComponentsModel()
        await component_model.initialize()

        try:
            # 获取 API 实体
            entity_ids = data.get("ids") or data.get("entity_list")
            if not entity_ids:
                return JSONResponse(
                    status_code=400,
                    content=BaseResponse(code=400, message="实体ID列表不能为空", data={}).dict()
                )

            api_entities = []
            for node_id in entity_ids:
                node = await neo4j_model.find_by_property("id", node_id)
                if node and node[0].get("type") == "API":
                    api_node = node[0]
                    api_node["parameters"] = json_utils.safe_json_load(api_node.get("parameters", "[]"))
                    api_node["request_body"] = json_utils.safe_json_load(api_node.get("request_body", "{}"))
                    api_node["responses"] = json_utils.safe_json_load(api_node.get("responses", "{}"))
                    api_node["method"] = api_node.get("method", "").upper()
                    api_entities.append(api_node)

            if not api_entities:
                # 更新任务状态为“已完成”但未找到API实体
                await task_api_model.update_task_api_status(task_id, 1)
                return JSONResponse(
                    status_code=404,
                    content=BaseResponse(code=404, message="未找到任何API实体", data={}).dict()
                )

            # 获取所有组件
            mysql_components = await component_model.get_all_components_by_kb_uid(relation_uid)
            all_components = []
            for comp in mysql_components:
                all_components.append({
                    "id": comp["id"],
                    "name": comp["entity_name"],
                    "properties": json_utils.safe_json_load(comp["properties"]),
                    "enum_values": json_utils.safe_json_load(comp.get("enum_values", "[]"))
                })

            if not all_components:
                # 更新任务状态为“已完成”但未找到组件
                await task_api_model.update_task_api_status(task_id, 1)
                return JSONResponse(
                    status_code=404,
                    content=BaseResponse(code=404, message="未找到组件定义", data={}).dict()
                )

            # 开始匹配
            success_relations = []
            skipped_relations = []

            # 定义一个集合用于保存所有API ID
            all_api_ids = set()
            for api in api_entities:
                api_name = api["name"]
                method = api["method"]
                all_api_ids.add(api["id"])

                # 1. 匹配QUERY参数 (parameters中in=query的name值)
                query_params = [p["name"] for p in api.get("parameters", [])
                              if isinstance(p, dict) and p.get("in") == "query"]
                # logger.info(f"匹配QUERY参数：{query_params}")
                for param_name in query_params:
                    matched = json_utils.find_matching_components(param_name, all_components)
                    for component in matched:
                        relation_id = await api_relations_model.create_relation(
                            relation_uid=relation_uid,
                            api_name=api_name,
                            api_type="API",
                            data_model=component["name"],
                            relation_type="QUERY_QUOTE",
                            details={
                                "matched_field": param_name,
                                "match_type": component["match_type"],
                                "timestamp": datetime.now().isoformat()
                            }
                        )
                        success_relations.append({
                            "api": api_name,
                            "component": component["name"],
                            "relation_type": "QUERY_QUOTE",
                            "matched_field": param_name
                        })

                # 2. 匹配BODY (request_body中的example)
                request_body = api.get("request_body", {})
                example_data = json_utils.extract_example_from_content(request_body.get("content", {}))

                if example_data:
                    matched = json_utils.find_matching_components_from_data(example_data, all_components)
                    for component in matched:
                        relation_id = await api_relations_model.create_relation(
                            relation_uid=relation_uid,
                            api_name=api_name,
                            api_type="API",
                            data_model=component["name"],
                            relation_type="BODY_QUOTE",
                            details={
                                "matched_fields": component["matched_fields"],
                                "match_type": component["match_type"],
                                "timestamp": datetime.now().isoformat()
                            }
                        )
                        success_relations.append({
                            "api": api_name,
                            "component": component["name"],
                            "relation_type": "BODY_QUOTE",
                            "matched_fields": component["matched_fields"]
                        })

                # 3. 匹配RESPONSE (200或default的example)
                responses = api.get("responses", {})
                example_data = json_utils.extract_example_from_content(responses.get("200", {}).get("content", {}))
                if not example_data:
                    example_data = json_utils.extract_example_from_content(responses.get("default", {}).get("content", {}))

                if example_data:
                    matched = json_utils.find_matching_components_from_data(example_data, all_components)
                    for component in matched:
                        relation_type = f"{method}_BY" if method else "RESPONSE_BY"
                        relation_id = await api_relations_model.create_relation(
                            relation_uid=relation_uid,
                            api_name=api_name,
                            api_type="API",
                            data_model=component["name"],
                            relation_type=relation_type,
                            details={
                                "matched_fields": component["matched_fields"],
                                "match_type": component["match_type"],
                                "timestamp": datetime.now().isoformat()
                            }
                        )
                        success_relations.append({
                            "api": api_name,
                            "component": component["name"],
                            "relation_type": relation_type,
                            "matched_fields": component["matched_fields"]
                        })

            # 更新任务状态为“已完成”
            await task_api_model.update_task_api_status(task_id, 1)

            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message=f"成功创建 {len(success_relations)} 条关系记录",
                    data={
                        "success_relations": success_relations,
                        "skipped_relations": skipped_relations
                    }
                ).dict()
            )

        finally:
            await neo4j_model.close()
            await api_relations_model.close()
            await component_model.close()
            await task_api_model.close()

    except Exception as e:
        logger.error(f"增量提取关系失败: {str(e)}")

        # 更新任务状态为“提取失败”
        if task_id:
            task_api_model = MapTaskApiModel()
            await task_api_model.initialize()
            try:
                await task_api_model.update_task_api_status(task_id, 2)
            finally:
                await task_api_model.close()

        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"增量提取关系失败: {str(e)}",
                data={}
            ).dict()
        )


@router.get("/task_progress", response_model=BaseResponse)
async def get_task_progress():
    """
    查询所有任务的进度信息（无须参数）

    Returns:
        BaseResponse: 响应对象，包含任务进度列表
    """
    try:
        # 初始化任务API模型
        task_api_model = MapTaskApiModel()
        await task_api_model.initialize()

        try:
            # 查询所有任务记录
            tasks = await task_api_model.get_all_tasks_with_progress()

            if not tasks:
                return BaseResponse(
                    code=200,
                    message="暂无任务进度信息",
                    data=[]
                )

            return BaseResponse(
                code=200,
                message="获取任务进度信息成功",
                data=tasks
            )

        finally:
            await task_api_model.close()

    except Exception as e:
        logger.error(f"获取任务进度信息失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"获取任务进度信息失败: {str(e)}",
            data={}
        )


# ==================== 提示词管理相关接口 ====================

class TermDefinition(BaseModel):
    name: str
    definition: str

class BusinessPrompt(BaseModel):
    content: str

class ExtraEvidence(BaseModel):
    name: str
    description: str

class PromptManagementRequest(BaseModel):
    businessIntroduction: str
    termDefinitions: List[TermDefinition]
    businessPrompts: List[BusinessPrompt]
    extraEvidence: List[ExtraEvidence]


@router.post("/save", summary="保存提示词配置")
async def save_prompt_config(
    relation_uid: str = Path(..., description="知识库ID"),
    request: PromptManagementRequest = Body(...)
):
    """保存提示词管理配置"""
    prompt_model = PromptManagementModel()
    try:
        logger.info(f"开始保存提示词配置，knowledge_base_id: {relation_uid}")
        await prompt_model.initialize()
        logger.info("数据库连接初始化成功")

        # 准备数据
        data = {
            'business_introduction': request.businessIntroduction,
            'term_definitions': [term.dict() for term in request.termDefinitions],
            'business_prompts': [prompt.dict() for prompt in request.businessPrompts],
            'extra_evidence': [evidence.dict() for evidence in request.extraEvidence]
        }

        logger.info(f"保存提示词配置: {data}")

        # 使用upsert方法，如果存在则更新，不存在则插入
        logger.info(f"开始执行数据库upsert操作，knowledge_base_id: {relation_uid}")
        result = await prompt_model.upsert_by_knowledge_base_id(relation_uid, data)
        logger.info(f"数据库upsert操作结果: {result}")

        if result > 0:
            logger.info(f"提示词配置保存成功，knowledge_base_id: {relation_uid}")
            return BaseResponse(
                code=200,
                message="提示词配置保存成功",
                data={"saved": True, "knowledge_base_id": relation_uid}
            )
        else:
            logger.error(f"数据库操作返回结果为0，保存失败，knowledge_base_id: {relation_uid}")
            return BaseResponse(
                code=500,
                message="保存失败，数据库操作未成功",
                data={}
            )

    except Exception as e:
        logger.error(f"保存提示词配置失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return BaseResponse(
            code=500,
            message=f"保存失败: {str(e)}",
            data={}
        )
    finally:
        await prompt_model.close()
        logger.info("数据库连接已关闭")


@router.get("/load", summary="加载提示词配置")
async def load_prompt_config(
    relation_uid: str = Path(..., description="知识库ID")
):
    """加载提示词管理配置"""
    prompt_model = PromptManagementModel()
    try:
        logger.info(f"开始加载提示词配置，knowledge_base_id: {relation_uid}")
        await prompt_model.initialize()
        logger.info("数据库连接初始化成功")

        # 从数据库加载配置
        logger.info(f"开始查询数据库，knowledge_base_id: {relation_uid}")
        config = await prompt_model.find_by_knowledge_base_id(relation_uid)
        logger.info(f"数据库查询结果: {config}")

        if config:
            # 转换数据格式以匹配前端期望的格式
            response_data = {
                "businessIntroduction": config.get('business_introduction', ''),
                "termDefinitions": config.get('term_definitions', []),
                "businessPrompts": config.get('business_prompts', []),
                "extraEvidence": config.get('extra_evidence', [])
            }

            logger.info(f"加载提示词配置成功: {relation_uid}, 数据: {response_data}")
        else:
            # 如果没有找到配置，返回默认的空配置
            response_data = {
                "businessIntroduction": "",
                "termDefinitions": [
                    {"name": "", "definition": ""}
                ],
                "businessPrompts": [
                    {"content": ""}
                ],
                "extraEvidence": [
                    {"name": "", "description": ""}
                ]
            }
            logger.info(f"未找到提示词配置，返回默认配置: {relation_uid}")

        return BaseResponse(
            code=200,
            message="加载提示词配置成功",
            data=response_data
        )
    except Exception as e:
        logger.error(f"加载提示词配置失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return BaseResponse(
            code=500,
            message=f"加载失败: {str(e)}",
            data={}
        )
    finally:
        await prompt_model.close()
        logger.info("数据库连接已关闭")
