from typing import Dict, List, Any, Optional
from db.mysql.mysql_pool import MySQLPool
from logger import logging
import json


class RecallTestHistoryModel:
    def __init__(self):
        self.db_pool = MySQLPool()
        self.logger = logging()
        self.table = "recall_test_history"
        
    async def initialize(self):
        """初始化数据库连接池"""
        await self.db_pool.initialize()
        
    async def close(self):
        """关闭连接池，释放资源"""
        await self.db_pool.close()
        
    async def get_recent_history(self, uid: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        获取用户最近的召回测试历史记录
        
        Args:
            uid: 用户UID
            limit: 返回的记录数量，默认为20
            
        Returns:
            List[Dict[str, Any]]: 历史记录列表，按时间倒序排列
        """
        try:
            query = """
                SELECT 
                    id,
                    uid,
                    type,
                    DATE_FORMAT(time, '%%Y-%%m-%%d %%H:%%i:%%s') as time,
                    content
                FROM recall_test_history 
                WHERE uid = %s 
                ORDER BY time DESC
                LIMIT %s
            """
            result = await self.db_pool.fetch_all(query, (uid, limit))
            return result
                    
        except Exception as e:
            self.logger.error(f"获取用户 {uid} 的召回测试历史记录失败: {str(e)}")
            return []
            
    async def insert_all(self, records: List[Dict[str, Any]]) -> bool:
        """
        批量插入召回测试历史记录
        
        Args:
            records: 历史记录列表，每条记录包含：
                - uid: 用户UID
                - type: 类型
                - content: 内容
                - time: 时间
                
        Returns:
            bool: 是否插入成功
        """
        try:
            query = """
                INSERT INTO recall_test_history (
                    uid,
                    type,
                    content,
                    time
                ) VALUES (%s, %s, %s, %s)
            """
            
            # 准备数据
            values = [
                (
                    record["uid"],
                    record["type"],
                    record["content"],
                    record["time"]
                )
                for record in records
            ]
            
            # 执行批量插入
            await self.db_pool.execute_many(query, values)
            return True
            
        except Exception as e:
            self.logger.error(f"插入召回测试历史记录失败: {str(e)}")
            return False
  