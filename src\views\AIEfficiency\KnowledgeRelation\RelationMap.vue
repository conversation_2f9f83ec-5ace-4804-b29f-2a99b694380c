<template>
  <div class="relation-map-container">
    <!-- 加载状态提示 -->
    <div v-if="isLoading" class="loading-overlay">
      <a-spin size="large" />
      <div class="loading-text">正在加载图谱数据...</div>
    </div>

    <!-- 遮罩层 -->
    <div v-if="selectedItem" class="mask" @click="handleMaskClick"></div>

    <!-- 左侧操作面板 -->
    <div class="left-panel">
      <!-- 实体关系预览卡片 -->
      <div class="panel-card">
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ stats?.entityCount || 0 }}</div>
            <div class="stat-label">实体数量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats?.relationCount || 0 }}</div>
            <div class="stat-label">关系数量</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ stats?.categoryCount || 0 }}</div>
            <div class="stat-label">实体类型</div>
          </div>
        </div>
      </div>

      <!-- 操作卡片 -->
      <div class="panel-card">
        <div class="search-section">
          <a-input-search v-model:value="highlightText" placeholder="搜索实体或关系" @search="handleHighlight" />
        </div>
        <div class="action-section">
          <a-button type="primary" @click="showAddEntityModal">
            新增实体
          </a-button>
          <a-button type="primary" @click="showAddRelationModal">
            新增关系
          </a-button>
        </div>
      </div>

      <!-- 详情卡片 -->
      <div v-if="selectedItem" class="panel-card detail-card">
        <div class="detail-list">
          <template v-if="selectedItem.type === 'node'">
            <div class="detail-item">
              <div class="detail-label">NAME</div>
              <div class="detail-value">{{ selectedItem.name }}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">TYPE</div>
              <div class="detail-value">{{ selectedItem.category || selectedItem.relationType }}</div>
            </div>
            <template v-if="selectedItem.fields">
              <div v-for="(value, key) in selectedItem.fields" :key="key" class="detail-item">
                <div class="detail-label">{{ key.toUpperCase() }}</div>
                <div class="detail-value">{{ value }}</div>
              </div>
            </template>
          </template>
          <template v-else-if="selectedItem.type === 'edge'">
            <div class="detail-item" v-for="(value, key) in getRelationAllFields(selectedItem)" :key="key">
              <div class="detail-label">{{ key.toUpperCase() }}</div>
              <div class="detail-value">{{ value }}</div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 中间图谱区域 -->
    <div class="graph-container">
      <div ref="graphRef" class="graph-content"></div>
      <!-- 地图中间底部的搜索框 -->
      <div class="map-search-box">
        <!-- 组合式搜索输入框 -->
        <div class="cypher-query-builder">
          <span class="query-keyword">MATCH (n{{ dynamicEntityTags }})</span>
          <a-input v-model:value="cypherCondition" placeholder="过滤条件，如: WHERE n.name = ''"
            style="flex-grow: 1; margin: 0 5px; width: 300px;" />
          <span class="query-keyword">RETURN n LIMIT</span>
          <a-input-number v-model:value="searchLimit" :min="1" :max="1000" placeholder="25" style="width: 120px;" />
          <a-button type="primary" @click="handleSearch" style="margin-left: 10px;">执行查询</a-button>
        </div>
      </div>
    </div>

    <!-- 右侧编辑卡片 -->
    <div class="right-panel" :class="{ 'show': selectedItem }" v-if="selectedItem">
      <div class="panel-header">
        <h3>{{ selectedItem.type === 'node' ? '实体编辑' : '关系编辑' }}</h3>
        <a-button type="text" class="close-btn" @click="selectedItem = null">
          <CloseOutlined />
        </a-button>
      </div>

      <div class="panel-content">
        <!-- 实体详情 -->
        <template v-if="selectedItem.type === 'node'">
          <div class="entity-preview">
            <div class="entity-relations">
              <div class="relation-list">
                <!-- 上游关系 -->
                <div class="relation-group">
                  <div v-for="relation in getUpstreamRelations(selectedItem.id)" :key="relation.id"
                    class="relation-item">
                    <div class="related-entity">
                      <a-tag color="blue" class="entity-tag">
                        <span class="entity-name">{{ getEntityName(relation.source) }}</span>
                      </a-tag>
                    </div>
                    <div class="relation-arrow upstream">
                      <ArrowLeftOutlined />
                    </div>
                    <div class="relation-details">
                      <div class="relation-type upstream">{{ relation.type }}</div>
                      <!-- 展示关系的所有字段 -->
                      <div v-if="getRelationAllFields(relation)" class="relation-fields">
                        <div v-for="(value, key) in getRelationAllFields(relation)" :key="key" class="relation-field">
                          <span class="field-key">{{ key }}:</span>
                          <span class="field-value">{{ value }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 下游关系 -->
                <div class="relation-group">
                  <div v-for="relation in getDownstreamRelations(selectedItem.id)" :key="relation.id"
                    class="relation-item">
                    <div class="relation-details">
                      <div class="relation-type downstream">{{ relation.type }}</div>
                      <!-- 展示关系的所有字段 -->
                      <div v-if="getRelationAllFields(relation)" class="relation-fields">
                        <div v-for="(value, key) in getRelationAllFields(relation)" :key="key" class="relation-field">
                          <span class="field-key">{{ key }}:</span>
                          <span class="field-value">{{ value }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="relation-arrow downstream">
                      <ArrowRightOutlined />
                    </div>
                    <div class="related-entity">
                      <a-tag color="blue" class="entity-tag">
                        <span class="entity-name">{{ getEntityName(relation.target) }}</span>
                      </a-tag>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="delete-section">
              <a-alert description="将删除当前实体和实体相关联的关系,请谨慎操作!" type="warning" show-icon banner
                style="margin-bottom: 16px" />
              <a-button type="primary" danger block @click="handleDelete">
                删除实体
              </a-button>
            </div>
          </div>
        </template>

        <!-- 关系详情 -->
        <template v-else-if="selectedItem.type === 'edge'">
          <div class="relation-preview">
            <div class="relation-container">
              <div class="entity-box source-entity">
                <a-tag color="blue" class="entity-tag">
                  <span class="entity-name">{{ getEntityName(selectedItem.source) }}</span>
                </a-tag>
              </div>
              <div class="relation-arrow">
                <ArrowRightOutlined />
              </div>
              <div class="entity-box target-entity">
                <a-tag color="blue" class="entity-tag">
                  <span class="entity-name">{{ getEntityName(selectedItem.target) }}</span>
                </a-tag>
              </div>
            </div>
            <div class="relation-edit">
              <div class="section-title">关系类型</div>
              <a-input v-model:value="selectedItem.fields.relationType" placeholder="输入关系类型" class="relation-input" />
            </div>
            <div class="relation-description">
              <div class="section-title">描述</div>
              <a-textarea v-model:value="selectedItem.description" placeholder="在此处编辑关系描述" :rows="3"
                class="description-input" />
            </div>
          </div>
          <div class="delete-section">
            <a-alert description="删除将无法恢复,请谨慎操作!" type="warning" show-icon banner style="margin-bottom: 16px" />
            <a-button type="primary" danger block @click="handleDelete">
              删除关系
            </a-button>
          </div>
        </template>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <a-button type="primary" @click="handleSave">保存</a-button>
          <a-button @click="handleCancel">取消</a-button>
        </div>
      </div>
    </div>

    <!-- 新增实体弹窗 -->
    <a-modal v-model:visible="addEntityModalVisible" title="新增实体" @ok="handleAddEntity"
      @cancel="addEntityModalVisible = false" :width="600">
      <a-form :model="newEntity" layout="vertical">
        <div class="form-row">
          <a-form-item label="实体名称" required class="form-item-name">
            <a-input v-model:value="newEntity.name" placeholder="请输入实体名称" />
          </a-form-item>
          <a-form-item label="实体类型" required class="form-item-type">
            <a-input v-model:value="newEntity.category" placeholder="请输入实体类型" />
          </a-form-item>
        </div>
        <a-form-item label="实体描述">
          <a-textarea v-model:value="newEntity.description" :rows="3" placeholder="请输入实体描述" />
        </a-form-item>

        <!-- 自定义字段 -->
        <div class="custom-fields">
          <div class="fields-header">
            <span class="fields-title">自定义字段</span>
            <a-button type="link" @click="addCustomField">
              <PlusOutlined />
              添加字段
            </a-button>
          </div>
          <div class="fields-list">
            <div v-for="(field, index) in newEntity.customFields" :key="index" class="field-item">
              <a-input v-model:value="field.key" placeholder="字段名" style="width: 120px" />
              <span class="field-separator">:</span>
              <a-input v-model:value="field.value" placeholder="字段值" style="flex: 1" />
              <a-button type="link" danger @click="removeCustomField(index)">
                <CloseOutlined />
              </a-button>
            </div>
          </div>
        </div>
      </a-form>
    </a-modal>

    <!-- 新增关系弹窗 -->
    <a-modal v-model:visible="addRelationModalVisible" title="新增关系" @ok="handleAddRelation"
      @cancel="addRelationModalVisible = false" :width="800">
      <a-form :model="newRelation" layout="vertical">
        <div class="relation-form">
          <!-- 关系主体部分 -->
          <a-row class="relation-main" align="middle">
            <!-- 源实体 -->
            <a-col :flex="4">
              <a-form-item>
                <a-select v-model:value="newRelation.source" placeholder="请选择源实体（源实体）" class="entity-select" show-search
                  :filter-option="filterOption" style="width: 220px">
                  <a-select-option v-for="entity in graphData.nodes" :key="entity.id" :value="entity.id"
                    :label="entity.name">
                    {{ entity.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <!-- 箭头 -->
            <a-col :flex="1" class="relation-arrow-col">
              <ArrowRightOutlined />
            </a-col>
            <!-- 目标实体 -->
            <a-col :flex="4">
              <a-form-item>
                <a-select v-model:value="newRelation.target" placeholder="请选择目标实体（目标实体）" class="entity-select"
                  show-search :filter-option="filterOption" style="width: 220px">
                  <a-select-option v-for="entity in graphData.nodes.filter(node => node.id !== newRelation.source)"
                    :key="entity.id" :value="entity.id" :label="entity.name">
                    {{ entity.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 关系描述 -->
          <a-form-item label="关系描述">
            <a-textarea v-model:value="newRelation.description" :rows="3" placeholder="请输入关系描述" />
          </a-form-item>

          <!-- 自定义字段 for relation -->
          <div class="custom-fields">
            <div class="fields-header">
              <span class="fields-title">自定义字段</span>
              <a-button type="link" @click="addRelationCustomField">
                <PlusOutlined />
                添加字段
              </a-button>
            </div>
            <div class="fields-list">
              <!-- TYPE字段，默认且不可删除 -->
              <div class="field-item">
                <a-input v-model:value="newRelation.customFields[0].key" disabled style="width: 120px" />
                <span class="field-separator">:</span>
                <a-input v-model:value="newRelation.customFields[0].value" placeholder="请输入类型" style="flex: 1" />
              </div>
              <!-- 其他可编辑可删除字段 -->
              <div v-for="(field, index) in newRelation.customFields.slice(1)" :key="index + 1" class="field-item">
                <a-input v-model:value="field.key" placeholder="字段名" style="width: 120px" />
                <span class="field-separator">:</span>
                <a-input v-model:value="field.value" placeholder="字段值" style="flex: 1" />
                <a-button type="link" danger @click="removeRelationCustomField(index + 1)">
                  <CloseOutlined />
                </a-button>
              </div>
            </div>
          </div>
        </div>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick, onUnmounted, computed } from 'vue';
import { PlusOutlined, LinkOutlined, CloseOutlined, ArrowRightOutlined, ApiOutlined, ArrowLeftOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import http from '@/utils/ai/http';
import { useGraphManager, useOpacityManager } from './composables/useGraphManager';

const props = defineProps({
  mapInfo: {
    type: Object,
    default: null
  }
});

// 定义emit事件
const emit = defineEmits(['refreshMap']);

// 使用图谱管理器
const {
  chart,
  graphRef,
  hoverTimer,
  highlightedDataIndex,
  highlightedDataType,
  initGraph,
  updateGraphData: updateGraphDataFromManager,
  handleResize,
  disposeChart
} = useGraphManager();

// 数据状态
const graphData = ref({
  nodes: [],
  links: [],
  categories: []
});

// 统计数据
const stats = ref({
  entityCount: 0,
  relationCount: 0,
  categoryCount: 0
});

// 用于存储来自API响应的原始标签字符串
const apiResponseRawTags = ref('');

// 动态生成实体标签字符串
const dynamicEntityTags = computed(() => {
  if (apiResponseRawTags.value) {
    const formattedTags = apiResponseRawTags.value.replace(/、/g, ':');
    return ':' + formattedTags;
  } else if (graphData.value.categories && graphData.value.categories.length > 0) {
    const formattedCategories = graphData.value.categories
      .map(cat => cat.name.replace(/,/g, ':'))
      .join(':');
    return ':' + formattedCategories;
  } else {
    return ':Entity';
  }
});

// 高亮和查询相关
const highlightText = ref('');
const cypherCondition = ref('');
const searchResults = ref([]);

// 选中的节点或边
const selectedItem = ref(null);

// 实体和关系类型
const categories = ref([]);
const relationTypes = ref([]);

// 弹窗状态
const addEntityModalVisible = ref(false);
const addRelationModalVisible = ref(false);

// 新增实体和关系的数据
const newEntity = ref({
  name: '',
  category: '',
  description: '',
  customFields: []
});

const newRelation = ref({
  source: '',
  target: '',
  description: '',
  customFields: [
    { key: 'TYPE', value: '' }
  ]
});

// 加载状态
const isLoading = ref(true);

// 搜索限制
const searchLimit = ref(25);

// 处理节点点击
const handleNodeClick = (node) => {
  const fullNodeData = props.mapInfo?.data?.nodes?.find(n => n.id === node.id);
  selectedItem.value = {
    type: 'node',
    ...node,
    fields: fullNodeData?.fields || {}
  };

  // 更新节点透明度
  const { updateNodeOpacity } = useOpacityManager(chart);
  updateNodeOpacity(node.id);
};

// 处理边点击
const handleEdgeClick = (edge) => {
  const fullEdgeData = props.mapInfo?.data?.links?.find(l =>
    (l.id && l.id === edge.id) ||
    (l.source === edge.source && l.target === edge.target)
  );

  if (fullEdgeData) {
    selectedItem.value = {
      ...fullEdgeData,
      type: 'edge',
      fields: {
        ...(fullEdgeData.fields || {}),
        relationType: fullEdgeData.type,
      },
    };
  } else {
    selectedItem.value = {
      ...edge,
      type: 'edge',
      fields: {
        ...(edge.fields || {}),
        relationType: edge.type,
      }
    };
  }
};

// 初始化图谱
const initGraphWithData = () => {
  initGraph(graphData.value, categories.value, handleNodeClick, handleEdgeClick);
};

// 更新图谱数据
const updateGraphData = (mapInfo) => {
  console.log('收到的地图信息:', mapInfo);

  if (!mapInfo || !mapInfo.data) {
    console.warn('地图信息数据不完整');
    isLoading.value = false;
    return;
  }

  apiResponseRawTags.value = mapInfo.tags || '';

  const transformedData = updateGraphDataFromManager(mapInfo, handleNodeClick);

  if (!transformedData) {
    console.warn('数据转换失败');
    isLoading.value = false;
    return;
  }

  graphData.value = transformedData;

  categories.value = mapInfo.data.categories.map(cat => cat.name);
  relationTypes.value = [...new Set(mapInfo.data.links.map(link => link.type))];

  stats.value = {
    entityCount: mapInfo.data.nodes.length,
    relationCount: mapInfo.data.links.length,
    categoryCount: mapInfo.data.categories.length
  };

  isLoading.value = false;
};

// 监听 mapInfo 变化
watch(() => props.mapInfo, (newVal) => {
  if (newVal) {
    updateGraphData(newVal);
  }
}, { deep: true });

// 处理搜索
const handleSearch = async () => {
  const cypherQuery = `MATCH (n${dynamicEntityTags.value}) ${cypherCondition.value || ''} RETURN n LIMIT ${searchLimit.value || 25}`;
  console.log("前端组合的伪 Cypher 查询:", cypherQuery);

  const currentPath = window.location.pathname;
  const relationId = currentPath.split('/relation/')[1];

  if (!relationId) {
    message.error('无法获取知识库ID');
    return;
  }

  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
  if (!userInfo.userId || !userInfo.username) {
    message.error('用户信息获取失败');
    return;
  }

  isLoading.value = true;
  try {
    const res = await http.get(`/knowledge_api/${relationId}/map`, {
      params: {
        cypher: cypherQuery,
        username: userInfo.username,
        user_id: userInfo.userId
      }
    });

    if (res.data.code === 200) {
      console.log('查询结果:', res.data);
      updateGraphData(res.data);
      message.success('查询成功');
    } else {
      message.error(res.data.message || '查询失败');
    }
  } catch (error) {
    console.error('查询失败:', error);
    message.error('查询失败');
  } finally {
    isLoading.value = false;
  }
};

// 显示新增实体弹窗
const showAddEntityModal = () => {
  newEntity.value = {
    name: '',
    category: '',
    description: '',
    customFields: []
  };
  addEntityModalVisible.value = true;
};

// 显示新增关系弹窗
const showAddRelationModal = () => {
  newRelation.value = {
    source: '',
    target: '',
    description: '',
    customFields: [
      { key: 'TYPE', value: '' }
    ]
  };
  addRelationModalVisible.value = true;
};

// 添加自定义字段
const addCustomField = () => {
  newEntity.value.customFields.push({
    key: '',
    value: ''
  });
};

// 删除自定义字段
const removeCustomField = (index) => {
  newEntity.value.customFields.splice(index, 1);
};

// 验证字段名称
const validateFieldName = (name) => {
  const nameRegex = /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5_]*$/;
  return nameRegex.test(name);
};

// 处理新增实体
const handleAddEntity = async () => {
  if (!newEntity.value.name) {
    message.error('请输入实体名称');
    return;
  }

  const nameRegex = /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5_]*$/;
  if (!nameRegex.test(newEntity.value.name)) {
    message.error('实体名称不能以数字开头，且不能包含. : -等特殊字符');
    return;
  }

  if (!newEntity.value.category) {
    message.error('请输入实体类型');
    return;
  }

  if (!nameRegex.test(newEntity.value.category)) {
    message.error('实体类型不能以数字开头，且不能包含. : -等特殊字符');
    return;
  }

  for (const field of newEntity.value.customFields) {
    if (field.key && !validateFieldName(field.key)) {
      message.error(`自定义字段"${field.key}"不能以数字开头，且不能包含. : -等特殊字符`);
      return;
    }
  }

  try {
    const currentPath = window.location.pathname;
    const relationId = currentPath.split('/relation/')[1];

    if (!relationId) {
      message.error('无法获取知识库ID');
      return;
    }

    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    if (!userInfo.userId || !userInfo.username) {
      message.error('用户信息获取失败');
      return;
    }

    const fields = {
      description: newEntity.value.description || ''
    };
    newEntity.value.customFields.forEach(field => {
      if (field.key && field.value) {
        fields[field.key] = field.value;
      }
    });

    const entityData = {
      name: newEntity.value.name,
      type: newEntity.value.category,
      fields: fields
    };

    const res = await http.post(`/knowledge_api/${relationId}/entity`, {
      entity_data: entityData
    }, {
      params: {
        username: userInfo.username,
        user_id: userInfo.userId
      }
    });

    if (res.data.code === 200) {
      const newId = `entity_${Date.now()}`;

      const entity = {
        id: newId,
        name: newEntity.value.name,
        category: newEntity.value.category,
        description: newEntity.value.description,
        fields: fields,
        symbolSize: 40
      };

      graphData.value.nodes.push(entity);

      if (chart.value) {
        chart.value.setOption({
          series: [{
            data: graphData.value.nodes
          }]
        });
      }

      addEntityModalVisible.value = false;

      newEntity.value = {
        name: '',
        category: '',
        description: '',
        customFields: []
      };

      message.success('实体添加成功');

      emit('refreshMap');

      fetchMapData();
    } else {
      message.error(res.data.message || '添加实体失败');
    }
  } catch (error) {
    console.error('添加实体失败:', error);
    message.error('添加实体失败');
  }
};

// 处理新增关系
const handleAddRelation = async () => {
  if (!newRelation.value.customFields[0].value || !newRelation.value.source || !newRelation.value.target) {
    message.error('请填写必填项');
    return;
  }

  const nameRegex = /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5_]*$/;
  if (!nameRegex.test(newRelation.value.customFields[0].value)) {
    message.error('类型不能以数字开头，且不能包含. : -等特殊字符');
    return;
  }

  for (const field of newRelation.value.customFields.slice(1)) {
    if (field.key && !validateFieldName(field.key)) {
      message.error(`自定义字段"${field.key}"不能以数字开头，且不能包含. : -等特殊字符`);
      return;
    }
  }

  try {
    const currentPath = window.location.pathname;
    const relationId = currentPath.split('/relation/')[1];

    if (!relationId) {
      message.error('无法获取知识库ID');
      return;
    }

    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    if (!userInfo.userId || !userInfo.username) {
      message.error('用户信息获取失败');
      return;
    }

    const fields = {
      description: newRelation.value.description || ''
    };
    newRelation.value.customFields.slice(1).forEach(field => {
      if (field.key && field.value) {
        fields[field.key] = field.value;
      }
    });

    const relationData = {
      source_id: newRelation.value.source,
      target_id: newRelation.value.target,
      type: newRelation.value.customFields[0].value,
      fields: fields
    };

    const res = await http.post(`/knowledge_api/${relationId}/relation`, relationData, {
      params: {
        username: userInfo.username,
        user_id: userInfo.userId
      }
    });

    if (res.data.code === 200) {
      const relation = {
        source: newRelation.value.source,
        target: newRelation.value.target,
        relationType: newRelation.value.customFields[0].value,
        description: newRelation.value.description
      };

      graphData.value.links.push(relation);

      if (chart.value) {
        chart.value.setOption({
          series: [{
            links: graphData.value.links
          }]
        });
      }

      addRelationModalVisible.value = false;

      newRelation.value = {
        source: '',
        target: '',
        description: '',
        customFields: [
          { key: 'TYPE', value: '' }
        ]
      };

      message.success('关系添加成功');

      emit('refreshMap');

      fetchMapData();
    } else {
      message.error(res.data.message || '添加关系失败');
    }
  } catch (error) {
    console.error('添加关系失败:', error);
    message.error('添加关系失败');
  }
};

// 处理保存
const handleSave = () => {
  if (!selectedItem.value) return;

  if (selectedItem.value.type === 'node') {
    const nodeIndex = graphData.value.nodes.findIndex(node => node.id === selectedItem.value.id);
    if (nodeIndex !== -1) {
      graphData.value.nodes[nodeIndex] = { ...selectedItem.value };
    }
  } else {
    const edgeIndex = graphData.value.links.findIndex(link =>
      link.source === selectedItem.value.source && link.target === selectedItem.value.target
    );
    if (edgeIndex !== -1) {
      graphData.value.links[edgeIndex] = { ...selectedItem.value };
    }
  }

  chart.value.setOption({
    series: [{
      data: graphData.value.nodes,
      links: graphData.value.links
    }]
  });

  selectedItem.value = null;

  emit('refreshMap');

  fetchMapData();
};

// 监听 selectedItem 变化
watch(selectedItem, (newVal) => {
  if (!newVal) {
    const { resetNodeOpacity } = useOpacityManager(chart);
    resetNodeOpacity();
  }
});

// 处理取消
const handleCancel = () => {
  selectedItem.value = null;
  const { resetNodeOpacity } = useOpacityManager(chart);
  resetNodeOpacity();
};

// 获取实体名称
const getEntityName = (entityId) => {
  const entity = graphData.value.nodes.find(node => node.id === entityId);
  return entity ? entity.name : '';
};

// 刷新图谱数据
const refreshGraphData = () => {
  isLoading.value = true;

  selectedItem.value = null;

  graphData.value = {
    nodes: [],
    links: [],
    categories: []
  };

  stats.value = {
    entityCount: 0,
    relationCount: 0,
    categoryCount: 0
  };

  nextTick(() => {
    disposeChart();

    // 延迟初始化，确保DOM完全更新
    setTimeout(() => {
      initGraphWithData();

      if (props.mapInfo) {
        updateGraphData(props.mapInfo);
      } else {
        isLoading.value = false;
      }
    }, 100);
  });
};

// 直接从接口获取最新地图数据
const fetchMapData = async () => {
  try {
    isLoading.value = true;

    const currentPath = window.location.pathname;
    const relationId = currentPath.split('/relation/')[1];

    if (!relationId) {
      message.error('无法获取知识库ID');
      isLoading.value = false;
      return;
    }

    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    if (!userInfo.userId || !userInfo.username) {
      message.error('用户信息获取失败');
      isLoading.value = false;
      return;
    }

    const res = await http.get(`/knowledge_api/${relationId}/map`, {
      params: {
        username: userInfo.username,
        user_id: userInfo.userId
      }
    });

    if (res.data.code === 200) {
      console.log('直接获取到的地图信息:', res.data);
      updateGraphData(res.data);

      emit('refreshMap');
    } else {
      message.error(res.data.message || '获取地图信息失败');
      isLoading.value = false;
    }
  } catch (error) {
    console.error('获取地图信息失败:', error);
    message.error('获取地图信息失败');
    isLoading.value = false;
  }
};

// 暴露方法给父组件
defineExpose({
  refreshGraphData,
  fetchMapData
});

// 组件挂载时初始化
onMounted(() => {
  initGraphWithData();
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  disposeChart();
  window.removeEventListener('resize', handleResize);
});

// 处理遮罩层点击
const handleMaskClick = () => {
  selectedItem.value = null;
  const { resetNodeOpacity } = useOpacityManager(chart);
  resetNodeOpacity();
};

// 处理删除
const handleDelete = async () => {
  if (!selectedItem.value) return;

  if (selectedItem.value.type === 'node') {
    try {
      const currentPath = window.location.pathname;
      const relationId = currentPath.split('/relation/')[1];

      if (!relationId) {
        message.error('无法获取知识库ID');
        return;
      }

      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      if (!userInfo.userId || !userInfo.username) {
        message.error('用户信息获取失败');
        return;
      }

      const nodeData = props.mapInfo?.data?.nodes?.find(node => node.id === selectedItem.value.id);
      const version = nodeData?.fields?.version || '';

      const res = await http.delete(`/knowledge_api/${relationId}/entity`, {
        data: {
          version: version,
          entity_id: selectedItem.value.id
        }
      });

      if (res.data.code === 200) {
        const nodeIndex = graphData.value.nodes.findIndex(node => node.id === selectedItem.value.id);
        if (nodeIndex !== -1) {
          graphData.value.nodes.splice(nodeIndex, 1);
        }

        graphData.value.links = graphData.value.links.filter(link =>
          link.source !== selectedItem.value.id && link.target !== selectedItem.value.id
        );

        chart.value.setOption({
          series: [{
            data: graphData.value.nodes,
            links: graphData.value.links
          }]
        });

        selectedItem.value = null;
        message.success('实体已删除');

        emit('refreshMap');

        fetchMapData();
      } else {
        message.error(res.data.message || '删除实体失败');
      }
    } catch (error) {
      console.error('删除实体失败:', error);
      message.error('删除实体失败');
    }
  } else if (selectedItem.value.type === 'edge') {
    try {
      const currentPath = window.location.pathname;
      const relationUid = currentPath.split('/relation/')[1];
      const relationId = selectedItem.value.id;

      const res = await http.delete(`/knowledge_api/${relationUid}/delete_relation/${relationId}`);
      if (res.data.code === 200) {
        let index = graphData.value.links.findIndex(link => link.id === relationId);
        if (index === -1) {
          index = graphData.value.links.findIndex(link =>
            link.source === selectedItem.value.source && link.target === selectedItem.value.target
          );
        }
        if (index !== -1) {
          graphData.value.links.splice(index, 1);
        }
        chart.value.setOption({
          series: [{ links: graphData.value.links }]
        });
        selectedItem.value = null;
        message.success('关系已删除');
        emit('refreshMap');
        fetchMapData();
      } else {
        message.error(res.data.message || '删除关系失败');
      }
    } catch (error) {
      console.error('删除关系失败:', error);
      message.error('删除关系失败');
    }
  }
};

// 获取实体的所有关系
const getEntityRelations = (entityId) => {
  return graphData.value.links.filter(link => link.source === entityId);
};

// 获取实体的上游关系
const getUpstreamRelations = (entityId) => {
  const relations = graphData.value.links.filter(link => link.target === entityId);
  return relations.map(relation => {
    const fullRelationData = props.mapInfo?.data?.links?.find(l =>
      l.source === relation.source && l.target === relation.target
    );
    return {
      ...relation,
      fields: fullRelationData?.fields || {}
    };
  });
};

// 获取实体的下游关系
const getDownstreamRelations = (entityId) => {
  const relations = graphData.value.links.filter(link => link.source === entityId);
  return relations.map(relation => {
    const fullRelationData = props.mapInfo?.data?.links?.find(l =>
      l.source === relation.source && l.target === relation.target
    );
    return {
      ...relation,
      fields: fullRelationData?.fields || {}
    };
  });
};

const filterOption = (input, option) => {
  const label = (option.label || (option.children && option.children[0]) || '').toString().toLowerCase();
  const value = (option.value || '').toString().toLowerCase();
  return label.includes(input.toLowerCase()) || value.includes(input.toLowerCase());
};

// 高亮方法：根据highlightText高亮实体或关系
const handleHighlight = () => {
  const text = highlightText.value.trim().toLowerCase();
  if (!text) {
    const { resetNodeOpacity } = useOpacityManager(chart);
    resetNodeOpacity();
    return;
  }

  const option = chart.value.getOption();
  const nodes = option.series[0].data;
  const links = option.series[0].links;
  const matchedNodeIds = new Set();

  nodes.forEach(node => {
    if ((node.name && node.name.toLowerCase().includes(text)) || (node.category && node.category.toLowerCase().includes(text))) {
      matchedNodeIds.add(node.id);
    }
  });

  const updatedNodes = nodes.map(node => ({
    ...node,
    itemStyle: {
      ...node.itemStyle,
      opacity: matchedNodeIds.size === 0 ? 1 : (matchedNodeIds.has(node.id) ? 1 : 0.3)
    }
  }));

  const updatedLinks = links.map(link => ({
    ...link,
    lineStyle: {
      ...link.lineStyle,
      opacity: (matchedNodeIds.has(link.source) || matchedNodeIds.has(link.target)) ? 1 : 0.3
    }
  }));

  chart.value.setOption({
    series: [{
      data: updatedNodes,
      links: updatedLinks
    }]
  });
};

const addRelationCustomField = () => {
  newRelation.value.customFields.push({ key: '', value: '' });
};

const removeRelationCustomField = (index) => {
  if (index > 0) {
    newRelation.value.customFields.splice(index, 1);
  }
};

// 获取关系的所有字段（排除基础字段）
const getRelationAllFields = (relation) => {
  if (!relation) return {};
  const displayFields = {};
  
  // 排除的字段
  const exclude = ['id', 'source', 'target', 'type','fields', 'lineStyle', 'itemStyle'];

  
  // 需要单独展示的字段
  const separateFields = ['relationType', 'version'];
  
  const combined = { ...relation, ...(relation.fields || {}) };

  // 先处理需要单独展示的字段
  for (const key of separateFields) {
    if (Object.prototype.hasOwnProperty.call(combined, key) && combined[key] !== undefined && combined[key] !== '') {
      displayFields[key] = combined[key];
    }
  }

  // 处理其他字段，合并到一个字段中
  const otherFields = {};
  for (const key in combined) {
    if (Object.prototype.hasOwnProperty.call(combined, key) && 
        !exclude.includes(key) && 
        !separateFields.includes(key) &&
        combined[key] !== undefined && 
        combined[key] !== '') {
      otherFields[key] = combined[key];
    }
  }

  // 如果有其他字段，将它们合并到一个字段中
  if (Object.keys(otherFields).length > 0) {
    const otherFieldsStr = Object.entries(otherFields)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ');
    displayFields['match_fields'] = otherFieldsStr;
  }

  return displayFields;
};
</script>

<style scoped>
.relation-map-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
}

/* 左侧面板样式 */
.left-panel {
  width: 320px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 100;
}

.panel-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 16px;
  transition: all 0.3s ease;
}

.action-section {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  margin-top: 16px;
}

.action-section .ant-btn {
  flex: 1;
}

.search-section {
  width: 100%;
}

.stats-grid {
  display: flex;
  justify-content: space-between;
  gap: 8px;
}

.stat-item {
  flex: 1;
  padding: 12px;
  border-radius: 4px;
  text-align: center;
  transition: all 0.3s;
}

.stat-item:hover {
  background-color: #f0f0f0;
}

.stat-value {
  font-size: 20px;
  font-weight: 500;
  color: #1890ff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
}

/* 图谱容器样式 */
.graph-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  width: 108%;
}

.graph-content {
  width: 100%;
  height: 100%;
}

/* 地图中间底部的搜索框样式 */
.map-search-box {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  padding: 10px 15px;
}

/* 右侧面板样式 */
.right-panel {
  position: absolute;
  top: 16px;
  right: -300px;
  width: 340px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  z-index: 100;
  transform: translateX(0);
  opacity: 0;
}

.right-panel.show {
  right: 16px;
  transform: translateX(0);
  opacity: 1;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.close-btn {
  padding: 0;
  height: 24px;
  width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.panel-content {
  padding: 16px;
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  font-size: 14px;
  margin-bottom: 8px;
  color: #262626;
}

.entity-info {
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 24px;
}

/* 遮罩层样式 */
.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.02);
  z-index: 99;
}

/* 详情卡片样式 */
.detail-card {
  margin-top: 0;
  max-height: 40vh;
  overflow-y: auto;
}

.detail-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 8px;
}

.detail-label {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
  min-width: 80px;
  flex-shrink: 0;
  text-align: left;
}

.detail-value {
  font-size: 14px;
  color: #262626;
  word-break: break-all;
  flex: 1;
  text-align: left;
}

/* 隐藏原生滚动条 */
.detail-card::-webkit-scrollbar {
  width: 6px;
}

.detail-card::-webkit-scrollbar-track {
  background: transparent;
}

.detail-card::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.detail-card:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
}

.relation-preview {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px;
}

.relation-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.entity-box {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.entity-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  height: 28px;
  background-color: #1890ff;
  border-radius: 4px;
  max-width: 120px;
}

.entity-icon {
  font-size: 14px;
  color: #fff;
}

.entity-name {
  font-size: 13px;
  color: #fff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

.relation-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  padding: 0 8px;
  flex: 0 0 auto;
}

.relation-arrow.upstream {
  color: #722ed1;
}

.relation-arrow.downstream {
  color: #1890ff;
}

.relation-edit {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
  text-align: left;
}

.relation-input {
  width: 100%;
}

.relation-input :deep(.ant-input) {
  text-align: left;
  font-weight: 500;
  color: #1890ff;
}

.relation-description {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.description-input :deep(.ant-input) {
  font-size: 13px;
  color: #595959;
}

.delete-section {
  background-color: #fff;
  border-radius: 4px;
}

.entity-preview {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px;
}

.entity-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 12px;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.entity-relations {
  display: flex;
  flex-direction: column;
}

.relation-list {
  display: flex;
  flex-direction: column;
}

.relation-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px;
  border-radius: 4px;
  margin-bottom: 4px;
}

.relation-label {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
  min-width: 60px;
  text-align: left;
}

.relation-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  padding: 0 8px;
}

.entity-select {
  min-width: 100%;
  margin-top: 20px;
}

.relation-type-input {
  flex: 1;
  margin-top: 20px;
}

.relation-type {
  font-size: 13px;
  font-weight: 500;
  min-width: 60px;
  text-align: left;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #f0f0f0;
}

.relation-type.upstream {
  color: #722ed1;
  background-color: #f9f0ff;
}

.relation-type.downstream {
  color: #1890ff;
  background-color: #e6f7ff;
}

.related-entity {
  display: flex;
  justify-content: flex-start;
}

.relation-group {
  margin-bottom: 8px;
}

/* 关系详情样式 */
.relation-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 120px;
  max-width: 200px;
}

.relation-fields {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 11px;
  color: #666;
  background-color: #f8f9fa;
  padding: 4px 6px;
  border-radius: 3px;
  border-left: 2px solid #d9d9d9;
}

.relation-field {
  display: flex;
  align-items: center;
  gap: 4px;
  line-height: 1.2;
}

.field-key {
  font-weight: 500;
  color: #333;
  min-width: 40px;
}

.field-value {
  color: #666;
  word-break: break-word;
  flex: 1;
}

/* 添加加载状态样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1000;
}

.loading-text {
  margin-top: 16px;
  font-size: 14px;
  color: #595959;
}

.custom-fields {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 12px;
}

.fields-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.fields-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.fields-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.field-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-separator {
  color: #8c8c8c;
  padding: 0 4px;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-item-name {
  flex: 1;
}

.form-item-type {
  flex: 1;
}

.relation-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.relation-main {
  width: 100%;
  margin-top: 2%;
  padding: 0 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.relation-arrow-col {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  font-size: 16px;
  flex: 0 0 24px;
}

.entity-select {
  width: 100%;
  flex: 1;
}

.relation-type-input {
  width: 100%;
  flex: 1;
}

/* 组合式搜索输入框容器 */
.cypher-query-builder {
  display: flex;
  align-items: center;
  width: 100%;
  min-width: 800px;
  background-color: hsla(210, 100%, 99%, 0.95);
  /* 极浅蓝色，提供清洁和高级感 */
  padding: 14px 25px;
  border-radius: 10px;
  border: 1px solid rgba(24, 144, 255, 0.08);
  /* 极细微的蓝色边框 */
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.07);
  /* 柔和且宽广的浮动阴影 */
  transition: all 0.3s ease;
}

.cypher-query-builder:hover {
  box-shadow: 0 18px 60px rgba(0, 0, 0, 0.1);
  /* 悬停时增强阴影 */
}

.query-keyword {
  font-weight: bold;
  color: #1890ff;
  margin: 0 5px;
  white-space: nowrap;
}

/* 可以给 input 和 input-number 添加一些 Ant Design 的样式 */
.cypher-query-builder :deep(.ant-input),
.cypher-query-builder :deep(.ant-input-number) {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 4px 11px;
  height: 32px;
  /* 统一高度 */
}

.cypher-query-builder :deep(.ant-input-number-handler-wrap) {
  height: 100%;
  /* 确保上下箭头按钮高度也一致 */
}

.map-search-box {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
}
</style>
