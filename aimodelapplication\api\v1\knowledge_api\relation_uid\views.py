import asyncio
import time
from typing import List

from fastapi import APIRouter, Path, Body, Query, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
from db.mysql.models.api_relations import ApiRelationsModel
from db.mysql.models.map_task_api import MapTaskApiModel
from db.neo.models.neo4j_map import Neo4jModel
from db.mysql.models.map_info import MapInfoModel
from service.map.TokenPoolManager import TokenPoolManager
from template.response_temp import BaseResponse,BaseResponseWithModel
from logger import logging
import uuid
import os
import json
import requests
from db.mysql.models import knowledge_base
from db.mysql.models.knowledge_base import KnowledgeBaseModel
from db.mysql.models.map_info_history import MapInfoHistoryModel
from db.mysql.models.recall_test_history import RecallTestHistoryModel
from datetime import datetime
from db.mysql.models.map_doc_api import MapDocApiModel
from db.mysql.models.map_doc_component import MapDocComponentsModel
from service.map.paths_extractor import ExtractSwaggerPathEndpoints
from service.map.components_extractor import ExtractSwaggerComponents
from service.map import json_utils
from db.mysql.models.prompt_management import PromptManagementModel
from service.map import generate_task_id
from service.map.relation_extractor import ApiComponentRelationExtractor
from .schemas import PromptManagementRequest, PromptManagementResponse

router = APIRouter()
logger = logging()

# 阿里云百炼API配置
DASHSCOPE_API_KEY = "sk-fdfd70db7f9b48b4b8cad01c56c3fe99"
DASHSCOPE_API_URL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"

class DashScopeService:
    """阿里云百炼API服务"""

    @staticmethod
    def call_llm(prompt: str, model: str = "qwen-max-latest") -> str:
        """调用阿里云百炼大语言模型"""
        headers = {
            "Authorization": f"Bearer {DASHSCOPE_API_KEY}",
            "Content-Type": "application/json"
        }

        data = {
            "model": model,
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            },
            "parameters": {
                "temperature": 0.1,        # 温度参数，控制输出的随机性
                "repetition_penalty": 1.0  # 重复惩罚参数
            }
        }

        try:
            response = requests.post(DASHSCOPE_API_URL, headers=headers, json=data, timeout=30)
            response.raise_for_status()

            result = response.json()

            # 处理不同的API响应格式
            if result.get("output"):
                output = result["output"]

                # 新格式：直接返回text字段
                if "text" in output:
                    return output["text"]

                # 旧格式：choices数组格式
                elif "choices" in output and output["choices"]:
                    return output["choices"][0]["message"]["content"]

                else:
                    raise Exception(f"API返回格式异常: {result}")
            else:
                raise Exception(f"API返回格式异常: {result}")

        except requests.exceptions.RequestException as e:
            logger.error(f"调用阿里云百炼API失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"调用大语言模型失败: {str(e)}")
        except Exception as e:
            logger.error(f"处理API响应失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"处理模型响应失败: {str(e)}")

@router.post("/extract_relations", summary="关系提取")
async def extract_relations(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="请求数据，包含选中的API ID列表")
):
    """
    关系提取接口：
    1. 接收前端选中的API ID列表
    2. 从数据库中根据知识库id获取到提示词信息
    3. 根据选中的API ID获取具体的API信息
    4. 将用户填写的提示词与本地写好的提示词进行组装
    5. 调用大模型分析选中API的前置依赖
    6. 在Neo4j中查找所有API节点，找出能提供前置依赖的相关接口
    7. 返回接口的详细信息
    """
    prompt_model = None
    neo4j_model = None
    map_doc_api_model = None

    try:
        # 0. 验证请求参数
        selected_api_ids = data.get("entity_list", [])
        if not selected_api_ids:
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="请选择要分析的API接口",
                    data={}
                ).dict()
            )

        logger.info(f"开始分析选中的API: {selected_api_ids}")
        # 1. 从数据库中根据知识库id获取提示词信息
        prompt_model = PromptManagementModel()
        await prompt_model.initialize()

        logger.info(f"开始获取知识库 {relation_uid} 的提示词配置")
        prompt_config = await prompt_model.find_by_knowledge_base_id(relation_uid)

        if not prompt_config:
            logger.warning(f"未找到知识库 {relation_uid} 的提示词配置")
            return JSONResponse(
                status_code=404,
                content=BaseResponse(
                    code=404,
                    message="未找到该知识库的提示词配置，请先配置提示词",
                    data={}
                ).dict()
            )

        logger.info(f"成功获取提示词配置: {prompt_config}")

        # 2. 根据选中的API ID获取具体的API信息
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()

        selected_apis = []
        for api_id in selected_api_ids:
            # 从Neo4j中根据ID查找API记录
            api_info = await neo4j_model.get_api_record_by_id(api_id)
            if api_info:
                # 验证API是否属于当前知识库(通过标签或属性验证)
                if api_info.get("id") == api_id:  # Neo4j节点ID匹配检查
                    selected_apis.append(api_info)
                else:
                    logger.warning(f"API {api_id} 不属于知识库 {relation_uid}")
            else:
                logger.warning(f"未找到API记录: {api_id}")

        if not selected_apis:
            return JSONResponse(
                status_code=404,
                content=BaseResponse(
                    code=404,
                    message="未找到选中的API信息，请检查选择的API是否有效",
                    data={}
                ).dict()
            )

        logger.info(f"成功获取 {len(selected_apis)} 个选中的API信息")

        # 3. 从Neo4j数据库中获取所有类型为API的节点（用于查找前置依赖接口）
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()

        logger.info(f"开始查询知识库 {relation_uid} 中所有API类型的节点")
        all_api_nodes = await neo4j_model.find_nodes_by_type("API")

        if not all_api_nodes:
            logger.warning(f"知识库 {relation_uid} 中未找到API类型的节点")
            return JSONResponse(
                status_code=404,
                content=BaseResponse(
                    code=404,
                    message="该知识库中未找到API接口信息，请先导入接口文档",
                    data={}
                ).dict()
            )

        logger.info(f"成功获取 {len(all_api_nodes)} 个API节点用于查找前置依赖")

        # 4. 构建选中API的接口文档内容（用于分析前置依赖）
        selected_api_document = build_selected_api_document(selected_apis)
        logger.info(f"构建的选中API文档长度: {len(selected_api_document)} 字符")

        # 5. 构建完整的提示词并调用大模型分析选中API的前置依赖
        complete_prompt = build_complete_prompt_for_relation_extraction(prompt_config, selected_api_document)
        logger.info("开始调用大语言模型分析选中API的前置依赖")

        # 调用大语言模型
        llm_result = DashScopeService.call_llm(complete_prompt)
        logger.info(f"大模型返回结果: {llm_result}")

        # 6. 根据前置依赖分析结果，在所有API节点中查找相关接口
        related_apis = find_related_apis(llm_result, all_api_nodes)
        logger.info(f"找到 {len(related_apis)} 个相关接口")

        # 7. 提取选中API与找到的前置API之间的关系
        api_relations = []
        if related_apis:
            logger.info("开始提取API之间的关系")
            api_relations = extract_api_relations(selected_apis, related_apis)
            logger.info(f"提取到 {len(api_relations)} 个API关系")

        return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message="关系提取成功",
                data={
                    "prerequisite_analysis": llm_result.strip(),
                    "related_apis": related_apis,
                    "total_apis_found": len(related_apis),
                    "api_relations": api_relations,
                    "total_relations": len(api_relations)
                }
            ).dict()
        )

    except Exception as e:
        logger.error(f"关系提取失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"关系提取失败: {str(e)}",
                data={}
            ).dict()
        )
    finally:
        # 确保关闭数据库连接
        if prompt_model:
            await prompt_model.close()
        if neo4j_model:
            await neo4j_model.close()
        if map_doc_api_model:
            await map_doc_api_model.close()



def build_selected_api_document(selected_apis: List[dict]) -> str:
    """
    构建选中API的接口文档内容（用于分析前置依赖）

    Args:
        selected_apis: 选中的API信息列表（来自map_doc_api表）

    Returns:
        str: 格式化的选中API接口文档内容
    """
    document_parts = []

    for i, api in enumerate(selected_apis, 1):
        # 基本信息
        method = api.get("method", "").upper()
        path = api.get("path", "")
        summary = api.get("summary", "")
        entity_name = api.get("entity_name", "")

        # 构建接口描述
        api_desc = f"## 选中接口 {i}: {method} {path}\n"
        if entity_name:
            api_desc += f"**接口名称**: {entity_name}\n"
        if summary:
            api_desc += f"**描述**: {summary}\n"

        # 添加参数信息
        parameters = api.get("parameters")
        if parameters:
            try:
                # 如果parameters是字符串，尝试解析为JSON
                if isinstance(parameters, str):
                    parameters = json.loads(parameters)

                if parameters and isinstance(parameters, list):
                    api_desc += "**参数**:\n"
                    for param in parameters:
                        param_name = param.get("name", "")
                        param_type = param.get("type", "")
                        param_desc = param.get("description", "")
                        if param_name:
                            api_desc += f"- {param_name} ({param_type}): {param_desc}\n"
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"解析参数失败: {str(e)}")

        # 添加请求体信息
        request_body = api.get("request_body")
        if request_body:
            try:
                # 如果request_body是字符串，尝试解析为JSON
                if isinstance(request_body, str):
                    request_body = json.loads(request_body)

                if request_body and isinstance(request_body, dict):
                    api_desc += "**请求体**: 存在\n"
                    # 尝试提取请求体示例
                    content = request_body.get("content", {})
                    if "application/json" in content:
                        example = content["application/json"].get("example")
                        if example:
                            if isinstance(example, str):
                                try:
                                    example_dict = json.loads(example)
                                    keys = list(example_dict.keys())
                                    if keys:
                                        api_desc += f"请求体字段: {', '.join(keys)}\n"
                                except json.JSONDecodeError:
                                    pass
                            elif isinstance(example, dict):
                                keys = list(example.keys())
                                if keys:
                                    api_desc += f"请求体字段: {', '.join(keys)}\n"
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"解析请求体失败: {str(e)}")

        # 添加响应信息
        responses = api.get("responses")
        if responses:
            try:
                if isinstance(responses, str):
                    responses = json.loads(responses)

                if responses and isinstance(responses, dict):
                    api_desc += "**响应**: 存在\n"
                    # 尝试提取响应示例
                    for status_code, response_info in responses.items():
                        if isinstance(response_info, dict):
                            content = response_info.get("content", {})
                            if "application/json" in content:
                                example = content["application/json"].get("example")
                                if example:
                                    if isinstance(example, str):
                                        try:
                                            example_dict = json.loads(example)
                                            keys = list(example_dict.keys())
                                            if keys:
                                                api_desc += f"响应字段({status_code}): {', '.join(keys)}\n"
                                        except json.JSONDecodeError:
                                            pass
                                    elif isinstance(example, dict):
                                        keys = list(example.keys())
                                        if keys:
                                            api_desc += f"响应字段({status_code}): {', '.join(keys)}\n"
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"解析响应失败: {str(e)}")

        api_desc += "\n"
        document_parts.append(api_desc)

    return "\n".join(document_parts)

def build_complete_prompt_for_relation_extraction(prompt_config: dict, api_document: str) -> str:
    """
    构建用于关系提取的完整提示词

    Args:
        prompt_config: 提示词配置
        api_document: 接口文档内容

    Returns:
        str: 完整的提示词
    """
    # 获取用户配置的提示词信息
    business_introduction = prompt_config.get('business_introduction', '')
    term_definitions = prompt_config.get('term_definitions', [])
    business_prompts = prompt_config.get('business_prompts', [])
    extra_evidence = prompt_config.get('extra_evidence', [])

    # 构建名词定义部分
    term_definitions_text = ""
    for term in term_definitions:
        if isinstance(term, dict) and term.get('name') and term.get('definition'):
            term_definitions_text += f"## {term['name']}：{term['definition']}\n"

    # 构建额外提示部分
    business_prompts_text = ""
    for prompt in business_prompts:
        if isinstance(prompt, dict) and prompt.get('content'):
            business_prompts_text += f"## {prompt['content']}\n"

    # 构建额外依据部分
    extra_evidence_text = ""
    for evidence in extra_evidence:
        if isinstance(evidence, dict) and evidence.get('name') and evidence.get('description'):
            extra_evidence_text += f"## {evidence['name']}：{evidence['description']}\n"

    # 完整提示词模板
    complete_prompt = f"""# 任务描述
根据接口传参判断，在执行接口前需要进行的前置操作

你是一个接口测试专家，擅长根据接口文档判断接口执行顺序。

# 业务介绍
{business_introduction}

# 名词定义
{term_definitions_text}

# 额外提示
{business_prompts_text}

# 必须满足的判断依据
## 业务场景：参考业务介绍判断出执行当前接口时必须要进行的操作，例如：更新授权规则前必须要条件为创建授权规则
## 接口类型：接口类型为创建类型接口
## 非枚举值：字段类型为非枚举值类型
## 路径嵌入：在url中明文声明的变量所必须依赖的前置操作，例如：/api/{{projectId}} 必须依赖创建项目

# 补充依据
## 字段映射：$ref引用的实体对象标识字段（如assetId→资产ID）
## 接口类型：接口类型为创建类型接口
{extra_evidence_text}

# 任务要求(必须满足)
- 只考虑一级参数，不考虑嵌套参数。例如：只考虑类似account的一级参数,排除account.sshKeyI等二级参数
- 每个前置接口均需要文档中的参数作为依据
- 不要放过任何一个参数，保证每个参数的前置操作均需要识别
- 额外提示一定要满足

# 接口文档
{api_document}

# 输出要求
## 仅输出前置接口以及举证参数
## 不要输出原因和描述，只需要给出结果

## 参考输出结果如下：
创建项目（projectId）、创建用户（userName）、创建资产(assetsId)
"""

    return complete_prompt

def find_related_apis(llm_result: str, api_nodes: List[dict]) -> List[dict]:
    """
    使用大语言模型根据前置依赖分析结果，精准提取相关的API接口

    Args:
        llm_result: 大模型返回的前置依赖分析结果（接口描述）
        api_nodes: 所有API节点列表

    Returns:
        List[dict]: 相关的API接口详细信息列表
    """
    if not api_nodes:
        return []

    try:
        # 构建接口文档，格式化为大模型可理解的格式
        api_document = build_api_document_for_extraction(api_nodes)

        # 构建精准API提取的提示词
        extraction_prompt = build_api_extraction_prompt(llm_result, api_document)

        # 调用大语言模型进行精准API提取
        logger.info("开始使用大语言模型进行精准API提取")
        extraction_result = DashScopeService.call_llm(extraction_prompt)
        logger.info(f"API提取结果: {extraction_result}")

        # 解析大模型返回的提取结果
        related_apis = parse_api_extraction_result(extraction_result, api_nodes)

        return related_apis

    except Exception as e:
        logger.error(f"使用大模型提取API失败: {str(e)}")
        # 如果大模型提取失败，回退到传统的关键词匹配方法
        logger.info("回退到传统关键词匹配方法")
        return find_related_apis_fallback(llm_result, api_nodes)

def build_api_document_for_extraction(api_nodes: List[dict]) -> str:
    """
    构建用于API提取的接口文档格式

    Args:
        api_nodes: API节点列表

    Returns:
        str: 格式化的接口文档
    """
    document_parts = []

    for node in api_nodes:
        method = node.get("method", "").upper()
        path = node.get("path", "")
        summary = node.get("summary", "")

        # 构建接口信息，格式与提示词要求一致
        api_info = {
            "method": method,
            "path": path,
            "summary": summary
        }

        document_parts.append(json.dumps(api_info, ensure_ascii=False))

    return "\n".join(document_parts)

def build_api_extraction_prompt(api_description: str, api_document: str) -> str:
    """
    构建精准API提取的提示词

    Args:
        api_description: 接口描述（前置依赖分析结果）
        api_document: 接口文档

    Returns:
        str: 完整的API提取提示词
    """
    prompt = f"""你是一个精准的API提取器。请精准的从接口文档中提取描述的接口。

【输入数据】
- 接口描述(不同描述会使用标点符号隔开)：
{api_description}
- 接口文档：
{api_document}

【筛选逻辑】
- 解析接口描述，提取所有明确提到的接口名称关键词
- 在接口文档中summary字段语义精确匹配

【特殊情况处理】
- 当描述中的关键词在文档中无匹配时，返回空数组
- 多个描述关键词匹配同一接口时只记录一次
- 若描述中未提及任何具体接口，返回{{"api":[],"total":0}}

【任务准则】
- 严格排除未在接口描述中出现的接口
- 严禁出现测试接口以及非接口文档中的接口
- 输出的所有数据均完全来自于接口文档，禁止修改
- 仅按输出格式输出，禁止输出不必要的说明

【输出格式】
- 仅输出json格式的原生接口信息
参考输出如下：
{{
"api": [{{"proof":"接口描述举证","url": "接口1的path", "method": "接口1的method", "summary":"接口1的summary"}}],
"total": 接口数量
}}"""

    return prompt

def parse_api_extraction_result(extraction_result: str, api_nodes: List[dict]) -> List[dict]:
    """
    解析大模型返回的API提取结果

    Args:
        extraction_result: 大模型返回的提取结果
        api_nodes: 原始API节点列表

    Returns:
        List[dict]: 解析后的相关API列表
    """
    related_apis = []

    try:
        # 尝试从返回结果中提取JSON
        import re
        json_match = re.search(r'\{.*\}', extraction_result, re.DOTALL)
        if json_match:
            json_str = json_match.group()
            result_data = json.loads(json_str)

            extracted_apis = result_data.get("api", [])
            total_count = result_data.get("total", 0)

            logger.info(f"大模型提取到 {total_count} 个API")

            # 根据提取结果在原始节点中查找匹配的API
            for extracted_api in extracted_apis:
                extracted_method = extracted_api.get("method", "").upper()
                extracted_path = extracted_api.get("url", "")  # 注意这里是url字段
                extracted_summary = extracted_api.get("summary", "")
                proof = extracted_api.get("proof", "")

                # 在原始API节点中查找匹配的接口
                for node in api_nodes:
                    node_method = node.get("method", "").upper()
                    node_path = node.get("path", "")
                    node_summary = node.get("summary", "")

                    # 精确匹配method和path
                    if (node_method == extracted_method and
                        node_path == extracted_path and
                        node_summary == extracted_summary):

                        api_info = {
                            "id": node.get("id"),
                            "name": node.get("name", ""),
                            "method": node_method,
                            "path": node_path,
                            "summary": node_summary,
                            "proof": proof,  # 添加举证信息
                            "match_reason": ["大模型精准提取"],
                            "match_score": 100,  # 大模型提取的都是高置信度
                            "parameters": node.get("parameters", []),
                            "request_body": node.get("request_body", {}),
                            "responses": node.get("responses", {}),
                            "tags": node.get("tags", [])
                        }

                        related_apis.append(api_info)
                        break  # 找到匹配的就跳出内层循环

        else:
            logger.warning("无法从大模型结果中提取JSON格式数据")

    except (json.JSONDecodeError, KeyError, ValueError) as e:
        logger.error(f"解析API提取结果失败: {str(e)}")
        logger.error(f"原始结果: {extraction_result}")

    return related_apis

def extract_api_relations(selected_apis: List[dict], related_apis: List[dict]) -> List[dict]:
    """
    提取选中API与找到的前置API之间的关系

    Args:
        selected_apis: 用户选中的API列表
        related_apis: 找到的前置API列表

    Returns:
        List[dict]: API关系列表
    """
    if not selected_apis or not related_apis:
        return []

    try:
        # 构建API关系提取的提示词
        relation_prompt = build_api_relation_prompt(selected_apis, related_apis)

        # 调用大语言模型进行关系提取
        logger.info("开始使用大语言模型提取API关系")
        relation_result = DashScopeService.call_llm(relation_prompt)
        logger.info(f"API关系提取结果: {relation_result}")

        # 解析大模型返回的关系结果
        api_relations = parse_api_relation_result(relation_result)

        return api_relations

    except Exception as e:
        logger.error(f"提取API关系失败: {str(e)}")
        return []

def build_api_relation_prompt(selected_apis: List[dict], related_apis: List[dict]) -> str:
    """
    构建API关系提取的提示词

    Args:
        selected_apis: 用户选中的API列表
        related_apis: 找到的前置API列表

    Returns:
        str: 完整的API关系提取提示词
    """
    # 构建选中API的信息
    selected_api_info = []
    for api in selected_apis:
        method = api.get("method", "").upper()
        path = api.get("path", "")
        summary = api.get("summary", "")
        entity_name = api.get("entity_name", "")

        # 提取参数信息
        param_fields = []
        try:
            parameters = api.get("parameters")
            if parameters:
                if isinstance(parameters, str):
                    parameters = json.loads(parameters)
                if isinstance(parameters, list):
                    for param in parameters:
                        param_name = param.get("name", "")
                        if param_name:
                            param_fields.append(param_name)
        except (json.JSONDecodeError, TypeError):
            pass

        # 提取请求体字段
        body_fields = []
        try:
            request_body = api.get("request_body")
            if request_body:
                if isinstance(request_body, str):
                    request_body = json.loads(request_body)
                if isinstance(request_body, dict):
                    content = request_body.get("content", {})
                    if "application/json" in content:
                        example = content["application/json"].get("example")
                        if example:
                            if isinstance(example, str):
                                try:
                                    example_dict = json.loads(example)
                                    body_fields.extend(list(example_dict.keys()))
                                except json.JSONDecodeError:
                                    pass
                            elif isinstance(example, dict):
                                body_fields.extend(list(example.keys()))
        except (json.JSONDecodeError, TypeError):
            pass

        api_info = {
            "id": api.get("id"),
            "name": entity_name or f"{method} {path}",
            "method": method,
            "path": path,
            "summary": summary,
            "parameters": param_fields,
            "request_body_fields": body_fields
        }
        selected_api_info.append(api_info)

    # 构建前置API的信息
    related_api_info = []
    for api in related_apis:
        method = api.get("method", "").upper()
        path = api.get("path", "")
        summary = api.get("summary", "")
        name = api.get("name", "")

        # 提取参数信息
        param_fields = []
        try:
            parameters = api.get("parameters")
            if parameters:
                if isinstance(parameters, str):
                    parameters = json.loads(parameters)
                if isinstance(parameters, list):
                    for param in parameters:
                        param_name = param.get("name", "")
                        if param_name:
                            param_fields.append(param_name)
        except (json.JSONDecodeError, TypeError):
            pass

        # 提取响应字段
        response_fields = []
        try:
            responses = api.get("responses")
            if responses:
                if isinstance(responses, str):
                    responses = json.loads(responses)
                if isinstance(responses, dict):
                    for status_code, response_info in responses.items():
                        if isinstance(response_info, dict):
                            content = response_info.get("content", {})
                            if "application/json" in content:
                                example = content["application/json"].get("example")
                                if example:
                                    if isinstance(example, str):
                                        try:
                                            example_dict = json.loads(example)
                                            response_fields.extend(list(example_dict.keys()))
                                        except json.JSONDecodeError:
                                            pass
                                    elif isinstance(example, dict):
                                        response_fields.extend(list(example.keys()))
        except (json.JSONDecodeError, TypeError):
            pass

        api_info = {
            "id": api.get("id"),
            "name": name or f"{method} {path}",
            "method": method,
            "path": path,
            "summary": summary,
            "parameters": param_fields,
            "response_fields": response_fields
        }
        related_api_info.append(api_info)

    # 构建完整的关系提取提示词
    prompt = f"""你是一个专业的API关系分析专家，需要分析目标API与前置API之间的数据依赖关系。

【任务描述】
分析目标API执行时需要从前置API获取哪些数据，并建立字段级别的依赖关系映射。

【目标API信息】
{json.dumps(selected_api_info, ensure_ascii=False, indent=2)}

【前置API信息】
{json.dumps(related_api_info, ensure_ascii=False, indent=2)}

【分析规则】
1. **参数依赖分析**：目标API的参数字段是否需要从前置API的响应字段中获取
2. **请求体依赖分析**：目标API的请求体字段是否需要从前置API的响应字段中获取
3. **路径参数依赖**：目标API路径中的参数（如{{userId}}）是否需要从前置API获取
4. **业务逻辑依赖**：根据API的功能描述判断是否存在业务上的依赖关系

【关系类型定义】
- **字段映射**：前置API的响应字段 → 目标API的输入字段
- **ID传递**：前置API返回的ID字段 → 目标API的路径参数或请求字段
- **状态依赖**：目标API的执行依赖于前置API的执行结果

【输出要求】
- 只输出存在明确依赖关系的API对
- 每个关系必须指明具体的字段映射
- 关系描述要准确反映数据流向

【输出格式】
严格按照以下JSON格式输出：
{{
  "relations": [
    {{
      "id": "随机生成的UUID",
      "name": "关系名称（简短描述）",
      "relation": "前置API的响应字段名 : 目标API的输入字段名",
      "source_api_id": "前置API的ID",
      "target_api_id": "目标API的ID",
      "source_api_name": "前置API名称",
      "target_api_name": "目标API名称",
      "relation_type": "字段映射|ID传递|状态依赖",
      "description": "详细的关系描述"
    }}
  ],
  "total": 关系总数
}}

【注意事项】
- 如果没有发现明确的依赖关系，返回空的relations数组
- 每个关系必须基于实际的字段分析，不能凭空推测
- 关系描述要具体明确，避免模糊表述
- 只输出JSON格式，不要包含其他说明文字"""

    return prompt

def parse_api_relation_result(relation_result: str) -> List[dict]:
    """
    解析大模型返回的API关系结果

    Args:
        relation_result: 大模型返回的关系结果

    Returns:
        List[dict]: 解析后的API关系列表
    """
    api_relations = []

    try:
        # 尝试从返回结果中提取JSON
        import re
        json_match = re.search(r'\{.*\}', relation_result, re.DOTALL)
        if json_match:
            json_str = json_match.group()
            result_data = json.loads(json_str)

            relations = result_data.get("relations", [])
            total_count = result_data.get("total", 0)

            logger.info(f"大模型提取到 {total_count} 个API关系")

            for relation in relations:
                # 如果没有提供ID，生成一个随机ID
                relation_id = relation.get("id")
                if not relation_id:
                    import uuid
                    relation_id = str(uuid.uuid4())

                api_relation = {
                    "id": relation_id,
                    "name": relation.get("name", ""),
                    "relation": relation.get("relation", ""),
                    "source_api_id": relation.get("source_api_id", ""),
                    "target_api_id": relation.get("target_api_id", ""),
                    "source_api_name": relation.get("source_api_name", ""),
                    "target_api_name": relation.get("target_api_name", ""),
                    "relation_type": relation.get("relation_type", ""),
                    "description": relation.get("description", "")
                }

                api_relations.append(api_relation)

        else:
            logger.warning("无法从大模型结果中提取JSON格式数据")

    except (json.JSONDecodeError, KeyError, ValueError) as e:
        logger.error(f"解析API关系结果失败: {str(e)}")
        logger.error(f"原始结果: {relation_result}")

    return api_relations

def build_api_matching_prompt(prerequisite_analysis: str, api_descriptions: List[str]) -> str:
    """
    构建用于API匹配的提示词

    Args:
        prerequisite_analysis: 前置依赖分析结果
        api_descriptions: API接口描述列表

    Returns:
        str: 用于API匹配的提示词
    """
    api_list = "\n".join(api_descriptions)

    prompt = f"""# 任务描述
你是一个接口依赖分析专家，需要根据前置依赖分析结果，从给定的API接口列表中找出相关的接口。

# 前置依赖分析结果
{prerequisite_analysis}

# 可用的API接口列表
{api_list}

# 分析要求
1. 仔细分析前置依赖分析结果中提到的操作和实体
2. 根据语义理解，找出能够实现这些前置操作的API接口
3. 考虑以下匹配规则：
   - 操作类型匹配（如"创建"对应POST方法）
   - 实体名称匹配（如"项目"对应包含project的接口）
   - 参数依赖匹配（如提到projectId，则需要能创建project的接口）
   - 业务逻辑匹配（如分配资产前需要先创建项目）

# 输出格式
请按照以下JSON格式输出匹配结果：
{{
  "matched_apis": [
    {{
      "api_index": 1,
      "match_score": 95,
      "match_reasons": ["创建类接口", "实体名称匹配: 项目", "业务逻辑相关"]
    }},
    {{
      "api_index": 3,
      "match_score": 80,
      "match_reasons": ["参数依赖: projectId", "业务逻辑相关"]
    }}
  ]
}}

# 注意事项
- api_index从1开始，对应API接口列表的序号
- match_score为匹配度评分（0-100）
- match_reasons为匹配原因列表
- 只输出匹配度大于60的接口
- 按匹配度从高到低排序
"""

    return prompt

def parse_api_matching_result(matching_result: str, api_nodes: List[dict]) -> List[dict]:
    """
    解析大模型返回的API匹配结果

    Args:
        matching_result: 大模型返回的匹配结果
        api_nodes: 原始API节点列表

    Returns:
        List[dict]: 解析后的相关API列表
    """
    related_apis = []

    try:
        # 尝试从返回结果中提取JSON
        import re
        json_match = re.search(r'\{.*\}', matching_result, re.DOTALL)
        if json_match:
            json_str = json_match.group()
            result_data = json.loads(json_str)

            matched_apis = result_data.get("matched_apis", [])

            for match in matched_apis:
                api_index = match.get("api_index", 0) - 1  # 转换为0基索引
                match_score = match.get("match_score", 0)
                match_reasons = match.get("match_reasons", [])

                # 验证索引有效性
                if 0 <= api_index < len(api_nodes):
                    node = api_nodes[api_index]

                    api_info = {
                        "id": node.get("id"),
                        "name": node.get("name", ""),
                        "method": node.get("method", "").upper(),
                        "path": node.get("path", ""),
                        "summary": node.get("summary", ""),
                        "match_score": match_score,
                        "match_reason": match_reasons,
                        "parameters": node.get("parameters", []),
                        "request_body": node.get("request_body", {}),
                        "responses": node.get("responses", {}),
                        "tags": node.get("tags", [])
                    }

                    related_apis.append(api_info)

            # 按匹配分数排序
            related_apis.sort(key=lambda x: x.get("match_score", 0), reverse=True)

        else:
            logger.warning("无法从大模型结果中提取JSON格式数据")

    except (json.JSONDecodeError, KeyError, ValueError) as e:
        logger.error(f"解析API匹配结果失败: {str(e)}")
        logger.error(f"原始结果: {matching_result}")

    return related_apis

def find_related_apis_fallback(llm_result: str, api_nodes: List[dict]) -> List[dict]:
    """
    传统的关键词匹配方法（作为大模型匹配失败时的回退方案）

    Args:
        llm_result: 大模型返回的前置依赖分析结果
        api_nodes: 所有API节点列表

    Returns:
        List[dict]: 相关的API接口详细信息列表
    """
    related_apis = []

    # 将大模型结果转换为小写以便匹配
    llm_result_lower = llm_result.lower()

    # 定义一些关键词映射，用于匹配接口
    create_keywords = ['创建', 'create', 'add', 'new', 'post']
    update_keywords = ['更新', 'update', 'modify', 'edit', 'put', 'patch']
    delete_keywords = ['删除', 'delete', 'remove', 'del']
    query_keywords = ['查询', 'get', 'find', 'search', 'list', 'select']

    for node in api_nodes:
        api_info = {
            "id": node.get("id"),
            "name": node.get("name", ""),
            "method": node.get("method", "").upper(),
            "path": node.get("path", ""),
            "summary": node.get("summary", ""),
            "match_reason": [],  # 记录匹配原因
            "match_score": 0     # 添加匹配分数字段
        }

        # 检查路径中是否包含大模型结果中提到的关键词
        path_lower = node.get("path", "").lower()
        method_lower = node.get("method", "").lower()
        summary_lower = node.get("summary", "").lower()
        name_lower = node.get("name", "").lower()

        # 1. 检查是否为创建类接口（根据方法和路径）
        if method_lower == 'post' or any(keyword in path_lower or keyword in summary_lower or keyword in name_lower for keyword in create_keywords):
            if any(keyword in llm_result_lower for keyword in create_keywords):
                api_info["match_reason"].append("创建类接口")
                api_info["match_score"] += 30

        # 2. 检查路径参数依赖
        import re
        path_params = re.findall(r'\{([^}]+)\}', node.get("path", ""))
        for param in path_params:
            param_lower = param.lower()
            if param_lower in llm_result_lower or param in llm_result:
                api_info["match_reason"].append(f"路径参数依赖: {param}")
                api_info["match_score"] += 25

        # 3. 检查接口名称或摘要中是否包含大模型结果中的关键词
        potential_entities = []
        chinese_words = re.findall(r'[\u4e00-\u9fff]+', llm_result)
        english_words = re.findall(r'[a-zA-Z]+', llm_result)
        potential_entities.extend(chinese_words)
        potential_entities.extend(english_words)

        for entity in potential_entities:
            entity_lower = entity.lower()
            if len(entity) > 1:  # 忽略单字符
                if (entity_lower in path_lower or entity_lower in summary_lower or
                    entity_lower in name_lower or entity in node.get("path", "") or
                    entity in node.get("summary", "") or entity in node.get("name", "")):
                    api_info["match_reason"].append(f"关键词匹配: {entity}")
                    api_info["match_score"] += 20

        # 4. 检查参数匹配
        try:
            parameters = node.get("parameters")
            if parameters:
                if isinstance(parameters, str):
                    parameters = json.loads(parameters)

                if isinstance(parameters, list):
                    for param in parameters:
                        param_name = param.get("name", "")
                        if param_name and (param_name.lower() in llm_result_lower or param_name in llm_result):
                            api_info["match_reason"].append(f"参数匹配: {param_name}")
                            api_info["match_score"] += 15
        except (json.JSONDecodeError, TypeError):
            pass

        # 5. 检查请求体字段匹配
        try:
            request_body = node.get("request_body")
            if request_body:
                if isinstance(request_body, str):
                    request_body = json.loads(request_body)

                if isinstance(request_body, dict):
                    content = request_body.get("content", {})
                    if "application/json" in content:
                        example = content["application/json"].get("example")
                        if example:
                            if isinstance(example, str):
                                try:
                                    example_dict = json.loads(example)
                                    for key in example_dict.keys():
                                        if key.lower() in llm_result_lower or key in llm_result:
                                            api_info["match_reason"].append(f"请求体字段匹配: {key}")
                                            api_info["match_score"] += 10
                                except json.JSONDecodeError:
                                    pass
                            elif isinstance(example, dict):
                                for key in example.keys():
                                    if key.lower() in llm_result_lower or key in llm_result:
                                        api_info["match_reason"].append(f"请求体字段匹配: {key}")
                                        api_info["match_score"] += 10
        except (json.JSONDecodeError, TypeError):
            pass

        # 如果有匹配原因，则添加到相关接口列表
        if api_info["match_reason"] and api_info["match_score"] > 0:
            # 添加更多详细信息
            api_info["parameters"] = node.get("parameters", [])
            api_info["request_body"] = node.get("request_body", {})
            api_info["responses"] = node.get("responses", {})
            api_info["tags"] = node.get("tags", [])

            related_apis.append(api_info)

    # 按匹配分数排序，匹配度高的排在前面
    related_apis.sort(key=lambda x: x.get("match_score", 0), reverse=True)

    return related_apis

@router.get("/map")
async def get_map_list(
    relation_uid: str = Path(..., description="知识库ID"),
    cypher: str = Query(None, description="cypher查询语句")
):
    """
    获取知识库地图列表

    Args:
        relation_uid: 知识库ID
        cypher: sql
    Returns:
        JSONResponse: 包含知识库地图列表的响应
    """
    try:
        # 初始化Neo4j模型
        model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await model.initialize()

        knowledge_model = KnowledgeBaseModel()
        await knowledge_model.initialize()
        knowledge_base_info = await knowledge_model.find_by_id(relation_uid)
        tags_info = knowledge_base_info.get('tags')

        if not cypher:
            # 查询所有节点
            nodes = await model.find_all()

            # 查询所有关系
            relationships = await model.find_relationships()

            # 获取所有类型
            categories = await model.get_categories()

        else:
            # 查询自定义节点
            nodes = await model.find_all_custom(cypher)

            # 查询自定义节点关系
            relationships = await  model.find_relationships_custom(nodes)

            # 获取自定义节点类型
            categories = await model.get_categories_custom(nodes)

        # 处理节点数据
        processed_nodes = []
        for node in nodes:
            # 确定节点类型
            node_type = node.get("type", "未知")
            
            # 构建节点数据
            node_data = {
                "id": node.get("id"),
                "name": node.get("name", ""),
                "category": 0,  # 默认分类
                "type": node_type,
                "fields": {
                    key: value for key, value in node.items()
                    if key not in ["id", "name", "type", "embedding", "text"]
                }
            }
            processed_nodes.append(node_data)

        # 处理关系数据
        processed_relationships = []
        for relationship in relationships:
            # 构建关系数据时，排除 id/source/target/type 后的其他字段作为 properties
            properties = {
                key: value for key, value in relationship.items()
                if key not in ["id", "source", "target", "type"]
            }

            relationship_data = {
                "id": relationship["id"],
                "source": relationship["source"],
                "target": relationship["target"],
                "type": relationship.get("type", "未知"),
                **properties  # 直接展开
            }

            processed_relationships.append(relationship_data)

        # 处理分类数据
        processed_categories = [{"name": "未知"}] if not categories else [{"name": category} for category in sorted(categories)]
        
        # 构建返回数据
        data = {
            "nodes": processed_nodes,
            "links": processed_relationships,
            "categories": processed_categories
        }
        
        return JSONResponse(
            status_code=200,
            content={
                "code": 200,
                "message": "获取知识库地图成功",
                "data": data,
                "tags": tags_info
            }
        )
        
    except Exception as e:
        logger.error(f"获取知识库地图失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "message": f"获取知识库地图失败: {str(e)}",
                "data": None
            }
        )
    finally:
        # 确保关闭连接
        if 'model' in locals():
            await model.close()

@router.get("/")
async def get_map_info(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    获取知识库地图信息
    
    Args:
        relation_uid: 知识库ID
        
    Returns:
        JSONResponse: 包含知识库地图信息的响应
    """
    try:
        # 创建MapInfoModel实例
        map_model = MapInfoModel()
        await map_model.initialize()
        
        try:
            # 获取知识库地图信息
            map_info = await map_model.get_map_info_by_uid(relation_uid)

            knowledge_model = KnowledgeBaseModel()
            await knowledge_model.initialize()


            if map_info:
                # 如果存在version_desc字段，从knowledge_base中查询description
                if "version_desc" in map_info:

                    try:
                        knowledge_info = await knowledge_model.find_by_id(map_info["uid"])
                        if knowledge_info and "description" in knowledge_info:
                            map_info["version_desc"] = knowledge_info["description"]
                    finally:
                        await knowledge_model.close()
                
                return JSONResponse(
                    status_code=200,
                    content=BaseResponse(
                        code=200,
                        message="获取知识库地图信息成功",
                        data=map_info
                    ).dict()
                )
            else:
                return JSONResponse(
                    status_code=404,
                    content=BaseResponse(
                        code=404,
                        message="未找到知识库地图信息",
                        data={}
                    ).dict()
                )
                
        finally:
            await map_model.close()
            
    except Exception as e:
        logger.error(f"获取知识库地图信息失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"获取知识库地图信息失败: {str(e)}",
                data={}
            ).dict()
        )
    
@router.post("/entity")
async def add_entity(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="实体数据")
):
    """
    添加实体节点

    Args:
        relation_uid: 知识库ID
        data: 实体数据，包含以下字段：
            - name: 实体名称
            - type: 实体类型
            - fields: 其他属性字段

    Returns:
        JSONResponse: 包含操作结果的响应
    """
    try:
        # 获取知识库版本信息
        map_model = MapInfoModel()
        await map_model.initialize()
        try:
            map_info = await map_model.get_map_info_by_uid(relation_uid)
            if not map_info:
                version = map_info.get("version")
            else:
                version = map_info.get("version", "V1")
        finally:
            await map_model.close()
            
        # 自动生成节点ID
        node_id = str(uuid.uuid4())

        entity_data = data.get("entity_data")
        # 构建节点数据，将fields字段展开
        node_data = {
            "id": node_id,
            "name": entity_data.get("name", ""),
            "type": entity_data.get("type", "未知"),
            "version": version,
            **entity_data.get("fields", {})  # 展开fields字段
        }
        
        # 初始化Neo4j模型，使用多个标签
        model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await model.initialize()
        
        # 创建节点
        success = await model.create(node_data)
        
        if success:
            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message="添加实体成功",
                    data=node_data  # 直接返回完整的node_data
                ).dict()
            )
        else:
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message="添加实体失败",
                    data={}
                ).dict()
            )
            
    except Exception as e:
        logger.error(f"添加实体失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"添加实体失败: {str(e)}",
                data={}
            ).dict()
        )
    finally:
        # 确保关闭连接
        if 'model' in locals():
            await model.close()

@router.delete("/entity")
async def delete_entity(
    relation_uid: str = Path(..., description="知识库关系ID"),
    data: dict = Body(..., description="实体数据")
):
    """
    删除实体及其关系
    
    Args:
        relation_uid: 知识库关系ID
        data: 实体数据，包含以下字段：
            - version: 版本
            - entity_id: 实体id
    Returns:
        JSONResponse: 删除结果
    """
    model = None
    try:
        # 获取知识库版本信息
        map_model = MapInfoModel()
        await map_model.initialize()
        try:
            map_info = await map_model.get_map_info_by_uid(relation_uid)
            if not map_info:
                return JSONResponse(
                    status_code=404,
                    content=BaseResponse(
                        code=404,
                        message="知识库不存在",
                        data={}
                    ).dict()
                )
            version = map_info.get("version")
        finally:
            await map_model.close()
            
        # 初始化Neo4j模型，使用多个标签
        model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await model.initialize()
        
        # 删除实体及其关系
        success = await model.delete(data.get("entity_id"))
        if not success:
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message="删除实体失败",
                    data={}
                ).dict()
            )
            
        return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message="删除实体成功",
                data={
                    "entity_id": data.get("entity_id"),
                    "relation_uid": relation_uid
                }
            ).dict()
        )
        
    except Exception as e:
        logger.error(f"删除实体失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"删除实体失败: {str(e)}",
                data={}
            ).dict()
        )
    finally:
        # 确保关闭连接
        if model:
            await model.close()

@router.post("/relation")
async def add_relation(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="关系数据")
):
    """
    添加关系

    Args:
        relation_uid: 知识库ID
        data: 关系数据，包含以下字段：
            - source_id: 源节点ID
            - target_id: 目标节点ID
            - type: 关系类型
            - fields: 其他属性字段

    Returns:
        JSONResponse: 包含操作结果的响应
    """
    model = None
    try:
        # 获取知识库版本信息
        map_model = MapInfoModel()
        await map_model.initialize()
        try:
            map_info = await map_model.get_map_info_by_uid(relation_uid)
            if not map_info:
                return JSONResponse(
                    status_code=404,
                    content=BaseResponse(
                        code=404,
                        message="知识库不存在",
                        data={}
                    ).dict()
                )
            version = map_info.get("version", "V1")
        finally:
            await map_model.close()
            
        # 自动生成关系ID
        relation_id = str(uuid.uuid4())
        
        # 构建关系数据
        relation_data = {
            "id": relation_id,
            "source_id": data.get("source_id"),
            "target_id": data.get("target_id"),
            "type": data.get("type", "未知"),
            "version": version,
            **data.get("fields", {})  # 展开fields字段
        }
        
        # 初始化Neo4j模型，使用多个标签
        model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await model.initialize()
        
        # 创建关系，只传递关系属性
        success = await model.create_relationship(
            source_id=relation_data["source_id"],
            target_id=relation_data["target_id"],
            relation_type=relation_data["type"],
            properties={
                "id": relation_data["id"],
                "version": relation_data["version"],
                **data.get("fields", {})  # 只传递fields中的属性
            }
        )
        
        if success:
            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message="添加关系成功",
                    data=relation_data
                ).dict()
            )
        else:
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message="添加关系失败",
                    data={}
                ).dict()
            )
            
    except Exception as e:
        logger.error(f"添加关系失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"添加关系失败: {str(e)}",
                data={}
            ).dict()
        )
    finally:
        # 确保关闭连接
        if model:
            await model.close()


@router.post("/archive")
async def archive(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="归档数据")
):
    """
    归档知识库版本
    
    Args:
        relation_uid: 知识库ID
        data: 归档数据，包含：
            - version: 版本号
            - version_desc: 版本描述
            
    Returns:
        JSONResponse: 操作结果
    """
    try:
        # 验证必要参数
        if not data.get("version"):
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="版本号不能为空",
                    data={}
                ).dict()
            )
            
        # 1. 更新 map_info 表的版本信息
        map_info_model = MapInfoModel()
        await map_info_model.initialize()
        update_result = await map_info_model.update(
            relation_uid,
            {
                "version": data["version"],
                "version_desc": data.get("version_desc", "")
            }
        )
        
        if not update_result:
            await map_info_model.close()
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message="更新知识库版本信息失败",
                    data={}
                ).dict()
            )
            
        # 2. 插入版本历史记录
        history_model = MapInfoHistoryModel()
        await history_model.initialize()
        
        insert_result = await history_model.insert_all([{
            "uid": relation_uid,
            "version": data["version"],
            "version_desc": data.get("version_desc", "")
        }])
        
        if not insert_result:
            await map_info_model.close()
            await history_model.close()
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message="保存版本历史记录失败",
                    data={}
                ).dict()
            )
            
        # 关闭连接
        await map_info_model.close()
        await history_model.close()
        
        return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message="版本归档成功",
                data={
                    "version": data["version"],
                    "version_desc": data.get("version_desc", "")
                }
            ).dict()
        )
        
    except Exception as e:
        logger.error(f"版本归档失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"版本归档失败: {str(e)}",
                data={}
            ).dict()
        )

@router.get("/archive_history")
async def get_history(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    获取知识库的历史版本记录
    
    Args:
        relation_uid: 知识库ID
        
    Returns:
        JSONResponse: 历史版本记录列表
    """
    try:
        # 初始化模型
        history_model = MapInfoHistoryModel()
        await history_model.initialize()
        
        # 获取历史版本记录
        history_list = await history_model.get_history_by_uid(relation_uid)
        
        # 关闭连接
        await history_model.close()
        
        return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message="获取历史版本成功",
                data={
                    "history": history_list
                }
            ).dict()
        )
        
    except Exception as e:
        logger.error(f"获取历史版本失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"获取历史版本失败: {str(e)}",
                data={}
            ).dict()
        )

@router.delete("/history/{version}")
async def delete_history(
    relation_uid: str = Path(..., description="知识库ID"),
    version: str = Path(..., description="版本号")
):
    """
    删除指定版本的历史记录
    
    Args:
        relation_uid: 知识库ID
        version: 版本号
        
    Returns:
        JSONResponse: 操作结果
    """
    try:
        # 初始化模型
        history_model = MapInfoHistoryModel()
        await history_model.initialize()
        
        # 删除历史记录
        result = await history_model.delete_by_version(relation_uid, version)
        
        # 关闭连接
        await history_model.close()
        
        if result:
            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message="删除历史版本成功",
                    data={}
                ).dict()
            )
        else:
            return JSONResponse(
                status_code=404,
                content=BaseResponse(
                    code=404,
                    message="未找到指定的历史版本",
                    data={}
                ).dict()
            )
        
    except Exception as e:
        logger.error(f"删除历史版本失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"删除历史版本失败: {str(e)}",
                data={}
            ).dict()
        )

@router.post("/rollback/{version}")
async def rollback_version(
    relation_uid: str = Path(..., description="知识库ID"),
    version: str = Path(..., description="版本号")
):
    """
    回退到指定版本
    
    Args:
        relation_uid: 知识库ID
        version: 版本号
        
    Returns:
        JSONResponse: 操作结果
    """
    try:
        # 1. 获取指定版本的信息
        history_model = MapInfoHistoryModel()
        await history_model.initialize()
        
        history_list = await history_model.get_history_by_uid(relation_uid)
        target_version = next((item for item in history_list if item["version"] == version), None)
        
        if not target_version:
            await history_model.close()
            return JSONResponse(
                status_code=404,
                content=BaseResponse(
                    code=404,
                    message="未找到指定的历史版本",
                    data={}
                ).dict()
            )
            
        # 2. 更新 map_info 表的版本信息
        map_info_model = MapInfoModel()
        await map_info_model.initialize()
        
        update_result = await map_info_model.update(
            relation_uid,
            {
                "version": target_version["version"],
                "version_desc": target_version["version_desc"]
            }
        )
        
        # 关闭连接
        await history_model.close()
        await map_info_model.close()
        
        if update_result:
            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message="版本回退成功",
                    data={
                        "version": target_version["version"],
                        "version_desc": target_version["version_desc"]
                    }
                ).dict()
            )
        else:
            return JSONResponse(
                status_code=500,
                content=BaseResponse(
                    code=500,
                    message="版本回退失败",
                    data={}
                ).dict()
            )
        
    except Exception as e:
        logger.error(f"版本回退失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"版本回退失败: {str(e)}",
                data={}
            ).dict()
        )

@router.get("/recall_history")
async def get_recall_history(
    relation_uid: str = Path(..., description="知识库ID"),
):
    """
    获取用户的召回测试历史记录
    
    Args:
        relation_uid: 知识库ID
        
    Returns:
        JSONResponse: 历史记录列表
    """
    try:
        # 初始化模型
        history_model = RecallTestHistoryModel()
        await history_model.initialize()
        
        # 获取历史记录
        history_list = await history_model.get_recent_history(relation_uid)
        
        # 关闭连接
        await history_model.close()
        
        return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message="获取召回测试历史记录成功",
                data={
                    "history": history_list
                }
            ).dict()
        )
        
    except Exception as e:
        logger.error(f"获取召回测试历史记录失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"获取召回测试历史记录失败: {str(e)}",
                data={}
            ).dict()
        )

@router.get("/node_properties")
async def get_node_properties(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    获取节点的属性类型信息
    
    Args:
        relation_uid: 知识库ID
        
    Returns:
        JSONResponse: 属性类型信息
    """
    try:
        # 初始化模型
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()
        
        # 获取属性类型
        properties = await neo4j_model.get_node_properties()
        
        # 获取节点类型
        node_types = await neo4j_model.get_node_types()
        
        # 关闭连接
        await neo4j_model.close()
        
        return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message="获取节点属性类型成功",
                data={
                    "properties": properties,
                    "node_types": node_types
                }
            ).dict()
        )
        
    except Exception as e:
        logger.error(f"获取节点属性类型失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"获取节点属性类型失败: {str(e)}",
                data={}
            ).dict()
        )


@router.post("/search_relations")
async def search_relations(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="请求数据")
):
    """
    搜索实体并获取其关系
    
    Args:
        relation_uid: 知识库ID
        data: 请求数据，包含：
            - content: 查询信息
            - topk: 返回数量
            - field: 搜索字段
            - method: 匹配方式
            - target_type: 目标实体类型列表
            - target_types: 目标实体类型列表（与target_type相同）
            - type: 匹配方式
            
    Returns:
        JSONResponse: 搜索结果和关系
    """
    try:
        # 验证必要参数
        if not data.get("content"):
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="查询信息不能为空",
                    data={}
                ).dict()
            )
            
        if not data.get("field"):
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="搜索字段不能为空",
                    data={}
                ).dict()
            )
            
        # 获取目标类型列表，支持两种参数名称
        target_types = data.get("target_types") or data.get("target_type")
        if not target_types:
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="目标实体类型不能为空",
                    data={}
                ).dict()
            )
            
        # 确保target_types是列表类型
        if isinstance(target_types, str):
            target_types = [target_types]
            
        # 初始化模型
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()
        
        # 搜索实体
        entities = await neo4j_model.search_entities(
            field=data["field"],
            value=data["content"],
            method=data.get("method", "cover"),
            topk=data.get("topk", 10),
            target_types=target_types
        )
        
        # 获取每个实体的关系
        result = []
        for entity in entities:
            # 获取实体的所有一级关系
            relations = await neo4j_model.find_entity_relations(str(entity["id"]))
            entity['relations'] = relations
            # 构建结果
            result.append(entity)
        
        # 关闭连接
        await neo4j_model.close()
        
        # 保存查询历史记录
        history_model = RecallTestHistoryModel()
        await history_model.initialize()
        try:
            await history_model.insert_all([{
                "uid": relation_uid,
                "type": "模糊匹配" if data["type"] == "txt" else "向量检索",
                "content": data["content"],
                "time": datetime.now()
            }])
        finally:
            await history_model.close()
        
        return JSONResponse(
            status_code=200,
            content=BaseResponse(
                code=200,
                message="搜索成功",
                data=result
            ).dict()
        )
        
    except Exception as e:
        logger.error(f"搜索失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"搜索失败: {str(e)}",
                data={}
            ).dict()
        )

@router.delete("/delete_relation/{relation_id}", response_model=BaseResponse)
async def delete_relation(
    relation_uid: str = Path(..., description="知识库ID"),
    relation_id: str = Path(..., description="关系ID")
):
    """
    根据关系ID删除知识图谱中的关系

    Args:
        relation_uid: 知识库ID (图谱标签的一部分)
        relation_id: 要删除的关系ID

    Returns:
        BaseResponse: 响应结果，包含：
            - code: 状态码
                - 200: 删除成功f
                - 404: 关系未找到或知识库信息不存在
                - 500: 服务器错误
            - message: 响应消息
            - data: {}
    """

    neo4j_model = None  # 初始化为 None

    try:
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()

        deleted = await neo4j_model.delete_relationship(relation_id)

        if deleted:
            logger.info(f"成功删除关系 {relation_id} (知识库: {relation_uid}")
            return BaseResponse(
                code=200,
                message=f"关系删除成功: {relation_id}",
                data={}
            )
        else:
            logger.warning(f"尝试删除关系 {relation_id} 但未找到 (知识库: {relation_uid}")
            return BaseResponse(
                code=404,
                message=f"关系未找到或无法删除: {relation_id}",
                data={}
            )

    except Exception as e:
        logger.error(f"删除关系 {relation_id} (知识库: {relation_uid}) 时发生未处理的错误: {e}", exc_info=True)
        return BaseResponse(
            code=500,
            message=f"删除关系时发生内部服务器错误",
            data={}
        )
    finally:
        # 5. 关闭 Neo4j 连接 (确保即使出错也关闭)
        if neo4j_model:
            await neo4j_model.close()

@router.get("/get_doc_list", response_model=BaseResponse)
async def get_doc_list(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    获取知识库下的文档列表（来自 map_doc_api 表）

    Args:
        relation_uid: 知识库ID

    Returns:
        BaseResponse: 响应对象，包含：
            - document_name: 文档名称
            - entity_count: 实体数量
            - status: 文档状态
            - create_time: 创建时间
    """
    map_doc_api_model = None
    try:
        map_doc_api_model = MapDocApiModel()
        await map_doc_api_model.initialize()

        doc_list = await map_doc_api_model.get_doc_mappings_by_kb_uid(relation_uid)

        return BaseResponse(
                code=200,
                message="获取文档列表成功",
                data=doc_list if doc_list else []
        )

    except Exception as e:
        logger.error(f"获取文档列表失败: {str(e)}")
        return BaseResponse(code=500, message="获取文档列表失败", data={})

    finally:
        await map_doc_api_model.close()

@router.delete("/delete_endpoints_doc", response_model=BaseResponse)
async def delete_endpoints_doc(
    relation_uid: str = Path(..., description="知识库ID"),
    document_name: str = Query(..., description="文档名称")
):
    """
    删除知识库下的文档映射记录

    Args:
        relation_uid: 知识库ID
        document_name: 文档名称

    Returns:
        BaseResponse: 响应对象
    """
    try:
        map_doc_model = MapDocApiModel()
        await map_doc_model.initialize()

        component_model = MapDocComponentsModel()
        await component_model.initialize()
        try:
            # 删除api
            api_success = await map_doc_model.delete_doc_mapping_by_name(document_name, relation_uid)
            # 删除组件
            component_success =  await component_model.delete_doc_mapping_by_name(document_name, relation_uid)

            if api_success or component_success:
                return BaseResponse(
                    code=200,
                    message=f"成功删除文档映射记录: {document_name} 及其关联的组件",
                    data={}
                )
            else:
                return BaseResponse(
                    code=404,
                    message=f"未找到文档映射记录: {document_name}",
                    data={}
                )

        finally:
            await map_doc_model.close()
            await component_model.close()

    except Exception as e:
        logger.error(f"删除文档映射记录失败: {str(e)}")
        return BaseResponse(
            code=500,
            message="删除文档映射记录失败",
            data={}
        )

@router.post("/endpoints_upload", response_model=BaseResponse)
async def endpoints_upload(
        relation_uid: str = Path(..., description="知识库ID"),
        file: UploadFile = File(..., description="上传的Swagger JSON文件")
):
    """
    上传并处理Swagger文档，提取API端点和组件信息存储到数据库
    支持大文件上传（最大100MB）

    Args:
        relation_uid: 知识库ID
        file: 上传的Swagger JSON文件

    Returns:
        BaseResponse: 响应对象
    """
    # 文件大小限制：100MB
    MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB in bytes
    
    try:
        # 1. 检查文件后缀是否为json
        file_name = file.filename
        if not file_name:
            return BaseResponse(
                code=400,
                message="文件名不能为空",
                data={}
            )
            
        file_ext = os.path.splitext(file_name)[1].lower()
        if file_ext != '.json':
            return BaseResponse(
                code=400,
                message="只支持上传JSON文件",
                data={}
            )

        # 2. 检查文件大小
        file_size = 0
        content = b""
        
        # 分块读取文件内容，避免内存溢出
        chunk_size = 1024 * 1024  # 1MB chunks
        logger.info(f"开始读取文件: {file_name}")
        
        while True:
            chunk = await file.read(chunk_size)
            if not chunk:
                break
            content += chunk
            file_size += len(chunk)
            
            # 检查文件大小是否超过限制
            if file_size > MAX_FILE_SIZE:
                return BaseResponse(
                    code=413,
                    message=f"文件大小超过限制，最大支持100MB，当前文件大小: {file_size / (1024*1024):.2f}MB",
                    data={}
                )
        
        logger.info(f"文件读取完成: {file_name}, 大小: {file_size / (1024*1024):.2f}MB")

        # 3. 创建临时文件保存上传的文件
        temp_file_path = f"temp_{uuid.uuid4().hex}_{file_name}"
        try:
            with open(temp_file_path, "wb") as buffer:
                buffer.write(content)
            logger.info(f"临时文件保存成功: {temp_file_path}")
        except Exception as e:
            logger.error(f"保存上传文件失败: {str(e)}")
            return BaseResponse(
                code=500,
                message="保存上传文件失败",
                data={}
            )

        # 4. 创建模型实例
        api_model = MapDocApiModel()
        comp_model = MapDocComponentsModel()

        try:
            # 5. 初始化数据库连接
            await api_model.initialize()
            await comp_model.initialize()

            # 6. 检查是否存在同名文档（两个表都检查）
            api_exists = await api_model.check_doc_name(file_name, relation_uid)
            comp_exists = await comp_model.check_doc_name(file_name, relation_uid)

            if api_exists or comp_exists:
                logger.warning(f"知识库 {relation_uid} 中已存在同名文档: {file_name}")
                return BaseResponse(
                    code=400,
                    message=f"知识库中已存在同名文档: {file_name}",
                    data={}
                )

            # 7. 提取API端点信息
            try:
                logger.info(f"开始提取API端点信息: {temp_file_path}")
                logger.info(f"文件是否存在: {os.path.exists(temp_file_path)}")

                api_extractor = ExtractSwaggerPathEndpoints(temp_file_path)
                api_entities = api_extractor.extract_swagger_endpoints()

                if not api_entities:
                    logger.warning("未能提取到任何 API 端点，请检查文件内容是否符合 Swagger/OpenAPI 格式")
                    return BaseResponse(
                        code=400,
                        message="未能提取到有效的 API 实体",
                        data={}
                    )

                logger.info(f"从 {file_name} 中提取到 {len(api_entities)} 个API端点")
            except Exception as e:
                logger.error(f"提取API端点失败: {str(e)}", exc_info=True)
                return BaseResponse(
                    code=500,
                    message="提取API端点失败",
                    data={}
                )

            # 8. 提取组件信息
            try:
                logger.info(f"开始提取组件信息: {temp_file_path}")
                comp_extractor = ExtractSwaggerComponents(temp_file_path)
                comp_entities = comp_extractor.extract_swagger_components()
                logger.info(f"从 {file_name} 中提取到 {len(comp_entities)} 个组件")
            except Exception as e:
                logger.error(f"提取组件失败: {str(e)}")
                return BaseResponse(
                    code=500,
                    message="提取组件失败",
                    data={}
                )

            # 9. 批量保存API端点数据到map_doc_api表
            logger.info(f"开始保存API端点数据，总数: {len(api_entities)}")
            api_success = 0
            api_failed = 0
            
            # 分批处理，避免数据库连接超时
            batch_size = 50
            for i in range(0, len(api_entities), batch_size):
                batch = api_entities[i:i + batch_size]
                logger.info(f"处理API批次 {i//batch_size + 1}/{(len(api_entities) + batch_size - 1)//batch_size}")
                
                for entity in batch:
                    try:
                        # 提取API实体信息
                        path = entity.get("path", "")
                        method = entity.get("method", "").upper()
                        summary = entity.get("summary", "")

                        # 实体名称使用"方法 路径"格式，但确保不会超过长度限制
                        entity_name = f"{method} {path}"
                        if len(entity_name) > 100:
                            entity_name = entity_name[:100]

                        # 创建API记录
                        await api_model.create_api_record(
                            knowledge_base_uid=relation_uid,
                            document_name=file_name,
                            entity_name=entity_name,
                            path=path,
                            method=method,
                            tags=entity.get("tags", []),
                            summary=summary,
                            parameters=entity.get("parameters", []),
                            request_body=entity.get("request_body", {}),
                            responses=entity.get("responses", {}),
                            status="未嵌入"
                        )
                        api_success += 1
                    except Exception as e:
                        logger.error(f"保存API端点失败: {str(e)}")
                        logger.debug(f"失败API实体: {json.dumps(entity, indent=2)}")
                        api_failed += 1

            # 10. 批量保存组件数据到map_doc_components表
            logger.info(f"开始保存组件数据，总数: {len(comp_entities)}")
            comp_success = 0
            comp_failed = 0
            
            for i in range(0, len(comp_entities), batch_size):
                batch = comp_entities[i:i + batch_size]
                logger.info(f"处理组件批次 {i//batch_size + 1}/{(len(comp_entities) + batch_size - 1)//batch_size}")
                
                for component in batch:
                    try:
                        # 提取组件信息
                        schema_name = component.get("schema_name", "")

                        # 创建组件记录
                        await comp_model.create_component_record(
                            knowledge_base_uid=relation_uid,
                            document_name=file_name,
                            entity_name=schema_name,
                            schema_type=component.get("type", ""),
                            title=component.get("title", ""),
                            default_value=component.get("default_value"),
                            enum_values=component.get("enum_values", []),
                            properties=component.get("properties", {}),
                            required_properties=component.get("required_properties", []),
                            status="未嵌入"
                        )
                        comp_success += 1
                    except Exception as e:
                        logger.error(f"保存组件失败: {str(e)}")
                        logger.debug(f"失败组件: {json.dumps(component, indent=2)}")
                        comp_failed += 1

            # 11. 返回结果
            total_api = len(api_entities)
            total_comp = len(comp_entities)
            total_success = api_success + comp_success
            total_failed = api_failed + comp_failed

            message = f"成功保存 {api_success} 个API端点和 {comp_success} 个组件"
            if total_failed > 0:
                message += f"（失败: {total_failed}）"

            logger.info(f"文件处理完成: {file_name}, API成功: {api_success}, 组件成功: {comp_success}, 失败: {total_failed}")

            return BaseResponse(
                code=200,
                message=message,
                data={
                    "file_info": {
                        "name": file_name,
                        "size_mb": round(file_size / (1024*1024), 2)
                    },
                    "api": {
                        "total": total_api,
                        "success": api_success,
                        "failed": api_failed
                    },
                    "components": {
                        "total": total_comp,
                        "success": comp_success,
                        "failed": comp_failed
                    }
                }
            )

        except Exception as e:
            logger.error(f"数据库操作失败: {str(e)}")
            return BaseResponse(
                code=500,
                message="数据库操作失败",
                data={}
            )
        finally:
            # 确保清理资源
            await api_model.close()
            await comp_model.close()
            try:
                os.remove(temp_file_path)
                logger.info(f"临时文件已删除: {temp_file_path}")
            except Exception as e:
                logger.warning(f"删除临时文件失败: {str(e)}")

    except Exception as e:
        logger.error(f"处理上传文件失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"处理过程中发生错误: {str(e)}",
            data={}
        )

@router.get("/get_entiy_list", response_model=BaseResponse)
async def get_entiy_list(
    relation_uid: str = Path(..., description="知识库ID"),
    # document_name: str = Query(..., description="文档名称")
    document_name: str = Query(None, description="文档名称（可选）")
):

    """
    获取指定知识库下指定文档的所有实体详细信息
    """
    try:
        map_doc_model = MapDocApiModel()
        await map_doc_model.initialize()
        try:
            if document_name:
                entity_list = await map_doc_model.get_api_mappings_by_kb_uid_and_doc_name(relation_uid, document_name)
            else:
                entity_list = await map_doc_model.get_api_mappings_by_kb_uid_all(relation_uid)
            if entity_list is None:
                entity_list = []

            if not isinstance(entity_list, list):
                logger.error(f"entity_list is not a list, actual type: {type(entity_list)}")
                entity_list = []

            # 新增字段 parameters_list 和 request_body_list
            for entity in entity_list:
                # 提取 parameters.name -> parameters_list
                parameters = entity.get("parameters") or []
                if not isinstance(parameters, list):
                    logger.error(f"parameters is not a list, actual type: {type(parameters)}")
                    parameters = []
                name_list = [param.get("name") for param in parameters if param.get("name")]
                entity["parameters_list"] = "、".join(name_list) if name_list else ""

                # 提取 request_body.example 的 keys -> request_body_list
                request_body = entity.get("request_body", {})

                example_str = json_utils.safe_get(request_body, "content", "application/json", "example")

                try:
                    if isinstance(example_str, str) and example_str.strip():
                        example_dict = json.loads(example_str)
                        keys = list(example_dict.keys())
                        entity["request_body_list"] = "、".join(keys) if keys else ""
                    else:
                        entity["request_body_list"] = ""
                except Exception as e:
                    logger.warning(f"解析 request_body.example 失败: {str(e)}")
                    entity["request_body_list"] = ""

                # 新增：提取 responses 中 200 或 default 的 example 的 keys -> responses_list
                responses = entity.get("responses", {})
                example_data = None

                if "200" in responses:
                    example_data = responses["200"].get("content", {}).get("application/json", {}).get("example", "")
                elif "default" in responses:
                    example_data = responses["default"].get("content", {}).get("application/json", {}).get("example", "")

                try:
                    if isinstance(example_data, str) and example_data.strip():
                        example_dict = json.loads(example_data)
                        keys = list(example_dict.keys())
                        entity["responses_list"] = "、".join(keys) if keys else ""
                    else:
                        entity["responses_list"] = ""
                except Exception as e:
                    logger.warning(f"解析 responses.example 失败: {str(e)}")
                    entity["responses_list"] = ""

            return BaseResponse(
                code=200,
                message="获取实体列表成功",
                data=entity_list
            )
        finally:
            await map_doc_model.close()

    except Exception as e:
        logger.error(f"获取实体列表失败: {str(e)}")
        return BaseResponse(
            code=500,
            message="获取实体列表失败",
            data={}
        )

@router.get("/get_data_model_list/")
async def get_data_model_list(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    获取指定知识库下所有组件记录的完整信息（不依赖文档名称）

    Args:
        relation_uid: 知识库ID

    Returns:
        BaseResponse: 响应对象，包含：
            - 所有字段信息（包括 enum_values、properties 等 JSON 字段）
    """
    try:
        component_model = MapDocComponentsModel()
        await component_model.initialize()

        try:
            # 调用封装好的数据库方法，只使用 knowledge_base_uid 查询所有组件
            results = await component_model.get_component_mappings_by_kb_uid_and_doc_name(
                knowledge_base_uid=relation_uid
            )

            if not results:
                await component_model.close()
                return BaseResponse(
                    code=200,
                    message="未找到相关组件记录",
                    data=[]
                )

            return BaseResponse(
                code=200,
                message="获取组件记录成功",
                data=results
            )

        finally:
            await component_model.close()

    except Exception as e:
        logger.error(f"获取组件记录失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"获取组件记录失败: {str(e)}",
            data={}
        )

@router.post("/entities")
async def add_entities(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="实体ID列表")
):
    """
    批量添加实体节点（基于 map_doc_api 表）

    Args:
        relation_uid: 知识库ID
        data: 实体ID列表，包含以下字段：
            - entity_list: 实体ID列表，如 [1019, 1020]

    Returns:
        JSONResponse: 包含操作结果的响应
    """
    try:
        # 获取知识库版本信息
        map_model = MapInfoModel()
        await map_model.initialize()
        try:
            map_info = await map_model.get_map_info_by_uid(relation_uid)
            version = map_info.get("version", "V1")
        finally:
            await map_model.close()

        # 获取实体ID列表
        entity_ids = data.get("entity_list", [])
        if not entity_ids:
            await map_model.close()
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="实体ID列表为空",
                    data={}
                ).dict()
            )

        # 初始化MapDocApi模型
        map_doc_model = MapDocApiModel()
        await map_doc_model.initialize()

        # 初始化Neo4j模型
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()

        success_nodes = []
        failed_nodes = []
        skipped_nodes = []  # 新增：跳过添加的节点

        try:
            for entity_id in entity_ids:
                try:
                    # 获取API实体信息
                    entity_info = await map_doc_model.get_api_record_by_id(entity_id)
                    if not entity_info:
                        failed_nodes.append({
                            "id": entity_id,
                            "error": "实体不存在"
                        })
                        continue

                    # 检查实体是否属于当前知识库
                    if entity_info.get("knowledge_base_uid") != relation_uid:
                        failed_nodes.append({
                            "id": entity_id,
                            "error": "实体不属于当前知识库"
                        })
                        continue

                    # 构建节点数据
                    method = entity_info.get("method", "").upper()
                    path = entity_info.get("path", "")
                    entity_name = f"{method} {path}"  # 方法+路径组成唯一标识

                    # 查询是否已存在相同 method + path 的节点
                    existing_node = await neo4j_model.find_node_by_method_and_path(method, path)
                    if existing_node:
                        skipped_nodes.append({
                            "id": entity_id,
                            "reason": f"已存在方法为 '{method}' 且路径为 '{path}' 的接口"
                        })
                        await map_doc_model.update_status(entity_id, "已跳过（重复）")
                        continue

                    # 自动生成节点ID
                    node_id = str(uuid.uuid4())

                    node_data = {
                        "id": node_id,
                        "name": entity_name,
                        "type": "API",  # 固定类型为 API
                        "version": version,
                        "path": path,
                        "method": method,
                        "summary": entity_info.get("summary", ""),
                        "tags": entity_info.get("tags", []),
                        "parameters": entity_info.get("parameters", []),
                        "request_body": entity_info.get("request_body", {}),
                        "responses": entity_info.get("responses", {})
                    }

                    # 创建节点
                    success = await neo4j_model.create(node_data)

                    if success:
                        # 更新map_doc_api表中对应记录的状态为"已嵌入"
                        await map_doc_model.update_status(entity_id, "已嵌入")
                        success_nodes.append(node_data)
                    else:
                        # 更新状态为"嵌入失败"
                        await map_doc_model.update_status(entity_id, "嵌入失败")
                        failed_nodes.append({
                            "id": entity_id,
                            "error": "创建节点失败"
                        })

                except Exception as e:
                    # 更新状态为"嵌入失败"
                    await map_doc_model.update_status(entity_id, "嵌入失败")
                    failed_nodes.append({
                        "id": entity_id,
                        "error": str(e)
                    })
                    logger.error(f"创建节点失败: {str(e)}")

            # 返回批量创建结果
            response_data = {
                "success_nodes": success_nodes,
                "failed_nodes": failed_nodes
            }

            if skipped_nodes:
                response_data["skipped_nodes"] = skipped_nodes

            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message=f"批量添加实体完成，成功: {len(success_nodes)}，失败: {len(failed_nodes)}，跳过: {len(skipped_nodes)}",
                    data=response_data
                ).dict()
            )

        finally:
            # 确保关闭连接
            await map_doc_model.close()
            await neo4j_model.close()

    except Exception as e:
        logger.error(f"批量添加实体失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"批量添加实体失败: {str(e)}",
                data={}
            ).dict()
        )

@router.post("/add_components")
async def add_components(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="组件ID列表")
):
    """
    批量添加组件节点（基于 map_doc_component 表）

    Args:
        relation_uid: 知识库ID
        data: 组件ID列表，包含以下字段：
            - component_list: 组件ID列表，如 [1001, 1002]

    Returns:
        JSONResponse: 包含操作结果的响应
    """
    try:
        # 获取知识库版本信息
        map_model = MapInfoModel()
        await map_model.initialize()
        try:
            map_info = await map_model.get_map_info_by_uid(relation_uid)
            version = map_info.get("version", "V1")
        finally:
            await map_model.close()

        # 获取组件ID列表
        component_ids = data.get("entity_list", [])
        if not component_ids:
            return JSONResponse(
                status_code=400,
                content=BaseResponse(
                    code=400,
                    message="组件ID列表为空",
                    data={}
                ).dict()
            )

        # 初始化MapDocComponents模型
        component_model = MapDocComponentsModel()
        await component_model.initialize()

        # 初始化Neo4j模型
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()

        success_nodes = []
        failed_nodes = []

        try:
            for component_id in component_ids:
                try:
                    # 获取组件实体信息
                    component_info = await component_model.get_component_record_by_id(component_id)
                    if not component_info:
                        failed_nodes.append({
                            "id": component_id,
                            "error": "组件不存在"
                        })
                        continue

                    # 检查组件是否属于当前知识库
                    if component_info.get("knowledge_base_uid") != relation_uid:
                        failed_nodes.append({
                            "id": component_id,
                            "error": "组件不属于当前知识库"
                        })
                        continue

                    # 自动生成节点ID
                    node_id = str(uuid.uuid4())

                    # 构建节点数据
                    node_data = {
                        "id": node_id,
                        "name": component_info.get("entity_name", ""),
                        "type": "Component",  # 固定类型为 Component
                        "version": version,
                        "schema_type": component_info.get("schema_type", ""),
                        "title": component_info.get("title", ""),
                        "default_value": component_info.get("default_value", ""),
                        "enum_values": component_info.get("enum_values", []),
                        "properties": component_info.get("properties", {}),
                        "required_properties": component_info.get("required_properties", [])
                    }

                    # 创建节点
                    success = await neo4j_model.create(node_data)

                    if success:
                        # 更新map_doc_component表中对应记录的状态为"已嵌入"
                        await component_model.update_status(component_id, "已嵌入")
                        success_nodes.append(node_data)
                    else:
                        # 更新状态为"嵌入失败"
                        await component_model.update_status(component_id, "嵌入失败")
                        failed_nodes.append({
                            "id": component_id,
                            "error": "创建节点失败"
                        })

                except Exception as e:
                    # 更新状态为"嵌入失败"
                    await component_model.update_status(component_id, "嵌入失败")
                    failed_nodes.append({
                        "id": component_id,
                        "error": str(e)
                    })
                    logger.error(f"创建组件节点失败: {str(e)}")

            # 返回批量创建结果
            return JSONResponse(
                status_code=200,
                content=BaseResponse(
                    code=200,
                    message=f"批量添加组件完成，成功: {len(success_nodes)}，失败: {len(failed_nodes)}",
                    data={
                        "success_nodes": success_nodes,
                        "failed_nodes": failed_nodes
                    }
                ).dict()
            )

        finally:
            # 确保关闭连接
            await component_model.close()
            await neo4j_model.close()

    except Exception as e:
        logger.error(f"批量添加组件失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"批量添加组件失败: {str(e)}",
                data={}
            ).dict()
        )

@router.get("/get_graph_api_data")
async def get_graph_api_data(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    查询Neo4j图数据库中的数据，返回类型为API的节点，并新增：
        - parameters_list
        - request_body_list
        - responses_list
    """
    try:
        # 初始化Neo4j模型
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        await neo4j_model.initialize()

        # 查询类型为 API 的节点
        nodes = await neo4j_model.find_nodes_by_type("API")

        # 关闭连接
        await neo4j_model.close()

        # 处理节点数据并添加新字段
        processed_nodes = []
        for node in nodes:
            # 尝试将字符串类型的字段转为 Python 对象
            try:
                parameters = json.loads(node.get("parameters", "[]"))
            except json.JSONDecodeError:
                parameters = []

            try:
                request_body = json.loads(node.get("request_body", "{}"))
            except json.JSONDecodeError:
                request_body = {}

            try:
                responses = json.loads(node.get("responses", "{}"))
            except json.JSONDecodeError:
                responses = {}

            node_data = {
                "id": node.get("id"),
                "name": node.get("name", ""),
                "type": node.get("type", "未知"),
                "path": node.get("path", ""),
                "method": node.get("method", "").upper(),
                "summary": node.get("summary", ""),
                "tags": node.get("tags", []),
                "parameters": parameters,
                "request_body": request_body,
                "responses": responses,
            }

            # 新增字段：parameters_list
            name_list = [param.get("name") for param in parameters if isinstance(param, dict) and param.get("name")]
            node_data["parameters_list"] = "、".join(name_list) if name_list else ""

            # 新增字段：request_body_list
            example_data = ""
            content = request_body.get("content", {})
            if isinstance(content, dict):
                app_json = content.get("application/json", {})
                if isinstance(app_json, dict):
                    example_data = app_json.get("example", "")

            try:
                if isinstance(example_data, str) and example_data.strip():
                    example_dict = json.loads(example_data)
                    keys = list(example_dict.keys())
                    node_data["request_body_list"] = "、".join(keys) if keys else ""
                else:
                    node_data["request_body_list"] = ""
            except Exception as e:
                logger.warning(f"解析 request_body.example 失败: {str(e)}")
                node_data["request_body_list"] = ""

            # 新增字段：responses_list
            example_data = ""
            if isinstance(responses, dict):
                resp_200 = responses.get("200")
                default_resp = responses.get("default")

                if resp_200 and isinstance(resp_200, dict):
                    content = resp_200.get("content", {})
                    if isinstance(content, dict):
                        app_json = content.get("application/json", {})
                        if isinstance(app_json, dict):
                            example_data = app_json.get("example", "")
                elif default_resp and isinstance(default_resp, dict):
                    content = default_resp.get("content", {})
                    if isinstance(content, dict):
                        app_json = content.get("application/json", {})
                        if isinstance(app_json, dict):
                            example_data = app_json.get("example", "")

            try:
                if isinstance(example_data, str) and example_data.strip():
                    example_dict = json.loads(example_data)
                    keys = list(example_dict.keys())
                    node_data["responses_list"] = "、".join(keys) if keys else ""
                else:
                    node_data["responses_list"] = ""
            except Exception as e:
                logger.warning(f"解析 responses.example 失败: {str(e)}")
                node_data["responses_list"] = ""

            processed_nodes.append(node_data)

        return BaseResponse(
            code=200,
            message="获取接口列表成功",
            data=processed_nodes
        )

    except Exception as e:
        logger.error(f"查询图数据失败: {str(e)}")
        return (BaseResponse(
            code=500,
            message=f"查询图数据失败: {str(e)}",
            data={}
        )
    )

@router.get("/get_entities_count", response_model=BaseResponse)
async def get_entities_info(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    获取实体相关信息（数量/待嵌入数/孤儿实体），
    默认返回所有字段。

    Args:
        relation_uid: 知识库ID

    Returns:
        BaseResponse: 包含以下字段：
            - total_entities (int): 实体总数
            - pending_entities (int): 待嵌入实体数
            - orphaned_entities (list): 孤儿实体列表
            - orphaned_entities_count (int): 孤儿实体数量
            - pending_relations (int): 待确认的关系数（status = 0）
    """
    result = {}
    # 初始化模型
    map_doc_api_model = None
    neo4j_model = None
    api_relations_model = None
    try:
        map_doc_api_model = MapDocApiModel()
        neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)
        api_relations_model = ApiRelationsModel()
        await api_relations_model.initialize()
        await map_doc_api_model.initialize()
        await neo4j_model.initialize()
        # 查询所有API记录
        entities = await map_doc_api_model.get_api_mappings_by_kb_uid_all(relation_uid)

        # 总实体数
        result["total_entities"] = len(entities)

        # 待嵌入实体数
        pending_entities = [e for e in entities if e.get("status") == "未嵌入"]
        result["pending_entities"] = len(pending_entities)

        # 查询孤儿实体

        orphaned_entities = await neo4j_model.find_orphaned_entities()
        # result["orphaned_entities"] = orphaned_entities
        result["orphaned_entities_count"] = len(orphaned_entities)

        # 查询待确认的关系数（status = 0）
        relations = await api_relations_model.get_all_relations(relation_uid=relation_uid)
        pending_relations = [r for r in relations if r.get("status") == 0]
        result["pending_relations"] = len(pending_relations)

        return BaseResponse(
            code=200,
            message="获取实体信息成功",
            data=result
        )

    except Exception as e:
        logger.error(f"获取实体信息失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"获取实体信息失败: {str(e)}",
            data={}
        )
    finally:
        await api_relations_model.close()
        await map_doc_api_model.close()
        await neo4j_model.close()

@router.post("/create_task")
async def create_task(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="请求数据，包含 entity_list")
):
    task_id = None  # 初始化 task_id
    task_api_model = MapTaskApiModel()
    component_model = MapDocComponentsModel()
    try:
        # === 检查数据模型数量 ===
        await component_model.initialize()
        all_components = await component_model.get_all_components_by_kb_uid(relation_uid)
        if not all_components or len(all_components) == 0:
            return JSONResponse(
                status_code=400,
                content=BaseResponse(code=400, message="数据模型数量为0，无法创建任务", data={}).dict()
            )
        
        # === 插入任务记录 ===
        task_id = generate_task_id.generate_task_id()
        await task_api_model.initialize()

        api_ids = data.get("entity_list")
        if not api_ids:

            return JSONResponse(
                status_code=400,
                content=BaseResponse(code=400, message="API ID列表不能为空", data={}).dict()
            )

        # 批量插入 map_task_api 记录（每个 api_id 一行）
        for api_id in api_ids:
            await task_api_model.create_task_api_record(
                knowledge_base_id=relation_uid,
                task_id=task_id,
                api_id=api_id,
                status=0  # 状态：待提取
            )

        return BaseResponse(
            code=200,
            message="任务创建成功",
            data={"task_id": task_id}
        )

    except Exception as e:
        logger.error(f"创建任务失败: {str(e)}")

        # 更新任务状态为"提取失败"
        if task_id:
            task_api_model = MapTaskApiModel()
            await task_api_model.initialize()
            try:
                await task_api_model.update_task_api_status(task_id, 2)
            finally:
                await task_api_model.close()

        return JSONResponse(
            status_code=500,
            content=BaseResponse(
                code=500,
                message=f"创建任务失败: {str(e)}",
                data={}
            ).dict()
        )
    finally:
        await task_api_model.close()
        await component_model.close()

@router.post("/embed_entities_increment")
async def embed_entities_increment(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="请求数据，包含 task_id")
):
    task_id = data.get("task_id")
    if not task_id:
        return JSONResponse(
            status_code=400,
            content=BaseResponse(code=400, message="缺少 task_id", data={}).dict()
        )
    asyncio.create_task(process_embed_task(relation_uid, task_id))
    return BaseResponse(
        code=200,
        message="任务已提交，正在后台处理",
        data={"task_id": task_id}
    )
async def process_embed_task(relation_uid: str, task_id: str):
    task_api_model = MapTaskApiModel()
    map_doc_api_model = MapDocApiModel()
    component_model = MapDocComponentsModel()
    api_relations_model = ApiRelationsModel()
    neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)

    try:
        await task_api_model.initialize()
        await map_doc_api_model.initialize()
        await component_model.initialize()
        await api_relations_model.initialize()
        await neo4j_model.initialize()

        # 获取当前任务的 API IDs
        task_records = await task_api_model.get_task_apis_by_task_id(task_id)
        if not task_records:
            await task_api_model.update_task_api_status(task_id, 2)  # 2=失败
            return

        api_ids = [record["api_id"] for record in task_records]

        # 获取所有组件信息
        all_components = await component_model.get_all_components_by_kb_uid(relation_uid)
        if not all_components:
            await task_api_model.update_task_api_status(task_id, 2)
            return
        # ====== 这里定义 allowed_model_names ======
        allowed_model_names = set(comp.get('entity_name') for comp in all_components)

        # 创建令牌池管理器
        token_pool = TokenPoolManager(initial_tokens=3, min_tokens=1, max_tokens=8)

        # 存储结果
        success_relations = []
        failed_apis = []

        # 定义单个API处理函数
        async def process_single_api(api_id: int):
            start_time = time.time()
            try:
                await token_pool.acquire_token()
                await task_api_model.update_task_api_status_by_api_id(task_id, api_id, status=2)  # 2=执行中
                try:
                    nodes = await neo4j_model.find_by_property("id", api_id)
                    if not isinstance(nodes, list) or len(nodes) == 0:
                        logger.warning(f"未在 Neo4j 中找到 ID 为 {api_id} 的 API 节点")
                        failed_apis.append({"api_id": api_id, "error": "节点不存在"})
                        return

                    api_info = nodes[0]
                    api_name = json_utils.safe_str(json_utils.safe_get(api_info, "name"))

                    # 从 api_name 中提取 method 和 path
                    if " " in api_name:
                        api_method, api_path = api_name.split(" ", 1)
                    else:
                        api_method = "UNKNOWN"
                        api_path = api_name

                    cleaned_api_info = json_utils.clean_example_field(api_info)
                    api_doc_str = json.dumps(cleaned_api_info, ensure_ascii=False, separators=(',', ':')).replace('\n', '').replace('\r', '')

                    component_docs = [json.dumps(comp, ensure_ascii=False, separators=(',', ':')) for comp in all_components]
                    component_doc_str = '[' + ''.join(component_docs) + ']'

                    # 调用大语言模型提取关系
                    extractor = ApiComponentRelationExtractor(
                        api_doc=api_doc_str,
                        component_doc=component_doc_str,
                        use_deep_thought=True
                    )
                    external_result = await asyncio.to_thread(extractor.extract_relationships)

                    # 处理提取结果
                    api_relations = []
                    for item in external_result.get("relationships", []):
                        relation_type = item.get("relationship")
                        if relation_type == "BY":
                            relation_type = f"{api_method}_BY"
                        matched_fields = item.get("parameters")
                        data_model = item.get("model_name")

                        # ====== 过滤：只保留真实存在的模型名 ======
                        if data_model not in allowed_model_names:
                            continue  # 跳过虚假的模型名

                        relation_id = await api_relations_model.create_relation(
                            relation_uid=relation_uid,
                            api_name=api_name,
                            api_type="API",
                            data_model=data_model,
                            relation_type=relation_type,
                            task_id=task_id,
                            details={
                                "matched_fields": matched_fields,
                                "match_type": relation_type,
                                "timestamp": datetime.now().isoformat()
                            },
                            status=0,
                            api_method=api_method,
                            api_path=api_path
                        )

                        api_relations.append({
                            "relation_id": relation_id,
                            "api_name": api_name,
                            "data_model": data_model,
                            "relation_type": relation_type,
                            "matched_fields": matched_fields
                        })

                    await task_api_model.update_task_api_status_by_api_id(task_id, api_id, status=1)  # 1=已完成
                    success_relations.extend(api_relations)

                    processing_time = time.time() - start_time
                    token_pool.record_processing_time(processing_time)
                    logger.info(f"API {api_id} 处理完成，耗时: {processing_time:.2f}秒，创建了 {len(api_relations)} 条关系")

                except Exception as e:
                    await task_api_model.update_task_api_status_by_api_id(task_id, api_id, status=3)  # 3=失败
                    failed_apis.append({"api_id": api_id, "error": str(e)})
                    logger.error(f"处理 API {api_id} 失败: {str(e)}")

            except Exception as e:
                failed_apis.append({"api_id": api_id, "error": str(e)})
                logger.error(f"处理 API {api_id} 时发生异常: {str(e)}")
            finally:
                token_pool.release_token()
                token_pool.adjust_tokens()

        # 并发执行所有API
        tasks = [process_single_api(api_id) for api_id in api_ids]
        await asyncio.gather(*tasks, return_exceptions=True)

    except Exception as e:
        logger.error(f"增量提取关系失败: {str(e)}")
        if task_id:
            try:
                await task_api_model.update_task_api_status(task_id, 2)
            except:
                pass
    finally:
        await task_api_model.close()
        await map_doc_api_model.close()
        await component_model.close()
        await api_relations_model.close()
        await neo4j_model.close()
@router.get("/task_progress", response_model=BaseResponse)
async def get_task_progress(
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    获取指定知识库下的所有任务进度信息

    Args:
        relation_uid (str): 知识库唯一标识符（UUID），用于筛选所属任务

    Returns:
        BaseResponse: 响应对象，包含以下字段：
            - code (int): 状态码（200 成功，500 失败）
            - message (str): 操作结果描述
            - data (List[Dict]): 任务进度列表，每项包含：
                - task_id (str): 任务ID
                - completion_rate (float): 完成百分比
                - total (int): 总任务数
                - completed (int): 已完成数量
    """
    task_api_model = None
    try:
        task_api_model = MapTaskApiModel()
        await task_api_model.initialize()
        # 查询该知识库下的所有任务记录
        tasks = await task_api_model.get_task_apis_by_kb_id(relation_uid)

        task_map = {}

        for task in tasks:
            task_id = task["task_id"]
            status = task["status"]

            if task_id not in task_map:
                task_map[task_id] = []

            task_map[task_id].append(status)

        result = []
        for task_id, statuses in task_map.items():
            total = len(statuses)
            completed = sum(1 for s in statuses if s == 1)  # 只有状态为1才算完成
            completion_rate = round((completed / total) * 100) if total > 0 else 0

            result.append({
                "task_id": task_id,
                "completion_rate": completion_rate,
                "total": total,
                "completed": completed,
            })

        return BaseResponse(
            code=200,
            message="获取任务进度信息成功",
            data=result if tasks else {}
        )

    except Exception as e:
        logger.error(f"获取任务进度信息失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"获取任务进度信息失败: {str(e)}",
            data={}
        )
    finally:
        await task_api_model.close()
@router.get("/confirm_relations", response_model=BaseResponseWithModel)
async def confirm_relations(
    relation_uid: str = Path(..., description="知识库ID"),
    task_id: str = Query(None, description="任务ID（可选）")
):
    """
    关系确认查询接口：
    - 不传参：返回指定知识库的所有关系记录
    - 传参（task_id）：返回该任务相关的记录

    Returns:
        BaseResponse: 响应对象，包含关系数据列表，并新增 api_summary 和 tags 字段
    """
    try:
        # 初始化模型
        api_relations_model = ApiRelationsModel()
        await api_relations_model.initialize()

        map_doc_api_model = MapDocApiModel()
        await map_doc_api_model.initialize()

        component_model = MapDocComponentsModel()
        await component_model.initialize()

        try:
            # 查询数据
            if task_id:
                relations = await api_relations_model.get_relations_by_task_id(task_id, relation_uid)
                message = f"成功获取任务 {task_id} 的关系记录"
            else:
                relations = await api_relations_model.get_all_relations(relation_uid)
                message = "成功获取所有关系记录"

            if not relations:
                await api_relations_model.close()
                await component_model.close()
                await map_doc_api_model.close()
                return BaseResponse(
                    code=200,
                    message="未找到相关关系记录",
                    data={
                        "relations": []
                    }
                )

            # 补充 api_summary 和 tags 字段
            extended_relations = []
            for relation in relations:
                api_name = relation.get("api_name")

                # 从 map_doc_api 表中查找对应的 summary 和 tags
                api_info = await map_doc_api_model.get_api_record_by_name(api_name)

                summary = ""
                tags = []

                if api_info:
                    summary = api_info.get("summary", "")
                    tags = api_info.get("tags", [])

                # 扩展关系数据
                extended_relation = {
                    **relation,
                    "api_summary": summary,
                    "api_tags": tags
                }
                extended_relations.append(extended_relation)

            # 提取组件间的关系
            model_data = []
            if relation_uid:
                model_data = await component_model.extract_component_relationships(kb_uid=relation_uid)

            return BaseResponseWithModel(
                code=200,
                message=message,
                data=extended_relations,
                component=model_data
            )

        finally:
            await api_relations_model.close()
            await map_doc_api_model.close()
            await component_model.close()

    except Exception as e:
        logger.error(f"查询关系记录失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"查询关系记录失败: {str(e)}",
            data={}
        )

@router.post("/confirm_and_insert_neo4j", response_model=BaseResponse)
async def confirm_and_insert_neo4j(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="请求数据，包含 ids 列表")
):
    success_ids = []
    failed_ids = []

    api_relations_model = ApiRelationsModel()
    neo4j_model = Neo4jModel(labels=[relation_uid], uid=relation_uid)

    try:
        # 提取请求参数
        ids = data.get("ids")
        if not ids or not isinstance(ids, list):
            return BaseResponse(
                code=400,
                message="缺少有效的 relation_id 列表",
                data={}
            )

        await api_relations_model.initialize()
        await neo4j_model.initialize()

        for id in ids:
            relation_info = await api_relations_model.get_relation_by_id(id)
            if not relation_info:
                failed_ids.append(id)
                continue

            relation_type = relation_info.get("relation_type")
            api_name = relation_info.get("api_name")
            data_model = relation_info.get("data_model")

            if not api_name or not data_model:
                failed_ids.append(id)
                continue

            # 确定方向
            if relation_type.endswith("_BY"):
                source_name, target_name = api_name, data_model
            elif relation_type == "QUTO":
                source_name, target_name = data_model, api_name
            else:
                logger.warning(f"不支持的关系类型: {relation_type}")
                failed_ids.append(id)
                continue

            # 查找源节点和目标节点
            source_nodes = await neo4j_model.find_by_property("name", source_name)
            target_nodes = await neo4j_model.find_by_property("name", target_name)

            # 如果源节点不存在，自动创建
            if not source_nodes:
                logger.info(f"未找到源节点: {source_name}，正在创建新节点...")
                source_node_data = {
                    "id": str(uuid.uuid4()),
                    "name": source_name,
                    "type": "Component",
                    "version": relation_info.get("version", "V1"),
                    "status": "dynamic",
                    "description": f"自动创建的组件节点: {source_name}"
                }
                created = await neo4j_model.create(source_node_data)
                if not created:
                    logger.error(f"无法创建源节点: {source_name}")
                    failed_ids.append(id)
                    continue
                source_nodes = await neo4j_model.find_by_property("name", source_name)
                if not source_nodes:
                    logger.error(f"创建源节点后仍未能找到节点: {source_name}")
                    failed_ids.append(id)
                    continue

            # 如果目标节点不存在，自动创建
            if not target_nodes:
                logger.info(f"未找到目标节点: {target_name}，正在创建新节点...")
                target_node_data = {
                    "id": str(uuid.uuid4()),
                    "name": target_name,
                    "type": "Component",
                    "version": relation_info.get("version", "V1"),
                    "status": "dynamic",
                    "description": f"自动创建的组件节点: {target_name}"
                }
                created = await neo4j_model.create(target_node_data)
                if not created:
                    logger.error(f"无法创建目标节点: {target_name}")
                    failed_ids.append(id)
                    continue
                target_nodes = await neo4j_model.find_by_property("name", target_name)
                if not target_nodes:
                    logger.error(f"创建目标节点后仍未能找到节点: {target_name}")
                    failed_ids.append(id)
                    continue

            # 获取 source 和 target 的 ID
            source_id = source_nodes[0]["id"]
            target_id = target_nodes[0]["id"]

            # 构建关系属性
            rel_properties = {
                "id": str(uuid.uuid4()),
                "description": relation_info.get("description"),
                "version": relation_info.get("version", "V1")
            }

            matched_fields = relation_info.get("details", {}).get("matched_fields", {})
            cleaned_matched_fields = {}

            for key, value in matched_fields.items():
                cleaned_key = json_utils.sanitize_property_key(key)
                cleaned_matched_fields[cleaned_key] = value

            rel_properties.update(cleaned_matched_fields)

            # 插入 Neo4j 关系
            created = await neo4j_model.create_relationship(
                source_id=source_id,
                target_id=target_id,
                relation_type=relation_type,
                properties=rel_properties
            )

            if created:
                updated = await api_relations_model.update_relation_status(id, 1)
                if updated:
                    success_ids.append(id)
                else:
                    failed_ids.append(id)
            else:
                failed_ids.append(id)

        message = f"成功创建 {len(success_ids)} 条关系"
        if failed_ids:
            message += f"，但有 {len(failed_ids)} 条记录未找到或插入失败"

        return BaseResponse(code=200, message=message, data={"success_ids": success_ids, "failed_ids": failed_ids})

    except Exception as e:
        logger.error(f"确认并插入图数据失败: {str(e)}")
        return BaseResponse(code=500, message=f"确认并插入图数据失败: {str(e)}", data={})
    finally:
        await api_relations_model.close()
        await neo4j_model.close()


@router.delete("/delete_relations", response_model=BaseResponse)
async def delete_relations(
    data: dict = Body(..., description="请求数据，包含 ids 列表")
):
    """
    批量删除 api_relations 表中的记录

    Args:
        data: 请求数据，包含：
            - ids: 要删除的关系ID列表（字符串数组）

    Returns:
        BaseResponse: 响应对象，包含：
            - code: 状态码
            - message: 操作结果描述
            - data: 附加数据（成功和失败ID）
    """
    try:
        # 提取请求参数
        relation_ids = data.get("ids")
        if not relation_ids or not isinstance(relation_ids, list):
            return BaseResponse(
                code=400,
                message="缺少有效的 relation_id 列表",
                data={}
            )

        # 初始化模型
        api_relations_model = ApiRelationsModel()
        await api_relations_model.initialize()

        try:
            success_ids = []
            failed_ids = []

            for relation_id in relation_ids:
                deleted = await api_relations_model.delete_relation_by_uid(relation_id)
                if deleted:
                    success_ids.append(relation_id)
                else:
                    failed_ids.append(relation_id)

            # 构建响应消息
            message = f"成功删除 {len(success_ids)} 条记录"
            if failed_ids:
                message += f"，但有 {len(failed_ids)} 条记录未找到或删除失败"

            return BaseResponse(
                code=200,
                message=message,
                data={
                    "success_ids": success_ids,
                    "failed_ids": failed_ids
                }
            )

        finally:
            await api_relations_model.close()

    except Exception as e:
        logger.error(f"删除关系记录失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"删除关系记录失败: {str(e)}",
            data={}
        )
@router.post("/ignore_relations", response_model=BaseResponse)
async def ignore_relations(
    data: dict = Body(..., description="请求数据，包含 ids 列表")
):
    """
    批量忽略 api_relations 表中的记录

    Args:
        data: 请求数据，包含：
            - ids: 要忽略的关系ID列表（字符串数组）

    Returns:
        BaseResponse: 响应对象，包含：
            - code: 状态码
            - message: 操作结果描述
            - data: 附加数据（成功和失败ID）
    """
    try:
        # 提取请求参数
        relation_ids = data.get("ids")
        if not relation_ids or not isinstance(relation_ids, list):
            return BaseResponse(
                code=400,
                message="缺少有效的 relation_id 列表",
                data={}
            )

        # 初始化模型
        api_relations_model = ApiRelationsModel()
        await api_relations_model.initialize()

        try:
            success_ids = []
            failed_ids = []

            for relation_id in relation_ids:
                updated = await api_relations_model.update_relation_status(relation_id, 2)  # 状态 2 表示忽略
                if updated:
                    success_ids.append(relation_id)
                else:
                    failed_ids.append(relation_id)

            # 构建响应消息
            message = f"成功忽略 {len(success_ids)} 条记录"
            if failed_ids:
                message += f"，但有 {len(failed_ids)} 条记录未找到或更新失败"

            return BaseResponse(
                code=200,
                message=message,
                data={
                    "success_ids": success_ids,
                    "failed_ids": failed_ids
                }
            )

        finally:
            await api_relations_model.close()

    except Exception as e:
        logger.error(f"忽略关系记录失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"忽略关系记录失败: {str(e)}",
            data={}
        )

@router.delete("/delete_data_models", response_model=BaseResponse)
async def delete_data_models(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="请求数据，包含 ids 列表")
):
    """
    批量删除 map_doc_component 表中的记录

    Args:
        relation_uid: 知识库ID
        data: 请求数据，包含：
            - ids: 要删除的组件ID列表（整数数组）

    Returns:
        BaseResponse: 响应对象，包含：
            - code: 状态码
            - message: 操作结果描述
            - data: 附加数据（成功和失败ID）
    """
    # 初始化模型
    component_model = MapDocComponentsModel()
    await component_model.initialize()
    try:
        # 提取请求参数
        component_ids = data.get("ids")
        if not component_ids or not isinstance(component_ids, list):
            return BaseResponse(
                code=400,
                message="缺少有效的组件ID列表",
                data={}
            )

        success_ids = []
        failed_ids = []

        for component_id in component_ids:
            deleted = await component_model.delete_component_mapping(int(component_id))
            if deleted:
                success_ids.append(component_id)
            else:
                failed_ids.append(component_id)

        # 构建响应消息
        message = f"成功删除 {len(success_ids)} 条记录"
        if failed_ids:
            message += f"，但有 {len(failed_ids)} 条记录未找到或删除失败"

        return BaseResponse(
            code=200,
            message=message,
            data={
                "success_ids": success_ids,
                "failed_ids": failed_ids
            }
        )

    except Exception as e:
        logger.error(f"删除组件记录失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"删除组件记录失败: {str(e)}",
            data={}
        )
    finally:
        await component_model.close()

@router.delete("/delete_api_entities", response_model=BaseResponse)
async def delete_api_entities(
    relation_uid: str = Path(..., description="知识库ID"),
    data: dict = Body(..., description="请求数据，包含 ids 列表")
):
    """
    批量删除 map_doc_api 表中的记录

    Args:
        relation_uid: 知识库ID
        data: 请求数据，包含：
            - id_list: 要删除的API ID列表（整数数组）

    Returns:
        BaseResponse: 响应对象，包含：
            - code: 状态码
            - message: 操作结果描述
            - data: 附加数据（成功和失败ID）
    """
    # 初始化模型
    api_model = MapDocApiModel()
    await api_model.initialize()
    try:
        # 提取请求参数
        api_ids = data.get("ids")
        if not api_ids or not isinstance(api_ids, list):
            return BaseResponse(
                code=400,
                message="缺少有效的API ID列表",
                data={}
            )

        success_ids = []
        failed_ids = []

        for api_id in api_ids:
            deleted = await api_model.delete_api_mapping(int(api_id))
            if deleted:
                success_ids.append(api_id)
            else:
                failed_ids.append(api_id)

        # 构建响应消息
        message = f"成功删除 {len(success_ids)} 条记录"
        if failed_ids:
            message += f"，但有 {len(failed_ids)} 条记录未找到或删除失败"

        return BaseResponse(
            code=200,
            message=message,
            data={
                "success_ids": success_ids,
                "failed_ids": failed_ids
            }
        )

    except Exception as e:
        logger.error(f"删除API记录失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"删除API记录失败: {str(e)}",
            data={}
        )
    finally:
        await api_model.close()

@router.delete("/delete_task", response_model=BaseResponse)
async def delete_task(
    task_id: str = Query(..., description="要删除的任务ID（字符串）"),
    relation_uid: str = Path(..., description="知识库ID")
):
    """
    删除 map_task_api 和 api_relations 表中与指定 task_id 相关的所有记录

    Args:
        task_id: 要删除的任务ID（字符串）
        relation_uid: 知识库ID（路径参数）

    Returns:
        BaseResponse: 响应对象，包含：
            - code: 状态码
            - message: 操作结果描述
            - data: 附加数据（成功和失败信息）
    """
    # 初始化模型
    task_api_model = MapTaskApiModel()
    relations_model = ApiRelationsModel()
    await task_api_model.initialize()
    await relations_model.initialize()
    try:
        if not task_id or not isinstance(task_id, str):
            return BaseResponse(
                code=400,
                message="缺少有效的 task_id",
                data={}
            )
        # 1. 删除 map_task_api 表中与 task_id 相关的记录
        deleted_from_map_task = await task_api_model.delete_task_api_by_task_id(task_id)
        # 2. 删除 api_relations 表中与 task_id 相关的记录
        deleted_from_api_relations = await relations_model.delete_relations_by_task_id(task_id)
        # 判断是否至少删除了一条记录
        if deleted_from_map_task or deleted_from_api_relations:
            return BaseResponse(
                code=200,
                message=f"成功删除与 task_id={task_id} 相关的所有记录",
                data={"deleted_task_id": task_id}
            )
        else:
            return BaseResponse(
                code=404,
                message=f"未找到与 task_id={task_id} 相关的记录",
                data={}
            )

    except Exception as e:
        logger.error(f"删除任务记录失败: {str(e)}")
        return BaseResponse(
            code=500,
            message=f"删除任务记录失败: {str(e)}",
            data={}
        )
    finally:
        await task_api_model.close()
        await relations_model.close()
@router.get("/get_entiy_list", response_model=BaseResponse)
async def get_entiy_list(
    relation_uid: str = Path(..., description="知识库ID"),
    # document_name: str = Query(..., description="文档名称")
    document_name: str = Query(None, description="文档名称（可选）")
):

    """
    获取指定知识库下指定文档的所有实体详细信息
    """
    try:
        map_doc_model = MapDocApiModel()
        await map_doc_model.initialize()
        try:
            if document_name:
                entity_list = await map_doc_model.get_api_mappings_by_kb_uid_and_doc_name(relation_uid, document_name)
            else:
                entity_list = await map_doc_model.get_api_mappings_by_kb_uid_all(relation_uid)
            if entity_list is None:
                entity_list = []

            if not isinstance(entity_list, list):
                logger.error(f"entity_list is not a list, actual type: {type(entity_list)}")
                entity_list = []

            # 新增字段 parameters_list 和 request_body_list
            for entity in entity_list:
                # 提取 parameters.name -> parameters_list
                parameters = entity.get("parameters") or []
                if not isinstance(parameters, list):
                    logger.error(f"parameters is not a list, actual type: {type(parameters)}")
                    parameters = []
                name_list = [param.get("name") for param in parameters if param.get("name")]
                entity["parameters_list"] = "、".join(name_list) if name_list else ""

                # 提取 request_body.example 的 keys -> request_body_list
                request_body = entity.get("request_body", {})

                example_str = json_utils.safe_get(request_body, "content", "application/json", "example")

                try:
                    if isinstance(example_str, str) and example_str.strip():
                        example_dict = json.loads(example_str)
                        keys = list(example_dict.keys())
                        entity["request_body_list"] = "、".join(keys) if keys else ""
                    else:
                        entity["request_body_list"] = ""
                except Exception as e:
                    logger.warning(f"解析 request_body.example 失败: {str(e)}")
                    entity["request_body_list"] = ""

                # 新增：提取 responses 中 200 或 default 的 example 的 keys -> responses_list
                responses = entity.get("responses", {})
                example_data = None

                if "200" in responses:
                    example_data = responses["200"].get("content", {}).get("application/json", {}).get("example", "")
                elif "default" in responses:
                    example_data = responses["default"].get("content", {}).get("application/json", {}).get("example", "")

                try:
                    if isinstance(example_data, str) and example_data.strip():
                        example_dict = json.loads(example_data)
                        keys = list(example_dict.keys())
                        entity["responses_list"] = "、".join(keys) if keys else ""
                    else:
                        entity["responses_list"] = ""
                except Exception as e:
                    logger.warning(f"解析 responses.example 失败: {str(e)}")
                    entity["responses_list"] = ""

            return BaseResponse(
                code=200,
                message="获取实体列表成功",
                data=entity_list
            )
        finally:
            await map_doc_model.close()

    except Exception as e:
        logger.error(f"获取实体列表失败: {str(e)}")
        return BaseResponse(
            code=500,
            message="获取实体列表失败",
            data={}
        )


# ==================== 提示词管理接口 ====================
@router.post("/prompt_management_save")
async def prompt_management_save(
    relation_uid: str = Path(..., description="知识库ID"),
    request: PromptManagementRequest = Body(..., description="提示词管理配置数据")
):
    """保存提示词管理配置"""
    prompt_model = PromptManagementModel()
    try:
        logger.info(f"开始保存提示词配置，knowledge_base_id: {relation_uid}")
        await prompt_model.initialize()

        # 准备数据
        data = {
            'business_introduction': request.businessIntroduction,
            'term_definitions': [term.dict() for term in request.termDefinitions],
            'business_prompts': [prompt.dict() for prompt in request.businessPrompts],
            'extra_evidence': [evidence.dict() for evidence in request.extraEvidence]
        }

        logger.info(f"保存提示词配置数据: {data}")

        # 使用upsert方法，如果存在则更新，不存在则插入
        result = await prompt_model.upsert_by_knowledge_base_id(relation_uid, data)
        logger.info(f"数据库操作结果: {result}")

        if result > 0:
            logger.info(f"提示词配置保存成功，knowledge_base_id: {relation_uid}")
            return BaseResponse(
                code=200,
                message="提示词配置保存成功",
                data={"saved": True, "knowledge_base_id": relation_uid}
            )
        else:
            logger.error(f"数据库操作返回结果为0，保存失败，knowledge_base_id: {relation_uid}")
            return BaseResponse(
                code=500,
                message="保存失败，数据库操作未成功",
                data={}
            )

    except Exception as e:
        logger.error(f"保存提示词配置失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return BaseResponse(
            code=500,
            message=f"保存失败: {str(e)}",
            data={}
        )
    finally:
        await prompt_model.close()


@router.get("/prompt_management_load")
async def prompt_management_load(
    relation_uid: str = Path(..., description="知识库ID")
):
    """加载提示词管理配置"""
    prompt_model = PromptManagementModel()
    try:
        logger.info(f"开始加载提示词配置，knowledge_base_id: {relation_uid}")
        await prompt_model.initialize()

        # 从数据库加载配置
        config = await prompt_model.find_by_knowledge_base_id(relation_uid)
        logger.info(f"数据库查询结果: {config}")

        if config:
            # 转换数据格式以匹配前端期望的格式
            response_data = {
                "businessIntroduction": config.get('business_introduction', ''),
                "termDefinitions": config.get('term_definitions', []),
                "businessPrompts": config.get('business_prompts', []),
                "extraEvidence": config.get('extra_evidence', [])
            }

            logger.info(f"加载提示词配置成功: {relation_uid}")
        else:
            # 如果没有找到配置，返回默认的空配置
            response_data = {
                "businessIntroduction": "",
                "termDefinitions": [
                    {"name": "", "definition": ""}
                ],
                "businessPrompts": [
                    {"content": ""}
                ],
                "extraEvidence": [
                    {"name": "", "description": ""}
                ]
            }
            logger.info(f"未找到提示词配置，返回默认配置: {relation_uid}")

        return BaseResponse(
            code=200,
            message="加载提示词配置成功",
            data=response_data
        )
    except Exception as e:
        logger.error(f"加载提示词配置失败: {str(e)}")
        import traceback
        logger.error(f"详细错误信息: {traceback.format_exc()}")
        return BaseResponse(
            code=500,
            message=f"加载失败: {str(e)}",
            data={}
        )
    finally:
        await prompt_model.close()
