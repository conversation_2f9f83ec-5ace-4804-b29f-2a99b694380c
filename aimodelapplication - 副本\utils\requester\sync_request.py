import httpx


class SyncRequester:
    def __init__(self, base_url=None):
        self.base_url = base_url
        self.client = httpx.Client()

    def get(self, url, params=None, headers=None):
        """
        发送同步 GET 请求
        :param url: 请求的 URL
        :param params: 查询参数
        :param headers: 请求头
        :return: 响应对象
        """
        if self.base_url:
            url = f"{self.base_url}{url}"
        response = self.client.get(url, params=params, headers=headers)
        return response

    def post(self, url, data=None, json=None, headers=None):
        """
        发送同步 POST 请求
        :param url: 请求的 URL
        :param data: 表单数据
        :param json: JSON 数据
        :param headers: 请求头
        :return: 响应对象
        """
        if self.base_url:
            url = f"{self.base_url}{url}"
        response = self.client.post(url, data=data, json=json, headers=headers)
        return response

    def put(self, url, data=None, json=None, headers=None):
        """
        发送同步 PUT 请求
        :param url: 请求的 URL
        :param data: 表单数据
        :param json: JSON 数据
        :param headers: 请求头
        :return: 响应对象
        """
        if self.base_url:
            url = f"{self.base_url}{url}"
        response = self.client.put(url, data=data, json=json, headers=headers)
        return response

    def delete(self, url, params=None, headers=None):
        """
        发送同步 DELETE 请求
        :param url: 请求的 URL
        :param params: 查询参数
        :param headers: 请求头
        :return: 响应对象
        """
        if self.base_url:
            url = f"{self.base_url}{url}"
        response = self.client.delete(url, params=params, headers=headers)
        return response

    def patch(self, url, data=None, json=None, headers=None):
        """
        发送同步 PATCH 请求
        :param url: 请求的 URL
        :param data: 表单数据
        :param json: JSON 数据
        :param headers: 请求头
        :return: 响应对象
        """
        if self.base_url:
            url = f"{self.base_url}{url}"
        response = self.client.patch(url, data=data, json=json, headers=headers)
        return response

    def close(self):
        """
        关闭同步客户端
        """
        self.client.close()
