#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试提示词管理数据库操作
"""

import asyncio
import sys
import os
sys.path.append(os.path.dirname(__file__))

from db.mysql.models.prompt_management import PromptManagementModel

async def test_prompt_management():
    """测试提示词管理数据库操作"""
    model = PromptManagementModel()
    
    try:
        # 初始化
        await model.initialize()
        print("数据库连接初始化成功")
        
        # 测试数据
        test_data = {
            'business_introduction': '当前系统为堡垒机系统',
            'term_definitions': [
                {'name': '资产', 'definition': '本地添加的资产，包括主机数据库等'},
                {'name': '账号', 'definition': '创建资产时同步创建或者绑定资产创建'}
            ],
            'business_prompts': [
                {'content': '授权是对用户进行（资产，资产账号等）授权'},
                {'content': '账号是关联在资产下，创建资产可以同时创建账号，也可以创建资产后再创建账号'}
            ],
            'extra_evidence': [],
            'updated_by': 'test_user'
        }
        
        knowledge_base_id = "test_kb_001"
        
        print(f"准备保存数据: {test_data}")
        
        # 测试插入或更新
        result = await model.upsert_by_knowledge_base_id(knowledge_base_id, test_data)
        print(f"保存结果: {result}")
        
        if result > 0:
            print("保存成功")
            
            # 测试查询
            saved_data = await model.find_by_knowledge_base_id(knowledge_base_id)
            print(f"查询结果: {saved_data}")
            
            if saved_data:
                print("查询成功，数据存在")
                print(f"业务介绍: {saved_data.get('business_introduction')}")
                print(f"名词定义: {saved_data.get('term_definitions')}")
                print(f"业务提示: {saved_data.get('business_prompts')}")
                print(f"额外依据: {saved_data.get('extra_evidence')}")
            else:
                print("查询失败，数据不存在")
        else:
            print("保存失败")
            
        # 测试查询所有数据
        all_data = await model.find_all()
        print(f"所有数据数量: {len(all_data)}")
        for item in all_data:
            print(f"  - ID: {item.get('id')}, 知识库ID: {item.get('knowledge_base_id')}")
            
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        await model.close()
        print("数据库连接已关闭")

if __name__ == "__main__":
    print("开始测试提示词管理数据库操作...")
    asyncio.run(test_prompt_management())
    print("测试完成")
