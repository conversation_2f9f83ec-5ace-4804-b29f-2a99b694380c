import asyncio
import time
from logger import logging
logger = logging()

# 添加令牌池管理器类
class TokenPoolManager:
    def __init__(self, initial_tokens: int = 3, min_tokens: int = 1, max_tokens: int = 10):
        self.semaphore = asyncio.Semaphore(initial_tokens)
        self.current_tokens = initial_tokens
        self.min_tokens = min_tokens
        self.max_tokens = max_tokens
        self.processing_times = []
        self.last_adjustment_time = time.time()

    async def acquire_token(self):
        await self.semaphore.acquire()

    def release_token(self):
        self.semaphore.release()

    def record_processing_time(self, processing_time: float):
        """记录处理时间用于动态调整"""
        self.processing_times.append(processing_time)
        # 只保留最近20次的记录
        if len(self.processing_times) > 20:
            self.processing_times.pop(0)

    def adjust_tokens(self):
        """根据处理时间动态调整令牌数量"""
        current_time = time.time()
        # 每30秒调整一次
        if current_time - self.last_adjustment_time < 30:
            return

        if len(self.processing_times) < 5:
            return

        avg_time = sum(self.processing_times) / len(self.processing_times)

        # 如果平均处理时间小于5秒，增加令牌
        if avg_time < 5 and self.current_tokens < self.max_tokens:
            self.current_tokens += 1
            self.semaphore = asyncio.Semaphore(self.current_tokens)
            logger.info(f"增加令牌数量到 {self.current_tokens}")
        # 如果平均处理时间大于15秒，减少令牌
        elif avg_time > 15 and self.current_tokens > self.min_tokens:
            self.current_tokens -= 1
            self.semaphore = asyncio.Semaphore(self.current_tokens)
            logger.info(f"减少令牌数量到 {self.current_tokens}")

        self.last_adjustment_time = current_time

