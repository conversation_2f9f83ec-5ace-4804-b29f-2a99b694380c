
import json as json_module
import os

from logger import logging
from service.dify.dify_api_manage import CONSOLE_API_LOGIN
from template.dify_temp import AccessTokenPayload, APIKeyAccessTokenPayload
from db.mysql.models.knowledge_dify_mapping import KnowledgeDifyMappingModel
from utils.requester.async_request import AsyncRequester
from utils.requester.sync_request import SyncRequester
from dotenv import load_dotenv

class DifyRequester:

    def __init__(self, payload: AccessTokenPayload):
        """只接受AccessTokenPayload格式的入参"""
        load_dotenv()
        self.base_url = os.getenv("DIFY_HOST")
        self.payload = json_module.loads(payload.model_dump_json())  # 使用重命名后的模块
        self.sync_request = SyncRequester(self.base_url)
        self.async_request = AsyncRequester(self.base_url)
        self._headers = {"Authorization" : f"Bearer {self._access_token()}"}

    def _access_token(self):
        get_response = self.sync_request.post(CONSOLE_API_LOGIN, json=self.payload)
        if get_response.status_code == 200:
            return get_response.json().get("data").get("access_token")
        else:
            logging().error(f"【鉴权失败】 dify用户： {self.payload.get('email')}")
            return None

    def account_active(self):
        """检查用户是否已经激活"""
        get_response = self.sync_request.post(CONSOLE_API_LOGIN, json=self.payload)
        return get_response.status_code

    def sync_get(self, url, params=None):
        """同步get请求"""
        res = self.sync_request.get(url, params=params, headers=self._headers)
        return res

    def sync_post(self, url, data=None, json=None):
        """同步post请求"""
        res = self.sync_request.post(url, data=data, json=json, headers=self._headers)
        return res

    def sync_put(self, url, data=None, json=None):
        """同步put请求"""
        res = self.sync_request.put(url, data=data, json=json, headers=self._headers)
        return res

    def sync_patch(self, url, data=None, json=None):
        """同步patch请求"""
        res = self.sync_request.patch(url, data=data, json=json, headers=self._headers)
        return res

    def sync_delete(self, url, params=None):
        """同步delete请求"""
        res = self.sync_request.delete(url, params=params, headers=self._headers)
        return res

    async def async_get(self, url, params=None):
        """异步get请求"""
        async with self.async_request:
            return await self.async_request.get(url, params=params, headers=self._headers)

    async def async_post(self, url, data=None, json=None, files=None):
        """异步post请求"""
        async with self.async_request:
            return await self.async_request.post(url, data=data, json=json, files=files, headers=self._headers)

    async def async_put(self, url, data=None, json=None):
        """异步put请求"""
        async with self.async_request:
            return await self.async_request.put(url, data=data, json=json, headers=self._headers)

    async def async_patch(self, url, data=None, json=None):
        """异步patch请求"""
        async with self.async_request:
            return await self.async_request.patch(url, data=data, json=json, headers=self._headers)

    async def async_delete(self, url, params=None):
        """异步delete请求"""
        async with self.async_request:
            return await self.async_request.delete(url, params=params, headers=self._headers)

class DifyAPIKeyRequester:
    """使用email和password进行认证的Dify请求器"""

    def __init__(self, payload: APIKeyAccessTokenPayload):
        """只接受AccessTokenPayload格式的入参"""
        load_dotenv()
        self.base_url = os.getenv("DIFY_HOST")
        self.payload = json_module.loads(payload.model_dump_json())
        self.sync_request = SyncRequester(self.base_url)
        self.async_request = AsyncRequester(self.base_url)
        self._headers = None  # 初始化时先不设置headers

    async def _access_token(self):
        """通过查询数据库获取API密钥"""
        email = self.payload.get('email')
        username = self.payload.get('username')

        if not email or not username:
            logging().error("【认证失败】 email或username为空")
            return None

        try:
            # 创建知识库映射模型实例
            kb_model = KnowledgeDifyMappingModel()
            await kb_model.initialize()

            # 查询API密钥
            records = await kb_model.find_by_dify_email_and_username(email, username)

            if records and len(records) > 0:
                api_key = records[0].get('dify_api_key')
                if api_key:
                    return api_key
                else:
                    logging().error(f"【认证失败】 未找到API密钥: email={email}, username={username}")
                    return None
            else:
                logging().error(f"【认证失败】 未找到用户记录: email={email}, username={username}")
                return None

        except Exception as e:
            logging().error(f"【认证失败】 查询API密钥时发生错误: {str(e)}")
            return None

        finally:
            # 确保关闭数据库连接
            if 'kb_model' in locals():
                await kb_model.close()

    async def _get_headers(self):
        """获取认证头"""
        if not self._headers:
            api_key = await self._access_token()
            if api_key:
                self._headers = {"Authorization": f"Bearer {api_key}"}
            else:
                self._headers = {}
        return self._headers

    def sync_get(self, url, params=None):
        """同步get请求"""
        res = self.sync_request.get(url, params=params, headers=self._headers)
        return res

    def sync_post(self, url, data=None, json=None):
        """同步post请求"""
        res = self.sync_request.post(url, data=data, json=json, headers=self._headers)
        return res

    def sync_put(self, url, data=None, json=None):
        """同步put请求"""
        res = self.sync_request.put(url, data=data, json=json, headers=self._headers)
        return res

    def sync_patch(self, url, data=None, json=None):
        """同步patch请求"""
        res = self.sync_request.patch(url, data=data, json=json, headers=self._headers)
        return res

    def sync_delete(self, url, params=None):
        """同步delete请求"""
        res = self.sync_request.delete(url, params=params, headers=self._headers)
        return res

    async def async_get(self, url, params=None):
        """异步get请求"""
        headers = await self._get_headers()
        async with self.async_request:
            return await self.async_request.get(url, params=params, headers=headers)

    async def async_post(self, url, data=None, json=None):
        """异步post请求"""
        headers = await self._get_headers()
        async with self.async_request:
            return await self.async_request.post(url, data=data, json=json, headers=headers)

    async def async_put(self, url, data=None, json=None):
        """异步put请求"""
        headers = await self._get_headers()
        async with self.async_request:
            return await self.async_request.put(url, data=data, json=json, headers=headers)

    async def async_patch(self, url, data=None, json=None):
        """异步patch请求"""
        headers = await self._get_headers()
        async with self.async_request:
            return await self.async_request.patch(url, data=data, json=json, headers=headers)

    async def async_delete(self, url, params=None):
        """异步delete请求"""
        headers = await self._get_headers()
        async with self.async_request:
            return await self.async_request.delete(url, params=params, headers=headers)