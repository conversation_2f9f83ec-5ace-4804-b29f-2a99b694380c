import asyncio
import os
from typing import Tuple, Dict, Any, List
import json

import service.dify.dify_api_manage as api
from fastapi import FastAP<PERSON>
from logger import logging

from config import DIFY_ADMINISTRATOR
from template.dify_temp import AccessTokenPayload, APIKeyAccessTokenPayload
from template.response_temp import BaseResponse
from utils.requester.dify_requester import Dify<PERSON>equester, DifyAPIKeyRequester

app = FastAPI()
logger = logging()

class DifyAPIKey:
    """Dify调用API-KEY的API客户端类"""

    def __init__(self, email=None, username=None):
        payload = APIKeyAccessTokenPayload(email=email, username=username)
        self.requester = DifyAPIKeyRequester(payload)

    async def retrieve(self, knowledge_uid: str, query: str, retrieval_model: dict[str, Any]):
        """
        检索知识库
        Args:
        knowledge_uid:知识库ID
        query: 检索关键词
        retrieval_model：检索参数（选填，如不填，按照默认方式召回）
        Returns:
        """
        try:
            # 将knowledge_uid替换到API路径中
            api_path = api.RETRIEVE.replace("{knowledge_uid}", knowledge_uid)
            response = await self.requester.async_post(api_path, json={"query": query, "retrieval_model": retrieval_model})

            if response.status_code == 200:
                return 200, response.json()
            else:
                return response.status_code, response.json()

        except Exception as e:
            logger.error(f"查询知识库文档失败: {str(e)}")
            return 500, {"error": str(e)}

    async def delete_database_document(self, knowledge_uid: str, document_id: str) -> Tuple[int, Any]:
        """
        删除知识库文档
        Args:
            knowledge_uid: 知识库ID
            document_id: 文档ID

        Returns:
            Tuple[int, Any]: (状态码, 响应数据)
        """
        try:
            # 将knowledge_uid替换到API路径中
            api_path = api.DELETE_KNOWLEDGE_BASE_DOC.replace("{knowledge_uid}", knowledge_uid).replace("{document_id}", document_id)
            response = await self.requester.async_delete(api_path)

            data = response.json()
            return response.status_code, data

        except Exception as e:
            logger.error(f"删除知识库文档失败1: {str(e)}")
            return 500, {"error": str(e)}

    async def get_database_doc(self, knowledge_uid: str) -> Tuple[int, Any]:
        """
        查询知识库文档信息
        
        Args:
            knowledge_uid: 知识库ID
            
        Returns:
            Tuple[int, Any]: (状态码, 响应数据)
        """
        try:
            # 将knowledge_uid替换到API路径中
            api_path = api.GET_KNOWLEDGE_BASE_DOC.replace("{knowledge_uid}", knowledge_uid)
            response = await self.requester.async_get(api_path)
            
            if response.status_code == 200:
                return 200, response.json()
            else:
                return response.status_code, response.json()
                
        except Exception as e:
            logger.error(f"查询知识库文档失败: {str(e)}")
            return 500, {"error": str(e)}

    async def get_database_segments(self, knowledge_uid: str, document_id: str):
        """
        查询知识库文档分片
        Args:
            knowledge_uid: 知识库ID
            document_id: 文档ID

        Returns:
            Tuple[int, Any]: (状态码, 响应数据)
        """
        try:
            api_path = api.GET_KNOWLEDGE_BASE_DOC_SEGMENTS.replace("{knowledge_uid}", knowledge_uid).replace("{document_id}", document_id)
            response = await self.requester.async_get(api_path)

            if response.status_code == 200:
                return 200, response.json()
            else:
                return response.status_code, response.json()

        except Exception as e:
            logger.error(f"查询知识库文档分片失败: {str(e)}")
            return 500, {"error": str(e)}

class Dify:
    """Dify API客户端类"""

    def __init__(self, email=None, password=None):
        payload = AccessTokenPayload(email=email, password=password)
        self.requester = DifyRequester(payload)

    async def add_doc_segments(
        self,
        knowledge_uid: str,
        document_id: str,
        content: str,
        keywords: List[str]
    ) -> Tuple[int, Dict[str, Any]]:
        """
        新增文档分段
        
        Args:
            knowledge_uid: 知识库ID
            document_id: 文档ID
            content: 分段内容
            keywords: 关键词列表
            
        Returns:
            Tuple[int, Dict[str, Any]]: (状态码, 响应数据)
        """
        try:
            # 构建API路径
            api_path = api.ADD_KNOWLEDGE_BASE_DOC_SEGMENTS.format(
                knowledge_uid=knowledge_uid,
                document_id=document_id
            )
            
            # 构建请求数据
            data = {
                "content": content,
                "keywords": keywords
            }
            
            # 发送POST请求
            response = await self.requester.async_post(
                url=api_path,
                json=data
            )
            
            # 解析响应
            try:
                response_data = json.loads(response.text)
            except json.JSONDecodeError:
                response_data = response.text
                
            # 返回状态码和响应数据
            return response.status_code, response_data
            
        except Exception as e:
            logger.error(f"新增文档分段失败: {str(e)}")
            return 500, {"error": str(e)}

    async def edit_doc_segments(
        self,
        knowledge_uid: str,
        document_id: str,
        segment_id: str,
        content: str,
        keywords: List[str]
    ) -> Tuple[int, Dict[str, Any]]:
        """
        编辑文档分段
        
        Args:
            knowledge_uid: 知识库ID
            document_id: 文档ID
            segment_id: 分段ID
            content: 分段内容
            keywords: 关键词列表
            
        Returns:
            Tuple[int, Dict[str, Any]]: (状态码, 响应数据)
        """
        try:
            # 构建API路径
            api_path = api.EDIT_KNOWLEDGE_BASE_DOC_SEGMENTS.format(
                knowledge_uid=knowledge_uid,
                document_id=document_id,
                segment_id=segment_id
            )
            
            # 构建请求数据
            data = {
                "content": content,
                "keywords": keywords
            }

            # 发送PATCH请求
            response = await self.requester.async_patch(
                url=api_path,
                json=data
            )
            
            # 解析响应
            try:
                response_data = json.loads(response.text)
            except json.JSONDecodeError:
                response_data = response.text
                
            # 返回状态码和响应数据
            return response.status_code, response_data
            
        except Exception as e:
            logger.error(f"编辑文档分段失败1: {str(e)}")
            return 500, {"error": str(e)}

    async def delete_doc_segments(self, knowledge_uid: str, document_id: str, segment_ids: list[str]) -> Tuple[int, Any]:
        """
        批量删除文档分段
        Args:
            knowledge_uid: 知识库ID
            document_id: 文档ID
            segment_ids: 分段ID列表
            
        Returns:
            Tuple[int, Any]: (状态码, 响应数据)
        """
        try:
            # 构建基础API路径
            api_path = api.DELETE_KNOWLEDGE_BASE_DOC_SEGMENTS.replace("{knowledge_uid}", knowledge_uid).replace("{document_id}", document_id)
            
            # 将segment_ids拼接成URL参数
            params = "&".join([f"segment_id={seg_id}" for seg_id in segment_ids])
            api_path = f"{api_path}?{params}"
            
            response = await self.requester.async_delete(api_path)
            
            # 检查响应状态码
            if response.status_code == 204:
                try:
                    # 尝试解析JSON响应
                    response_data = response.json()
                    return response.status_code, response_data
                except json.JSONDecodeError:
                    # 如果响应不是JSON格式，返回原始文本
                    return response.status_code, {"message": response.text}
            else:
                # 对于非200状态码，返回错误信息
                return response.status_code, {"error": f"请求失败: {response.text}"}
            
        except Exception as e:
            logger.error(f"批量删除文档分段失败: {str(e)}")
            return 500, {"error": str(e)}
        
    async def enable_doc_segments(self, knowledge_uid: str, document_id: str, segment_ids: list[str]) -> Tuple[int, Any]:
        """
        批量启用文档分段
        Args:
            knowledge_uid: 知识库ID
            document_id: 文档ID
            segment_ids: 分段ID列表
            
        Returns:
            Tuple[int, Any]: (状态码, 响应数据)
        """
        try:
            # 构建基础API路径
            api_path = api.ENABLE_KNOWLEDGE_BASE_DOC_SEGMENTS.replace("{knowledge_uid}", knowledge_uid).replace("{document_id}", document_id)
            
            # 将segment_ids拼接成URL参数
            params = "&".join([f"segment_id={seg_id}" for seg_id in segment_ids])
            api_path = f"{api_path}?{params}"

            response = await self.requester.async_patch(api_path)
            
            # 检查响应状态码
            if response.status_code == 200:
                try:
                    # 尝试解析JSON响应
                    response_data = response.json()
                    return response.status_code, response_data
                except json.JSONDecodeError:
                    # 如果响应不是JSON格式，返回原始文本
                    return response.status_code, {"message": response.text}
            else:
                # 对于非200状态码，返回错误信息
                return response.status_code, {"error": f"请求失败: {response.text}"}
            
        except Exception as e:
            logger.error(f"批量启用文档分段失败: {str(e)}")
            return 500, {"error": str(e)}
            
    async def disable_doc_segments(self, knowledge_uid: str, document_id: str, segment_ids: list[str]) -> Tuple[int, Any]:
        """
        批量禁用文档分段
        Args:
            knowledge_uid: 知识库ID
            document_id: 文档ID
            segment_ids: 分段ID列表
            
        Returns:
            Tuple[int, Any]: (状态码, 响应数据)
        """
        try:
            # 构建基础API路径
            api_path = api.DISABLE_KNOWLEDGE_BASE_DOC_SEGMENTS.replace("{knowledge_uid}", knowledge_uid).replace("{document_id}", document_id)
            
            # 将segment_ids拼接成URL参数
            params = "&".join([f"segment_id={seg_id}" for seg_id in segment_ids])
            api_path = f"{api_path}?{params}"
            
            response = await self.requester.async_patch(api_path)
            
            # 检查响应状态码
            if response.status_code == 200:
                try:
                    # 尝试解析JSON响应
                    response_data = response.json()
                    return response.status_code, response_data
                except json.JSONDecodeError:
                    # 如果响应不是JSON格式，返回原始文本
                    return response.status_code, {"message": response.text}
            else:
                # 对于非200状态码，返回错误信息
                return response.status_code, {"error": f"请求失败: {response.text}"}
            
        except Exception as e:
            logger.error(f"批量禁用文档分段失败: {str(e)}")
            return 500, {"error": str(e)}

    async def delete_knowledge_docs(self, knowledge_uid: str, document_ids: list[str]) -> Tuple[int, Any]:
        """
        批量删除知识库文档
        Args:
            knowledge_uid: 知识库ID
            document_ids: 文档ID列表
            
        Returns:
            Tuple[int, Any]: (状态码, 响应数据)
        """
        try:
            # 构建基础API路径
            api_path = api.DELETE_KNOWLEDGE_BASE_DOCS.replace("{knowledge_uid}", knowledge_uid)
            
            # 将document_ids拼接成URL参数
            params = "&".join([f"document_id={doc_id}" for doc_id in document_ids])
            api_path = f"{api_path}?{params}"

            response = await self.requester.async_delete(api_path)
            
            # 检查响应状态码
            if response.status_code == 204:
                return response.status_code, {"message": "删除成功"}
            else:
                try:
                    response_data = response.json()
                except json.JSONDecodeError:
                    response_data = {"message": response.text}
                return response.status_code, response_data
            
        except Exception as e:
            logger.error(f"批量删除知识库文档失败: {str(e)}")
            return 500, {"error": str(e)}

    async def recall_test_history(self, knowledge_uid: str) -> Tuple[int, Any]:
        """
        召回历史接口
        
        Returns:
            Tuple[int, Any]: (状态码, 响应数据)
        """
        try:
            api_path = api.RECALL_TEST_HISTORY.replace("{knowledge_uid}", knowledge_uid)
            
            response = await self.requester.async_get(api_path)
            
            if response.status_code == 200:
                return 200, response.json()
            else:
                return response.status_code, response.json()
                
        except Exception as e:
            logger.error(f"获取召回历史失败: {str(e)}")
            return 500, {"error": str(e)}

    async def embed_text(self, knowledge_uid: str, data: dict) -> Tuple[int, Any]:
        """
        嵌入文本
        Args:
            data: 嵌入的数据
            knowledge_uid: 知识库ID
        Returns:
            Tuple[int, Any]: (状态码, 响应数据)
        """
        try:
            # 替换API路径中的参数
            api_path = api.EMBED_TEXT.replace("{knowledge_uid}", knowledge_uid)
            
            # 发送POST请求
            response = await self.requester.async_post(api_path, json=data)
            
            if response.status_code == 200:
                return 200, response.json()
            else:
                return response.status_code, response.json()
                
        except Exception as e:
            logger.error(f"文本嵌入失败: {str(e)}")
            return 500, {"error": str(e)}

    async def get_process_rule(self) -> Tuple[int, Any]:
        """
        获取知识库分段设置信息
        
        Returns:
            Tuple[int, Any]: (状态码, 响应数据)
        """
        try:
            # 调用API获取分段设置信息
            response = await self.requester.async_get(api.GET_KNOWLEDGE_BASE_DOC_SEGMENTS_SETTING)
            
            if response.status_code == 200:
                return 200, response.json()
            else:
                return response.status_code, response.json()
                
        except Exception as e:
            logger.error(f"获取知识库分段设置信息失败: {str(e)}")
            return 500, {"error": str(e)}

    async def upload_database_doc(self, knowledge_uid: str, file_path: str, file_name: str = None) -> Tuple[int, Any]:
        """
        上传知识库文档
        
        Args:
            knowledge_uid: 知识库ID
            file_path: 文件路径
            file_name: 文件名，如果不提供则使用文件路径中的文件名
            
        Returns:
            Tuple[int, Any]: (状态码, 响应数据)
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                logger.error(f"文件不存在: {file_path}")
                return 404, {"error": "文件不存在"}
            
            # 如果没有提供文件名，从文件路径中获取
            if not file_name:
                file_name = os.path.basename(file_path)
            
            # 替换API路径中的参数
            api_path = api.UPLOAD_KNOWLEDGE_BASE_DOC.replace("{knowledge_uid}", knowledge_uid)
            
            # 准备文件数据
            with open(file_path, 'rb') as f:
                files = {
                    'file': (file_name, f, 'application/octet-stream')
                }
                
                # 发送POST请求
                response = await self.requester.async_post(api_path, files=files)
                
                if response.status_code == 200:
                    return 200, response.json()
                else:
                    return response.status_code, response.json()
                    
        except Exception as e:
            logger.error(f"上传知识库文档失败: {str(e)}")
            return 500, {"error": str(e)}

    def account_active(self):
        """
        查询用户是否存在,True为账号存在，False为账号不存在
        """
        if self.requester._access_token():
            return True
        else:
            return False
        
    async def update_database_document_name(self, knowledge_uid: str, document_id: str, name: str) -> Tuple[int, Any]:
        """
        修改知识库文档名
        """
        try:
            api_path = api.UPDATE_KNOWLEDGE_BASE_DOC_NAME.replace("{knowledge_uid}", knowledge_uid).replace("{document_id}", document_id)
            response = await self.requester.async_post(api_path, json={"name": name})
            if response.status_code == 200:
                return 200, response.json()
            else:
                return response.status_code, response.json()
        except Exception as e:
            logger.error(f"修改知识库文档名失败: {str(e)}")

    async def get_database_segments(self, knowledge_uid: str, document_id: str, page: str = "1", limit: str = "10", keyword: str = "", enabled: str = "all"):
        """
        查询知识库文档分片
        Args:
            knowledge_uid: 知识库ID
            document_id: 文档ID
            page: 页码，默认1
            limit: 每页数量，默认10
            keyword: 搜索关键词，默认为空
            enabled: 是否启用，默认为all

        Returns:
            Tuple[int, Any]: (状态码, 响应数据)
        """
        try:
            # 构建查询参数
            params = {
                "page": page,
                "limit": limit,
                "keyword": keyword,
                "enabled": enabled
            }
            
            # 替换API路径中的参数
            api_url = api.GET_KNOWLEDGE_BASE_DOC_SEGMENTS.replace("{knowledge_uid}", knowledge_uid).replace("{document_id}", document_id)
            api_path = api_url + "?" + "&".join([f"{key}={value}" for key, value in params.items() if value])
            # 调用GET方法
            response = await self.requester.async_get(api_path, params=params)
            
            if response.status_code == 200:
                return 200, response.json()
            else:
                return response.status_code, response.json()
                
        except Exception as e:
            logger.error(f"查询知识库文档分片失败: {str(e)}")
            return 500, {"error": str(e)}

    async def get_database(self) -> Tuple[int, Any]:
        """
        查询知识库
        """
        try:
            response = await self.requester.async_get(api.GET_KNOWLEDGE_BASE, params={
                "include_all": True
            })
            return response.status_code, response.json()
        except Exception as e:
            logger.error(f"查询知识库失败: {str(e)}")
            return 500, []

    async def get_api_keys(self) -> BaseResponse:
        """
        获取api-key
        """
        try:
            url = api.GET_DIFY_API_KEY
            response = await self.requester.async_get(url=url)
            
            # 解析响应
            if response.status_code == 200:
                response_data = response.json()
                return BaseResponse(
                    code=200,
                    message="获取API密钥成功",
                    data=response_data
                )
            else:
                try:
                    response_data = response.json()
                except Exception:
                    response_data = {"error": "无法解析响应内容", "status": response.status_code}
                    
                return BaseResponse(
                    code=response.status_code,
                    message="获取API密钥失败",
                    data=response_data
                )
        except Exception as e:
            logger.error(f"获取API密钥失败: {str(e)}")
            return BaseResponse(
                code=500,
                message=f"获取API密钥失败: {str(e)}",
                data={}
            )

    async def edit_knowledge_base(self, data: Dict[str, Any]) -> BaseResponse:
        """
        编辑知识库
        """
        try:
            # 获取uid并从data中移除
            uid = data.pop("uid", None)
            if not uid:
                return BaseResponse(
                    code=400,
                    message="缺少知识库ID",
                    data={}
                )
                
            # 构建URL（确保使用正确的API路径）
            url = api.DELETE_KNOWLEDGE_BASE + uid
            
            # 准备请求体数据
            body = {
                "name": data.get("name"),
                "description": data.get("description", "该知识库还没添加描述"),
                "permission": data.get("permission", "only_me"),
            }

            response = await self.requester.async_patch(
                url=url,
                json=body
            )
            
            # 解析响应
            response_data = response.json()
            if response.status_code == 200:
                return BaseResponse(
                    code=200,
                    message="知识库编辑成功",
                    data=response_data
                )
            else:
                return BaseResponse(
                    code=response.status_code,
                    message="知识库编辑失败",
                    data=response_data
                )
        except Exception as e:
            logger.error(f"编辑知识库失败: {str(e)}")
            return BaseResponse(
                code=500,
                message=f"编辑知识库失败: {str(e)}",
                data={}
            )

    async def delete_knowledge_base(self, uid: str) -> BaseResponse:
        """
        根据id删除知识库
        """
        try:
            # 发送请求
            response = await self.requester.async_delete(
                url=api.DELETE_KNOWLEDGE_BASE + uid
            )

            # 检查响应状态码
            if response.status_code == 204:
                return BaseResponse(
                    code=200,
                    message="知识库删除成功",
                    data={}  # 返回空字典，因为没有内容
                )
            else:
                # 尝试解析响应内容
                try:
                    response_data = response.json()
                except ValueError:
                    response_data = {"error": "无法解析响应内容"}

                return BaseResponse(
                    code=400,
                    message="知识库删除失败",
                    data=response_data
                )
        except Exception as e:
            logger.error(f"删除知识库失败: {str(e)}")
            return BaseResponse(
                code=500,
                message=f"删除知识库失败: {str(e)}",
                data={}
            )

    async def create_knowledge_base(self, name: str) -> BaseResponse:
        """
        根据名称创建知识库
        """
        try:
            # 构建请求数据
            data = {"name": name}
            
            # 发送请求
            response = await self.requester.async_post(
                url=api.CREATE_KNOWLEDGE_BASE,
                json=data
            )
            
            # 解析响应
            response_data = response.json()
            if response_data.get("id"):
                return BaseResponse(
                    code=200,
                    message="知识库创建成功",
                    data=response_data
                )
            else:
                return BaseResponse(
                    code=400,
                    message="知识库创建失败",
                    data=response_data
                )
        except Exception as e:
            logger.error(f"创建知识库失败: {str(e)}")
            return BaseResponse(
                code=500,
                message=f"创建知识库失败: {str(e)}",
                data={}
            )

class DifyAdmin:
    """Dify管理员操作类"""
    
    def __init__(self):
        # 使用管理员配置初始化
        payload = AccessTokenPayload(
            email=DIFY_ADMINISTRATOR.get("email"),
            password=DIFY_ADMINISTRATOR.get("password")
        )
        self.client = DifyRequester(payload)

    async def get_rerank_model(self) -> Tuple[int, Any]:
        """
        获取rerank模型
        """
        try:
            api_path = api.GET_RERANK_MODEL
            response = await self.client.async_get(api_path)
            if response.status_code == 200:
                return 200, response.json()
            else:
                return response.status_code, response.json()

        except Exception as e:
            logger.error(f"获取rerank模型失败: {str(e)}")
            return 500, {"error": str(e)}

    async def get_embedding_model(self) -> Tuple[int, Any]:
        """
        获取rerank模型
        """
        try:
            api_path = api.GET_TXT_EMBEDDING_MODEL
            response = await self.client.async_get(api_path)
            if response.status_code == 200:
                return 200, response.json()
            else:
                return response.status_code, response.json()

        except Exception as e:
            logger.error(f"获取rerank模型失败: {str(e)}")
            return 500, {"error": str(e)}

    async def create_dify_account(self, email: str) -> BaseResponse:
        """
        创建Dify账号
        
        Args:
            email: 用户邮箱
            
        Returns:
            BaseResponse: 响应对象
        """
        try:
            # 构建请求数据
            data = {
                "emails": [email],
                "role": "editor",
                "language": "zh-Hans"
            }
            
            # 发送请求
            response = await self.client.async_post(
                url=api.INVITE_EMAIL,
                json=data
            )
            
            # 解析响应
            response_data = response.json()
            if response_data and response_data.get("result") == "success":
                return BaseResponse(
                    code=200,
                    message="创建Dify账号成功",
                    data=response_data
                )
            else:
                return BaseResponse(
                    code=400,
                    message="创建Dify账号失败",
                    data=response_data
                )
            
        except Exception as e:
            logger.error(f"创建Dify账号失败: {str(e)}")
            return BaseResponse(
                code=500,
                message=f"创建Dify账号失败: {str(e)}",
                data={"count": 0, "records": []}
            )

    def delete_dify_account(self, uuid):
        """
        根据id删除账号
        """
        response = asyncio.run(self.client.async_delete(api.DELETE_ACCOUNT + uuid))
        try:
            return response.status_code, response.json()
        except Exception as e:
            return response.status_code, []

