from typing import Dict, List, Any, Optional
from db.mysql.mysql_pool import MySQLPool
from logger import logging
import json


class MapInfoHistoryModel:
    def __init__(self):
        self.table = "map_info_history"
        self.db_pool = MySQLPool()
        self.logger = logging()
        
    async def initialize(self):
        """初始化数据库连接池"""
        await self.db_pool.initialize()
        
    async def close(self):
        """关闭连接池，释放资源"""
        await self.db_pool.close()
        
    async def delete_all(self) -> bool:
        """
        删除表中的所有数据
        
        Returns:
            bool: 删除是否成功
        """
        try:
            query = f"DELETE FROM {self.table}"
            async with self.db_pool.get_connection() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute(query)
                    await conn.commit()
                    self.logger.info(f"成功删除 {self.table} 表中的所有数据")
                    return True
        except Exception as e:
            self.logger.error(f"删除 map_info_history 表数据失败: {str(e)}")
            return False
            
    async def insert_all(self, data_list: List[Dict[str, Any]]) -> bool:
        """
        批量插入数据到表中
        
        Args:
            data_list: 要插入的数据列表，每个元素是一个字典，包含以下字段：
                - uid: 知识库UID
                - version: 版本号
                - version_desc: 版本描述（可选）
                
        Returns:
            bool: 插入是否成功
        """
        if not data_list:
            self.logger.warning("插入数据列表为空")
            return False
            
        try:
            # 构建插入语句
            query = """
                INSERT INTO map_info_history 
                (uid, version, version_desc) 
                VALUES (%(uid)s, %(version)s, %(version_desc)s)
            """
            
            # 准备数据
            values = []
            for data in data_list:
                values.append({
                    "uid": data.get("uid"),
                    "version": data.get("version"),
                    "version_desc": data.get("version_desc", "")
                })
                
            # 执行批量插入
            affected_rows = await self.db_pool.execute_many(query, values)
            self.logger.info(f"成功批量插入 {affected_rows} 条数据到 map_info_history 表")
            return affected_rows > 0
                    
        except Exception as e:
            self.logger.error(f"批量插入数据到 map_info_history 表失败: {str(e)}")
            return False
            
    async def get_all(self) -> List[Dict[str, Any]]:
        """
        获取表中的所有数据
        
        Returns:
            List[Dict[str, Any]]: 数据列表，每个元素包含所有字段
        """
        try:
            query = "SELECT * FROM map_info_history"
            affected_rows = await self.db_pool.execute(query)
            return affected_rows
                    
        except Exception as e:
            self.logger.error(f"获取 map_info_history 表数据失败: {str(e)}")
            return []
            
    async def get_history_by_uid(self, uid: str) -> List[Dict[str, Any]]:
        """
        获取指定知识库的历史版本记录
        
        Args:
            uid: 知识库UID
            
        Returns:
            List[Dict[str, Any]]: 历史版本记录列表，按创建时间倒序排列
        """
        try:
            query = """
                SELECT 
                    id,
                    uid,
                    version,
                    version_desc,
                    DATE_FORMAT(create_time, '%%Y-%%m-%%d %%H:%%i:%%s') as create_time
                FROM map_info_history 
                WHERE uid = %s 
                ORDER BY create_time DESC
            """
            result = await self.db_pool.fetch_all(query, (uid,))
            return result
                    
        except Exception as e:
            self.logger.error(f"获取知识库 {uid} 的历史版本记录失败: {str(e)}")
            return []
            
    async def delete_by_version(self, uid: str, version: str) -> bool:
        """
        删除指定版本的历史记录
        
        Args:
            uid: 知识库UID
            version: 版本号
            
        Returns:
            bool: 删除是否成功
        """
        try:
            query = """
                DELETE FROM map_info_history 
                WHERE uid = %s AND version = %s
            """
            affected_rows = await self.db_pool.execute(query, (uid, version))
            self.logger.info(f"成功删除知识库 {uid} 的版本 {version} 的历史记录")
            return affected_rows > 0
                    
        except Exception as e:
            self.logger.error(f"删除知识库 {uid} 的版本 {version} 的历史记录失败: {str(e)}")
            return False

    