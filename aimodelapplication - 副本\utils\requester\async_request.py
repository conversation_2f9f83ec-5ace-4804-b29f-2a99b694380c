import httpx


class AsyncRequester:

    def __init__(self, base_url=None):
        self.base_url = base_url
        self.client = httpx.AsyncClient()

    async def get(self, url, params=None, headers=None):
        """
        发送异步 GET 请求
        :param url: 请求的 URL
        :param params: 查询参数
        :param headers: 请求头
        :return: 响应对象
        """
        if self.base_url:
            url = f"{self.base_url}{url}"
        response = await self.client.get(url, params=params, headers=headers)
        return response

    async def post(self, url, data=None, json=None, files=None, headers=None):
        """
        发送异步 POST 请求
        :param url: 请求的 URL
        :param data: 表单数据
        :param json: JSON 数据
        :param files: 文件数据
        :param headers: 请求头
        :return: 响应对象
        """
        if self.base_url:
            url = f"{self.base_url}{url}"
        response = await self.client.post(url, data=data, json=json, files=files, headers=headers)
        return response

    async def put(self, url, data=None, json=None, headers=None):
        """
        发送异步 PUT 请求
        :param url: 请求的 URL
        :param data: 表单数据
        :param json: JSON 数据
        :param headers: 请求头
        :return: 响应对象
        """
        if self.base_url:
            url = f"{self.base_url}{url}"
        response = await self.client.put(url, data=data, json=json, headers=headers)
        return response

    async def delete(self, url, params=None, headers=None):
        """
        发送异步 DELETE 请求
        :param url: 请求的 URL
        :param params: 查询参数
        :param headers: 请求头
        :return: 响应对象
        """
        if self.base_url:
            url = f"{self.base_url}{url}"
        response = await self.client.delete(url, params=params, headers=headers)
        return response

    async def patch(self, url, data=None, json=None, headers=None):
        """
        发送异步 PATCH 请求
        :param url: 请求的 URL
        :param data: 表单数据
        :param json: JSON 数据
        :param headers: 请求头
        :return: 响应对象
        """
        if self.base_url:
            url = f"{self.base_url}{url}"
        response = await self.client.patch(url, data=data, json=json, headers=headers)
        return response

    async def close(self):
        """
        关闭异步客户端
        """
        await self.client.aclose()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """
        在退出上下文时正确关闭客户端
        """
        await self.close()

