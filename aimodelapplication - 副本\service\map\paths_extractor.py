# extract_endpoints.py
import json
import uuid
import os


class ExtractSwaggerPathEndpoints:

    def __init__(self, filepath):
        self.filepath = filepath
        self.document_name = os.path.basename(filepath)

    def extract_swagger_endpoints(self):
        """
        提取OpenAPI文档中paths下的API端点信息
        返回包含端点详情的字典列表
        """
        try:
            with open(self.filepath, 'r', encoding='utf-8') as f:
                swagger_data = json.load(f)

            endpoints = []

            for path, methods in swagger_data.get('paths', {}).items():
                for method, details in methods.items():
                    # 仅处理标准HTTP方法
                    if method.lower() not in ['get', 'put', 'post', 'delete']:
                        continue

                    # 准备端点数据
                    endpoint = {
                        "path": path,
                        "method": method.upper(),
                        "tags": details.get('tags', []),
                        "summary": details.get('summary', ''),
                        "parameters": details.get('parameters', []),
                        "request_body": details.get('requestBody', {}),
                        "responses": details.get('responses', {})
                    }

                    endpoints.append(endpoint)

            print(f"🟢 从 {self.filepath} 中提取了 {len(endpoints)} 个API端点")
            return endpoints

        except Exception as e:
            print(f"🔴 提取API端点失败: {str(e)}")
            return []