import os
from typing import Dict, List, Any, Optional, Union
from contextlib import asynccontextmanager
from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase
from logger import logging

# 加载环境变量
load_dotenv()

class Neo4jPool:
    """Neo4j连接池管理类（单例模式）"""
    _instance = None
    _drivers = {}  # 存储多个数据库驱动
    _logger = None
    
    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(Neo4jPool, cls).__new__(cls)
            # 初始化日志
            cls._instance._logger = logging()
        return cls._instance
    
    def __init__(self, host: str = None, port: int = None, user: str = None, password: str = None, db_name: str = None):
        """
        初始化Neo4j连接池
        
        Args:
            host: Neo4j服务器地址
            port: Neo4j服务器端口
            user: 用户名
            password: 密码
            db_name: 数据库名称
        """
        # 初始化实例变量
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.db_name = db_name
    
    async def initialize(self, host: str = None, port: int = None, user: str = None, password: str = None, db_name: str = None):
        """
        初始化Neo4j连接池
        
        Args:
            host: Neo4j服务器地址
            port: Neo4j服务器端口
            user: 用户名
            password: 密码
            db_name: 数据库名称
        """
        # 优先使用传入的参数，如果为空则使用构造函数中的参数，最后才使用环境变量
        self.host = host or self.host or os.getenv('NEO4J_HOST')
        self.port = port or self.port or os.getenv('NEO4J_PORT')
        self.user = user or self.user or os.getenv('NEO4J_USER')
        self.password = password or self.password or os.getenv('NEO4J_PASSWORD')
        self.db_name = db_name or self.db_name or os.getenv('NEO4J_DB_NAME')

        # 验证必要的配置是否存在
        if not all([self.host, self.port, self.user, self.password, self.db_name]):
            raise ValueError("Neo4j配置不完整，请检查参数或环境变量")
            
        # 构建连接URI
        self.uri = f"neo4j://{self.host}:{self.port}"
        
        # 创建驱动唯一标识
        driver_key = self.uri
        
        # 如果驱动已存在且已初始化，直接返回
        if driver_key in self._drivers and self._drivers[driver_key]["initialized"]:
            return
        
        try:
            # 创建新驱动
            driver = AsyncGraphDatabase.driver(
                self.uri,
                auth=(self.user, self.password),
                database=self.db_name
            )
            
            # 测试连接
            await driver.verify_connectivity()
            
            # 保存驱动信息
            self._drivers[driver_key] = {
                "driver": driver,
                "initialized": True,
                "config": {
                    "uri": self.uri,
                    "user": self.user,
                    "database": self.db_name
                }
            }
        except Exception as e:
            self._logger.error(f"Neo4j驱动初始化失败: {str(e)}")
            # 标记初始化失败
            if driver_key in self._drivers:
                self._drivers[driver_key]["initialized"] = False
            raise
    
    async def close(self, uri: str = None) -> None:
        """关闭驱动
        
        Args:
            uri: 指定要关闭的数据库驱动，None表示关闭所有驱动
        """
        if uri:
            # 关闭指定URI的驱动
            if uri in self._drivers and self._drivers[uri]["initialized"]:
                await self._drivers[uri]["driver"].close()
                self._drivers[uri]["initialized"] = False
        else:
            # 关闭所有驱动
            for key, driver_info in list(self._drivers.items()):
                if driver_info["initialized"]:
                    await driver_info["driver"].close()
                    driver_info["initialized"] = False
    
    async def _get_driver(self, uri: str = None) -> Any:
        """获取指定URI的驱动，如果不存在则初始化
        
        Args:
            uri: Neo4j服务器URI，None表示使用默认URI
            
        Returns:
            驱动对象
        """
        # 如果已经初始化过，直接使用已保存的URI
        if hasattr(self, 'uri'):
            uri = uri or self.uri
        else:
            # 从环境变量获取配置
            host = os.getenv('NEO4J_HOST')
            port = os.getenv('NEO4J_PORT')
            
            # 检查必要的配置
            if not all([host, port]):
                missing = []
                if not host: missing.append('NEO4J_HOST')
                if not port: missing.append('NEO4J_PORT')
                self._logger.error(f"缺少Neo4j配置: {', '.join(missing)}")
                raise ValueError(f"缺少Neo4j配置: {', '.join(missing)}")
            
            # 构建URI
            uri = uri or f"neo4j://{host}:{port}"
        
        # 查找匹配的驱动
        if uri in self._drivers and self._drivers[uri]["initialized"]:
            return self._drivers[uri]["driver"]
        
        # 没有找到匹配的驱动，初始化一个新的
        if hasattr(self, 'host') and hasattr(self, 'port'):
            await self.initialize(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                db_name=self.db_name
            )
        else:
            await self.initialize()
        
        # 再次查找
        if uri in self._drivers and self._drivers[uri]["initialized"]:
            return self._drivers[uri]["driver"]
        
        # 如果仍然找不到，抛出异常
        raise ValueError(f"无法创建Neo4j '{uri}' 的驱动")
    
    @asynccontextmanager
    async def session(self, uri: str = None) -> Any:
        """获取数据库会话
        
        Args:
            uri: Neo4j服务器URI，None表示使用默认URI
            
        Yields:
            数据库会话对象
        """
        driver = await self._get_driver(uri)
        session = driver.session()
        try:
            yield session
        finally:
            await session.close()
    
    async def execute_query(self, 
                          query: str, 
                          parameters: Dict[str, Any] = None, 
                          uri: str = None) -> List[Dict[str, Any]]:
        """执行Cypher查询
        
        Args:
            query: Cypher查询语句
            parameters: 查询参数
            uri: Neo4j服务器URI，None表示使用默认URI
            
        Returns:
            查询结果列表
        """
        async with self.session(uri) as session:
            try:
                result = await session.run(query, parameters)
                return await result.data()
            except Exception as e:
                self._logger.error(f"Cypher查询错误: {query}, 参数: {parameters}, 错误: {str(e)}")
                raise
    
    async def execute_write(self, 
                          query: str, 
                          parameters: Dict[str, Any] = None, 
                          uri: str = None) -> int:
        """执行写操作
        
        Args:
            query: Cypher查询语句
            parameters: 查询参数
            uri: Neo4j服务器URI，None表示使用默认URI
            
        Returns:
            影响的行数
        """
        async with self.session(uri) as session:
            try:
                result = await session.run(query, parameters)
                summary = await result.consume()
                return summary.counters.nodes_created + summary.counters.nodes_deleted + \
                       summary.counters.relationships_created + summary.counters.relationships_deleted
            except Exception as e:
                self._logger.error(f"Cypher写操作错误: {query}, 参数: {parameters}, 错误: {str(e)}")
                raise
    
    def get_driver_info(self) -> Dict[str, Dict]:
        """获取所有驱动信息
        
        Returns:
            驱动信息字典
        """
        result = {}
        for key, driver_info in self._drivers.items():
            result[key] = {
                "initialized": driver_info["initialized"],
                "config": driver_info["config"].copy()
            }
        return result
